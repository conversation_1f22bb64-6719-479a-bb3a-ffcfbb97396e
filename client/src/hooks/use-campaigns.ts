import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import type {
  Campaign,
  InsertCampaign,
  CampaignMedia,
  CampaignScreen,
  CampaignSlide,
  InsertCampaignSlide
} from "@shared/schema";

export function useCampaigns(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [selectedCampaign, setSelectedCampaign] = useState<string | null>(null);

  // Get all campaigns for the team
  const {
    data: campaigns,
    isLoading,
    error,
  } = useQuery<Campaign[]>({
    queryKey: [`/api/teams/${teamId}/campaigns`],
    enabled: !!teamId,
  });

  // Remove the excessive all-campaign-media query that was causing performance issues
  // Individual campaign data should be fetched only when needed

  // Remove the excessive all-campaign-screens query as well

  // Get a single campaign
  const useCampaign = (campaignId: string | null) => {
    return useQuery<Campaign>({
      queryKey: [`/api/campaigns/${campaignId}`],
      enabled: !!campaignId,
    });
  };

  // Get campaign media
  const useCampaignMedia = (campaignId: string | null) => {
    return useQuery<CampaignMedia[]>({
      queryKey: [`/api/campaigns/${campaignId}/media`],
      enabled: !!campaignId,
    });
  };

  // Get campaign screens
  const useCampaignScreens = (campaignId: string | null) => {
    return useQuery<CampaignScreen[]>({
      queryKey: [`/api/campaigns/${campaignId}/screens`],
      enabled: !!campaignId,
    });
  };

  // Get campaign slides
  const useCampaignSlides = (campaignId: string | null) => {
    return useQuery<CampaignSlide[]>({
      queryKey: [`/api/campaigns/${campaignId}/slides`],
      enabled: !!campaignId,
    });
  };

  // Create a new campaign
  const createCampaignMutation = useMutation({
    mutationFn: async (campaignData: Omit<InsertCampaign, "teamId">) => {
      const newCampaign: InsertCampaign = {
        ...campaignData,
        teamId,
      };
      return apiRequest('POST', `/api/campaigns`, newCampaign);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/campaigns`] });
      toast({
        title: "Campaign created",
        description: "The campaign has been created successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating campaign",
        description: error.message || "An error occurred while creating the campaign",
        variant: "destructive"
      });
    },
  });

  // Update a campaign
  const updateCampaignMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<InsertCampaign> }) => {
      return apiRequest('PATCH', `/api/campaigns/${id}`, data);
    },
    onSuccess: (_, variables) => {
      // Invalidate the general campaigns list
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/campaigns`] });

      // Also invalidate this specific campaign's data to ensure fresh data on reopen
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.id}`] });

      // Invalidate campaign media and screens
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.id}/media`] });
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.id}/screens`] });

      toast({
        title: "Campaign updated",
        description: "The campaign has been updated successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating campaign",
        description: error.message || "An error occurred while updating the campaign",
        variant: "destructive"
      });
    },
  });

  // Delete a campaign
  const deleteCampaignMutation = useMutation({
    mutationFn: async (campaignId: string) => {
      await apiRequest('DELETE', `/api/campaigns/${campaignId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/campaigns`] });
      toast({
        title: "Campaign deleted",
        description: "The campaign has been deleted successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting campaign",
        description: error.message || "An error occurred while deleting the campaign",
        variant: "destructive"
      });
    },
  });

  // Add media to campaign
  const addMediaToCampaignMutation = useMutation({
    mutationFn: async ({
      campaignId,
      mediaId,
      order,
      campaignType = 0
    }: {
      campaignId: string;
      mediaId: string;
      order: number;
      campaignType?: number;
    }) => {
      return apiRequest('POST', `/api/campaign-media`, {
        campaignId,
        mediaId,
        displayOrder: order,
        campaignType,
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate campaign media list
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/media`] });
      // Also invalidate the specific campaign to ensure it updates with fresh media count
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Media added",
        description: "The media has been added to the campaign"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error adding media",
        description: error.message || "An error occurred while adding media to the campaign",
        variant: "destructive"
      });
    },
  });

  // Remove media from campaign
  const removeMediaFromCampaignMutation = useMutation({
    mutationFn: async ({ campaignId, mediaId }: { campaignId: string; mediaId: string }) => {
      return apiRequest('DELETE', `/api/campaign-media/${campaignId}/${mediaId}`);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/media`] });
      // Also invalidate the specific campaign
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Media removed",
        description: "The media has been removed from the campaign"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error removing media",
        description: error.message || "An error occurred while removing media from the campaign",
        variant: "destructive"
      });
    },
  });

  // Add screen to campaign
  const addScreenToCampaignMutation = useMutation({
    mutationFn: async ({ campaignId, screenId }: { campaignId: string; screenId: string }) => {
      return apiRequest('POST', `/api/campaign-screens`, {
        campaignId,
        screenId,
      });
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/screens`] });
      // Also invalidate the specific campaign
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Screen added",
        description: "The screen has been added to the campaign"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error adding screen",
        description: error.message || "An error occurred while adding screen to the campaign",
        variant: "destructive"
      });
    },
  });

  // Remove screen from campaign
  const removeScreenFromCampaignMutation = useMutation({
    mutationFn: async ({ campaignId, screenId }: { campaignId: string; screenId: string }) => {
      return apiRequest('DELETE', `/api/campaign-screens/${campaignId}/${screenId}`);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/screens`] });
      // Also invalidate the specific campaign
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Screen removed",
        description: "The screen has been removed from the campaign"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error removing screen",
        description: error.message || "An error occurred while removing screen from the campaign",
        variant: "destructive"
      });
    },
  });

  // Add slide to campaign - Using the campaign-media endpoint with campaignType=1
  const addSlideToCampaignMutation = useMutation({
    mutationFn: async ({
      campaignId,
      slideId,
      order
    }: {
      campaignId: string;
      slideId: string;
      order: number;
    }) => {
      // Use the campaign-media endpoint for slides with campaignType=1
      return apiRequest('POST', `/api/campaign-media`, {
        campaignId,
        mediaId: slideId,  // Slide ID goes in the mediaId field
        displayOrder: order,
        campaignType: 1,   // 1 indicates this is a slide
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate both slides and media queries since they're in the same table
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/slides`] });
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/media`] });
      // Also invalidate the specific campaign
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Slide added",
        description: "The slide has been added to the campaign"
      });
    },
    onError: (error: any) => {
      console.error("Error adding slide to campaign:", error);
      toast({
        title: "Error adding slide",
        description: error.message || "An error occurred while adding slide to the campaign",
        variant: "destructive"
      });
    },
  });

  // Remove slide from campaign - Using the campaign-media endpoint
  const removeSlideFromCampaignMutation = useMutation({
    mutationFn: async ({ campaignId, slideId }: { campaignId: string; slideId: string }) => {
      // Use the campaign-media endpoint to delete slides
      return apiRequest('DELETE', `/api/campaign-media/${campaignId}/${slideId}`);
    },
    onSuccess: (_, variables) => {
      // Invalidate both slides and media queries
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/slides`] });
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}/media`] });
      // Also invalidate the specific campaign
      queryClient.invalidateQueries({ queryKey: [`/api/campaigns/${variables.campaignId}`] });
      toast({
        title: "Slide removed",
        description: "The slide has been removed from the campaign"
      });
    },
    onError: (error: any) => {
      console.error("Error removing slide from campaign:", error);
      toast({
        title: "Error removing slide",
        description: error.message || "An error occurred while removing slide from the campaign",
        variant: "destructive"
      });
    },
  });

  // Simplified empty state logic
  const isEmpty = !isLoading && (!campaigns || campaigns.length === 0);

  return {
    campaigns,
    isLoading,
    isEmpty,
    error,
    selectedCampaign,
    setSelectedCampaign,
    useCampaign,
    useCampaignMedia,
    useCampaignScreens,
    useCampaignSlides,
    createCampaign: createCampaignMutation.mutate,
    isCreating: createCampaignMutation.isPending,
    updateCampaign: updateCampaignMutation.mutate,
    isUpdating: updateCampaignMutation.isPending,
    deleteCampaign: deleteCampaignMutation.mutate,
    isDeleting: deleteCampaignMutation.isPending,
    // Media methods
    addMediaToCampaign: addMediaToCampaignMutation.mutate,
    isAddingMedia: addMediaToCampaignMutation.isPending,
    removeMediaFromCampaign: removeMediaFromCampaignMutation.mutate,
    isRemovingMedia: removeMediaFromCampaignMutation.isPending,
    // Screen methods
    addScreenToCampaign: addScreenToCampaignMutation.mutate,
    isAddingScreen: addScreenToCampaignMutation.isPending,
    removeScreenFromCampaign: removeScreenFromCampaignMutation.mutate,
    isRemovingScreen: removeScreenFromCampaignMutation.isPending,
    // Slide methods
    addSlideToCampaign: addSlideToCampaignMutation.mutate,
    isAddingSlide: addSlideToCampaignMutation.isPending,
    removeSlideFromCampaign: removeSlideFromCampaignMutation.mutate,
    isRemovingSlide: removeSlideFromCampaignMutation.isPending,
  };
}
