import {
  profiles, teams, teamMembers, mediaItems, screens, campaigns,
  campaignMedias, campaignScreens, campaignSlides, slides, tags, mediaTags, screenTags,
  invoices, pricing, screenRegistrations,
  type Profile, type InsertProfile,
  type Team, type InsertTeam,
  type TeamMember, type InsertTeamMember,
  type MediaItem, type InsertMediaItem,
  type Screen, type InsertScreen,
  type Campaign, type InsertCampaign,
  type CampaignMedia, type InsertCampaignMedia,
  type CampaignScreen, type InsertCampaignScreen,
  type CampaignSlide, type InsertCampaignSlide,
  type Slide, type InsertSlide,
  type Tag, type InsertTag,
  type MediaTag, type InsertMediaTag,
  type ScreenTag, type InsertScreenTag,
  type Invoice, type InsertInvoice,
  type Pricing, type InsertPricing,
  type ScreenRegistration, type InsertScreenRegistration
} from "@shared/schema";

export interface IStorage {
  // Profiles
  getProfile(id: string): Promise<Profile | undefined>;
  getProfileByEmail(email: string): Promise<Profile | undefined>;
  createProfile(profile: InsertProfile): Promise<Profile>;
  updateProfile(id: string, profile: Partial<InsertProfile>): Promise<Profile | undefined>;

  // Teams
  getTeam(id: string): Promise<Team | undefined>;
  getTeamsByProfileId(profileId: string): Promise<Team[]>;
  createTeam(team: InsertTeam): Promise<Team>;
  updateTeam(id: string, team: Partial<InsertTeam>): Promise<Team | undefined>;

  // Team Members
  getTeamMembers(teamId: string): Promise<TeamMember[]>;
  createTeamMember(teamMember: InsertTeamMember): Promise<TeamMember>;
  removeTeamMember(profileId: string, teamId: string): Promise<boolean>;

  // Media Items
  getMediaItem(id: string): Promise<MediaItem | undefined>;
  getMediaItemsByTeamId(teamId: string): Promise<MediaItem[]>;
  getMediaItemsByTagId(tagId: string): Promise<MediaItem[]>;
  createMediaItem(mediaItem: InsertMediaItem): Promise<MediaItem>;
  updateMediaItem(id: string, mediaItem: Partial<InsertMediaItem>): Promise<MediaItem | undefined>;
  deleteMediaItem(id: string): Promise<boolean>;

  // Screens
  getScreen(id: string): Promise<Screen | undefined>;
  getScreensByTeamId(teamId: string): Promise<Screen[]>;
  getScreensByTagId(tagId: string): Promise<Screen[]>;
  getScreenByCode(code: string): Promise<Screen | undefined>;
  createScreen(screen: InsertScreen): Promise<Screen>;
  updateScreen(id: string, screen: Partial<InsertScreen>): Promise<Screen | undefined>;
  deleteScreen(id: string): Promise<boolean>;

  // Campaigns
  getCampaign(id: string): Promise<Campaign | undefined>;
  getCampaignsByTeamId(teamId: string): Promise<Campaign[]>;
  createCampaign(campaign: InsertCampaign): Promise<Campaign>;
  updateCampaign(id: string, campaign: Partial<InsertCampaign>): Promise<Campaign | undefined>;
  deleteCampaign(id: string): Promise<boolean>;

  // Campaign Media
  getCampaignMedias(campaignId: string): Promise<CampaignMedia[]>;
  getCampaignMediaItems(campaignId: string): Promise<any[]>;
  createCampaignMedia(campaignMedia: InsertCampaignMedia): Promise<CampaignMedia>;
  deleteCampaignMedia(campaignId: string, mediaId: string): Promise<boolean>;
  getCampaignSummary(campaignId: string, mediaId: string | null, teamId: string, startDate: string, endDate: string): Promise<any>;

  // Campaign Slides
  getCampaignSlides(campaignId: string): Promise<CampaignSlide[]>;
  createCampaignSlide(campaignSlide: InsertCampaignSlide): Promise<CampaignSlide>;
  deleteCampaignSlide(campaignId: string, slideId: string): Promise<boolean>;

  // Campaign Screens
  getCampaignScreens(campaignId: string): Promise<CampaignScreen[]>;
  createCampaignScreen(campaignScreen: InsertCampaignScreen): Promise<CampaignScreen>;
  deleteCampaignScreen(campaignId: string, screenId: string): Promise<boolean>;

  // Slides
  getSlide(id: string): Promise<Slide | undefined>;
  getSlidesByTeamId(teamId: string): Promise<Slide[]>;
  createSlide(slide: InsertSlide): Promise<Slide>;
  updateSlide(id: string, slide: Partial<InsertSlide>): Promise<Slide | undefined>;
  deleteSlide(id: string): Promise<boolean>;

  // Tags
  getTag(id: string): Promise<Tag | undefined>;
  getTagsByTeamId(teamId: string): Promise<Tag[]>;
  getTagsByName(teamId: string, name: string): Promise<Tag | undefined>;
  createTag(tag: InsertTag): Promise<Tag>;
  updateTag(id: string, tag: Partial<InsertTag>): Promise<Tag | undefined>;
  deleteTag(id: string): Promise<boolean>;

  // Media Tags
  getMediaTags(mediaId: string): Promise<Tag[]>;
  addMediaTag(mediaId: string, tagId: string): Promise<MediaTag>;
  removeMediaTag(mediaId: string, tagId: string): Promise<boolean>;

  // Screen Tags
  getScreenTags(screenId: string): Promise<Tag[]>;
  addScreenTag(screenId: string, tagId: string): Promise<ScreenTag>;
  removeScreenTag(screenId: string, tagId: string): Promise<boolean>;

  // Billing
  getPricing(): Promise<Pricing[]>;
  getBillingSummary(teamId: string): Promise<{
    totalCreditsBought: number;
    totalCreditsUsed: number;
    creditsAvailable: number;
  }>;
  getInvoicesByTeamId(teamId: string): Promise<Invoice[]>;
  createInvoice(invoice: InsertInvoice): Promise<Invoice>;
  updateInvoicePayment(invoiceId: string, paidAt: Date | null): Promise<Invoice | undefined>;
  deleteInvoice(invoiceId: string): Promise<boolean>;
  getScreenSubscriptionsByTeamId(teamId: string): Promise<any[]>;
  getAvailableScreensForSubscription(teamId: string): Promise<any[]>;
  bulkUpdateScreenRegistrations(updates: {
    screenId: string;
    trialEndsAt: Date;
    subscriptionStatus: string;
    billingCycle: string;
    invoiceId: number;
  }[]): Promise<boolean>;

  // Campaign Log Summary for Content Engagement
  getCampaignLogSummary(teamId: string, timeRange: 'week' | 'month' | 'year'): Promise<any[]>;

  // Campaign Summary Timeseries for Content Engagement (new RPC function)
  getCampaignSummaryTimeseries(teamId: string, startDate: string, endDate: string, option: 'week' | 'month' | 'year'): Promise<any[]>;
}

export class MemStorage implements IStorage {
  private profiles: Map<string, Profile>;
  private teams: Map<string, Team>;
  private teamMembers: Map<string, TeamMember[]>;
  private mediaItems: Map<string, MediaItem>;
  private screens: Map<string, Screen>;
  private campaigns: Map<string, Campaign>;
  private campaignMedias: Map<string, CampaignMedia[]>;
  private campaignSlides: Map<string, CampaignSlide[]>;
  private campaignScreens: Map<string, CampaignScreen[]>;
  private slides: Map<string, Slide>;
  private tags: Map<string, Tag>;
  private mediaTags: Map<string, MediaTag[]>;
  private screenTags: Map<string, ScreenTag[]>;

  constructor() {
    this.profiles = new Map();
    this.teams = new Map();
    this.teamMembers = new Map();
    this.mediaItems = new Map();
    this.screens = new Map();
    this.campaigns = new Map();
    this.campaignMedias = new Map();
    this.campaignSlides = new Map();
    this.campaignScreens = new Map();
    this.slides = new Map();
    this.tags = new Map();
    this.mediaTags = new Map();
    this.screenTags = new Map();
  }

  // Profiles
  async getProfile(id: string): Promise<Profile | undefined> {
    return this.profiles.get(id);
  }

  async getProfileByEmail(email: string): Promise<Profile | undefined> {
    return Array.from(this.profiles.values()).find(profile => profile.email === email);
  }

  async createProfile(profile: InsertProfile): Promise<Profile> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newProfile: Profile = {
      ...profile,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.profiles.set(id, newProfile);
    return newProfile;
  }

  async updateProfile(id: string, profile: Partial<InsertProfile>): Promise<Profile | undefined> {
    const existingProfile = this.profiles.get(id);
    if (!existingProfile) return undefined;

    const updatedProfile: Profile = {
      ...existingProfile,
      ...profile,
      updatedAt: new Date()
    };
    this.profiles.set(id, updatedProfile);
    return updatedProfile;
  }

  // Teams
  async getTeam(id: string): Promise<Team | undefined> {
    return this.teams.get(id);
  }

  async getTeamsByProfileId(profileId: string): Promise<Team[]> {
    const teamIds = Array.from(this.teamMembers.values())
      .flat()
      .filter(member => member.profileId === profileId)
      .map(member => member.teamId);

    return teamIds.map(id => this.teams.get(id)).filter(Boolean) as Team[];
  }

  async createTeam(team: InsertTeam): Promise<Team> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newTeam: Team = {
      ...team,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.teams.set(id, newTeam);
    return newTeam;
  }

  async updateTeam(id: string, team: Partial<InsertTeam>): Promise<Team | undefined> {
    const existingTeam = this.teams.get(id);
    if (!existingTeam) return undefined;

    const updatedTeam: Team = {
      ...existingTeam,
      ...team,
      updatedAt: new Date()
    };
    this.teams.set(id, updatedTeam);
    return updatedTeam;
  }

  // Team Members
  async getTeamMembers(teamId: string): Promise<TeamMember[]> {
    return Array.from(this.teamMembers.values())
      .flat()
      .filter(member => member.teamId === teamId);
  }

  async createTeamMember(teamMember: InsertTeamMember): Promise<TeamMember> {
    const newMember: TeamMember = {
      ...teamMember,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const members = this.teamMembers.get(teamMember.teamId) || [];
    members.push(newMember);
    this.teamMembers.set(teamMember.teamId, members);

    return newMember;
  }

  async removeTeamMember(profileId: string, teamId: string): Promise<boolean> {
    const members = this.teamMembers.get(teamId);
    if (!members) return false;

    const newMembers = members.filter(m => m.profileId !== profileId);
    this.teamMembers.set(teamId, newMembers);

    return members.length !== newMembers.length;
  }

  // Media Items
  async getMediaItem(id: string): Promise<MediaItem | undefined> {
    return this.mediaItems.get(id);
  }

  async getMediaItemsByTeamId(teamId: string): Promise<MediaItem[]> {
    return Array.from(this.mediaItems.values())
      .filter(item => item.teamId === teamId);
  }

  async getMediaItemsByTagId(tagId: string): Promise<MediaItem[]> {
    // Get all media tags with the given tag ID
    const mediaTagsWithTag = Array.from(this.mediaTags.values())
      .flat()
      .filter(tag => tag.tagId === tagId);

    // Map to array of media IDs
    const mediaIds = mediaTagsWithTag.map(tag => tag.mediaId);

    // Return media items with those IDs
    return Array.from(this.mediaItems.values())
      .filter(item => mediaIds.includes(item.id));
  }

  async createMediaItem(mediaItem: InsertMediaItem): Promise<MediaItem> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newMediaItem: MediaItem = {
      ...mediaItem,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.mediaItems.set(id, newMediaItem);
    return newMediaItem;
  }

  async updateMediaItem(id: string, mediaItem: Partial<InsertMediaItem>): Promise<MediaItem | undefined> {
    const existingMediaItem = this.mediaItems.get(id);
    if (!existingMediaItem) return undefined;

    const updatedMediaItem: MediaItem = {
      ...existingMediaItem,
      ...mediaItem,
      updatedAt: new Date()
    };
    this.mediaItems.set(id, updatedMediaItem);
    return updatedMediaItem;
  }

  async deleteMediaItem(id: string): Promise<boolean> {
    return this.mediaItems.delete(id);
  }

  // Screens
  async getScreen(id: string): Promise<Screen | undefined> {
    return this.screens.get(id);
  }

  async getScreensByTeamId(teamId: string): Promise<Screen[]> {
    return Array.from(this.screens.values())
      .filter(screen => screen.teamId === teamId);
  }

  async getScreensByTagId(tagId: string): Promise<Screen[]> {
    // Get all screen tags with the given tag ID
    const screenTagsWithTag = Array.from(this.screenTags.values())
      .flat()
      .filter(tag => tag.tagId === tagId);

    // Map to array of screen IDs
    const screenIds = screenTagsWithTag.map(tag => tag.screenId);

    // Return screen items with those IDs
    return Array.from(this.screens.values())
      .filter(screen => screenIds.includes(screen.id));
  }

  async getScreenByCode(code: string): Promise<Screen | undefined> {
    return Array.from(this.screens.values())
      .find(screen => screen.code === code);
  }

  async createScreen(screen: InsertScreen): Promise<Screen> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newScreen: Screen = {
      ...screen,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.screens.set(id, newScreen);
    return newScreen;
  }

  async updateScreen(id: string, screen: Partial<InsertScreen>): Promise<Screen | undefined> {
    const existingScreen = this.screens.get(id);
    if (!existingScreen) return undefined;

    const updatedScreen: Screen = {
      ...existingScreen,
      ...screen,
      updatedAt: new Date()
    };
    this.screens.set(id, updatedScreen);
    return updatedScreen;
  }

  async deleteScreen(id: string): Promise<boolean> {
    return this.screens.delete(id);
  }

  // Campaigns
  async getCampaign(id: string): Promise<Campaign | undefined> {
    return this.campaigns.get(id);
  }

  async getCampaignsByTeamId(teamId: string): Promise<Campaign[]> {
    return Array.from(this.campaigns.values())
      .filter(campaign => campaign.teamId === teamId);
  }

  async createCampaign(campaign: InsertCampaign): Promise<Campaign> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newCampaign: Campaign = {
      ...campaign,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.campaigns.set(id, newCampaign);
    return newCampaign;
  }

  async updateCampaign(id: string, campaign: Partial<InsertCampaign>): Promise<Campaign | undefined> {
    const existingCampaign = this.campaigns.get(id);
    if (!existingCampaign) return undefined;

    const updatedCampaign: Campaign = {
      ...existingCampaign,
      ...campaign,
      updatedAt: new Date()
    };
    this.campaigns.set(id, updatedCampaign);
    return updatedCampaign;
  }

  async deleteCampaign(id: string): Promise<boolean> {
    return this.campaigns.delete(id);
  }

  // Campaign Media
  async getCampaignMedias(campaignId: string): Promise<CampaignMedia[]> {
    const { data, error } = await supabase
      .from('campaign_medias')
      .select('*')
      .eq('campaign_id', campaignId);

    if (error || !data) return [];

    return data.map(item => ({
      campaignId: item.campaign_id,
      mediaId: item.media_id,
      displayOrder: item.display_order,
      campaignType: item.campaign_type,
      createdAt: new Date(item.created_at)
    }));
  }

  async createCampaignMedia(campaignMedia: InsertCampaignMedia): Promise<CampaignMedia> {
    console.log("Creating campaign media with detailed info:", {
      campaignId: campaignMedia.campaignId,
      mediaId: campaignMedia.mediaId,
      displayOrder: campaignMedia.displayOrder,
      campaignType: campaignMedia.campaignType,
    });

    try {
      const { data, error } = await supabase
        .from('campaign_medias')
        .insert([{
          campaign_id: campaignMedia.campaignId,
          media_id: campaignMedia.mediaId,
          display_order: campaignMedia.displayOrder,
          campaign_type: campaignMedia.campaignType,
        }])
        .select()
        .single();

      if (error) {
        console.error("Error creating campaign media:", error);
        throw error;
      }

      console.log("Campaign media created successfully:", {
        campaign_id: data.campaign_id,
        media_id: data.media_id,
        display_order: data.display_order,
        campaign_type: data.campaign_type,
      });

      return {
        campaignId: data.campaign_id,
        mediaId: data.media_id,
        displayOrder: data.display_order,
        campaignType: data.campaign_type,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error("Caught exception in createCampaignMedia:", error);
      throw error;
    }
  }

  async deleteCampaignMedia(campaignId: string, mediaId: string): Promise<boolean> {
    console.log(`Deleting campaign media: campaignId=${campaignId}, mediaId=${mediaId}`);

    const { error } = await supabase
      .from('campaign_medias')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('media_id', mediaId);

    if (error) {
      console.error("Error deleting campaign media:", error);
      return false;
    }

    console.log(`Successfully deleted campaign media: campaignId=${campaignId}, mediaId=${mediaId}`);
    return true;
  }

  // Campaign Slides
  async getCampaignSlides(campaignId: string): Promise<CampaignSlide[]> {
    console.log(`Getting campaign slides for campaignId=${campaignId}`);

    const { data, error } = await supabase
      .from('campaign_medias')
      .select('*')
      .eq('campaign_id', campaignId)
      .eq('campaign_type', 1); // 1 = slide

    if (error || !data) {
      console.error("Error getting campaign slides:", error);
      return [];
    }

    console.log(`Found ${data.length} slides for campaignId=${campaignId}`);

    return data.map(item => ({
      campaignId: item.campaign_id,
      slideId: item.media_id, // In campaign_medias table, media_id column holds the slide ID
      displayOrder: item.display_order,
      campaignType: item.campaign_type,
      createdAt: new Date(item.created_at)
    }));
  }

  async createCampaignSlide(campaignSlide: InsertCampaignSlide): Promise<CampaignSlide> {
    console.log("Creating campaign slide with detailed info:", {
      campaignId: campaignSlide.campaignId,
      slideId: campaignSlide.slideId,
      displayOrder: campaignSlide.displayOrder,
    });

    try {
      const { data, error } = await supabase
        .from('campaign_medias')
        .insert([{
          campaign_id: campaignSlide.campaignId,
          media_id: campaignSlide.slideId,
          display_order: campaignSlide.displayOrder,
          campaign_type: 1, // 1 = slide
        }])
        .select()
        .single();

      if (error) {
        console.error("Error creating campaign slide:", error);
        throw error;
      }

      console.log("Campaign slide created successfully:", {
        campaign_id: data.campaign_id,
        media_id: data.media_id,
        display_order: data.display_order,
        campaign_type: data.campaign_type,
      });

      return {
        campaignId: data.campaign_id,
        slideId: data.media_id,
        displayOrder: data.display_order,
        campaignType: data.campaign_type,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error("Caught exception in createCampaignSlide:", error);
      throw error;
    }
  }

  async deleteCampaignSlide(campaignId: string, slideId: string): Promise<boolean> {
    console.log(`Deleting campaign slide: campaignId=${campaignId}, slideId=${slideId}`);

    const { error } = await supabase
      .from('campaign_medias')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('media_id', slideId)
      .eq('campaign_type', 1); // 1 = slide

    if (error) {
      console.error("Error deleting campaign slide:", error);
      return false;
    }

    console.log(`Successfully deleted campaign slide: campaignId=${campaignId}, slideId=${slideId}`);
    return true;
  }

  // Campaign Screens
  async getCampaignScreens(campaignId: string): Promise<CampaignScreen[]> {
    console.log(`Getting campaign screens for campaignId=${campaignId}`);

    const { data, error } = await supabase
      .from('campaign_screens')
      .select('*')
      .eq('campaign_id', campaignId);

    if (error || !data) {
      console.error("Error getting campaign screens:", error);
      return [];
    }

    console.log(`Found ${data.length} screens for campaignId=${campaignId}`);

    return data.map(item => ({
      campaignId: item.campaign_id,
      screenId: item.screen_id,
      createdAt: new Date(item.created_at)
    }));
  }

  async createCampaignScreen(campaignScreen: InsertCampaignScreen): Promise<CampaignScreen> {
    console.log("Creating campaign screen with detailed info:", {
      campaignId: campaignScreen.campaignId,
      screenId: campaignScreen.screenId,
    });

    try {
      const { data, error } = await supabase
        .from('campaign_screens')
        .insert([{
          campaign_id: campaignScreen.campaignId,
          screen_id: campaignScreen.screenId,
        }])
        .select()
        .single();

      if (error) {
        console.error("Error creating campaign screen:", error);
        throw error;
      }

      console.log("Campaign screen created successfully:", {
        campaign_id: data.campaign_id,
        screen_id: data.screen_id,
      });

      return {
        campaignId: data.campaign_id,
        screenId: data.screen_id,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error("Caught exception in createCampaignScreen:", error);
      throw error;
    }
  }

  async deleteCampaignScreen(campaignId: string, screenId: string): Promise<boolean> {
    console.log(`Deleting campaign screen: campaignId=${campaignId}, screenId=${screenId}`);

    const { error } = await supabase
      .from('campaign_screens')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('screen_id', screenId);

    if (error) {
      console.error("Error deleting campaign screen:", error);
      return false;
    }

    console.log(`Successfully deleted campaign screen: campaignId=${campaignId}, screenId=${screenId}`);
    return true;
  }

  // Slides
  async getSlide(id: string): Promise<Slide | undefined> {
    return this.slides.get(id);
  }

  async getSlidesByTeamId(teamId: string): Promise<Slide[]> {
    return Array.from(this.slides.values())
      .filter(slide => slide.teamId === teamId);
  }

  async createSlide(slide: InsertSlide): Promise<Slide> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newSlide: Slide = {
      ...slide,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.slides.set(id, newSlide);
    return newSlide;
  }

  async updateSlide(id: string, slide: Partial<InsertSlide>): Promise<Slide | undefined> {
    const existingSlide = this.slides.get(id);
    if (!existingSlide) return undefined;

    const updatedSlide: Slide = {
      ...existingSlide,
      ...slide,
      updatedAt: new Date()
    };
    this.slides.set(id, updatedSlide);
    return updatedSlide;
  }

  async deleteSlide(id: string): Promise<boolean> {
    return this.slides.delete(id);
  }

  // Tags
  async getTag(id: string): Promise<Tag | undefined> {
    return this.tags.get(id);
  }

  async getTagsByTeamId(teamId: string): Promise<Tag[]> {
    return Array.from(this.tags.values())
      .filter(tag => tag.teamId === teamId);
  }

  async getTagsByName(teamId: string, name: string): Promise<Tag | undefined> {
    return Array.from(this.tags.values())
      .find(tag => tag.teamId === teamId && tag.name.toLowerCase() === name.toLowerCase());
  }

  async createTag(tag: InsertTag): Promise<Tag> {
    const id = crypto.randomUUID();
    const now = new Date();
    const newTag: Tag = {
      ...tag,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.tags.set(id, newTag);
    return newTag;
  }

  async updateTag(id: string, tag: Partial<InsertTag>): Promise<Tag | undefined> {
    const existingTag = this.tags.get(id);
    if (!existingTag) return undefined;

    const updatedTag: Tag = {
      ...existingTag,
      ...tag,
      updatedAt: new Date()
    };
    this.tags.set(id, updatedTag);
    return updatedTag;
  }

  async deleteTag(id: string): Promise<boolean> {
    return this.tags.delete(id);
  }

  // Media Tags
  async getMediaTags(mediaId: string): Promise<Tag[]> {
    const mediaTags = this.mediaTags.get(mediaId) || [];
    return mediaTags.map(mediaTag => this.tags.get(mediaTag.tagId)).filter(Boolean) as Tag[];
  }

  async addMediaTag(mediaId: string, tagId: string): Promise<MediaTag> {
    // Check if the media and tag exist
    const media = this.mediaItems.get(mediaId);
    const tag = this.tags.get(tagId);
    if (!media || !tag) {
      throw new Error("Media or tag not found");
    }

    const newMediaTag: MediaTag = {
      mediaId,
      tagId,
      createdAt: new Date()
    };

    const mediaTags = this.mediaTags.get(mediaId) || [];
    // Check if the tag is already assigned
    if (!mediaTags.some(mt => mt.tagId === tagId)) {
      mediaTags.push(newMediaTag);
      this.mediaTags.set(mediaId, mediaTags);
    }

    return newMediaTag;
  }

  async removeMediaTag(mediaId: string, tagId: string): Promise<boolean> {
    const mediaTags = this.mediaTags.get(mediaId);
    if (!mediaTags) return false;

    const newMediaTags = mediaTags.filter(mt => mt.tagId !== tagId);
    this.mediaTags.set(mediaId, newMediaTags);

    return mediaTags.length !== newMediaTags.length;
  }

  // Screen Tags
  async getScreenTags(screenId: string): Promise<Tag[]> {
    const screenTags = this.screenTags.get(screenId) || [];
    return screenTags.map(screenTag => this.tags.get(screenTag.tagId)).filter(Boolean) as Tag[];
  }

  async addScreenTag(screenId: string, tagId: string): Promise<ScreenTag> {
    // Check if the screen and tag exist
    const screen = this.screens.get(screenId);
    const tag = this.tags.get(tagId);
    if (!screen || !tag) {
      throw new Error("Screen or tag not found");
    }

    const newScreenTag: ScreenTag = {
      screenId,
      tagId,
      createdAt: new Date()
    };

    const screenTags = this.screenTags.get(screenId) || [];
    // Check if the tag is already assigned
    if (!screenTags.some(st => st.tagId === tagId)) {
      screenTags.push(newScreenTag);
      this.screenTags.set(screenId, screenTags);
    }

    return newScreenTag;
  }

  async removeScreenTag(screenId: string, tagId: string): Promise<boolean> {
    const screenTags = this.screenTags.get(screenId);
    if (!screenTags) return false;

    const newScreenTags = screenTags.filter(st => st.tagId !== tagId);
    this.screenTags.set(screenId, newScreenTags);

    return screenTags.length !== newScreenTags.length;
  }

  // Billing methods (stub implementations for MemStorage)
  async getPricing(): Promise<Pricing[]> {
    return [];
  }

  async getBillingSummary(teamId: string): Promise<{
    totalCreditsBought: number;
    totalCreditsUsed: number;
    creditsAvailable: number;
  }> {
    return {
      totalCreditsBought: 0,
      totalCreditsUsed: 0,
      creditsAvailable: 0,
    };
  }

  async getInvoicesByTeamId(teamId: string): Promise<Invoice[]> {
    return [];
  }

  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    const id = Math.floor(Math.random() * 1000000);
    const now = new Date();
    return {
      invoiceId: id,
      teamId: invoice.teamId,
      invoiceDate: invoice.invoiceDate,
      billingCycle: invoice.billingCycle,
      qty: invoice.qty,
      totalAmount: invoice.totalAmount,
      externalSubscriptionId: invoice.externalSubscriptionId,
      paidAt: invoice.paidAt,
      isDeleted: invoice.isDeleted || false,
    };
  }

  async updateInvoicePayment(invoiceId: string, paidAt: Date | null): Promise<Invoice | undefined> {
    return undefined;
  }

  async deleteInvoice(invoiceId: string): Promise<boolean> {
    return true;
  }

  async getScreenSubscriptionsByTeamId(teamId: string): Promise<any[]> {
    return [];
  }

  async getAvailableScreensForSubscription(teamId: string): Promise<any[]> {
    return [];
  }

  async bulkUpdateScreenRegistrations(updates: {
    screenId: string;
    trialEndsAt: Date;
    subscriptionStatus: string;
    billingCycle: string;
    invoiceId: number;
  }[]): Promise<boolean> {
    return true;
  }

  async getCampaignLogSummary(teamId: string, timeRange: 'week' | 'month' | 'year'): Promise<any[]> {
    // Return empty array for MemStorage (stub implementation)
    return [];
  }

  async getCampaignSummaryTimeseries(teamId: string, startDate: string, endDate: string, option: 'week' | 'month' | 'year'): Promise<any[]> {
    // Return empty array for MemStorage (stub implementation)
    return [];
  }

  async getCampaignMediaItems(campaignId: string): Promise<any[]> {
    // In memory storage, return empty array
    return [];
  }

  async getCampaignSummary(campaignId: string, mediaId: string | null, teamId: string, startDate: string, endDate: string): Promise<any> {
    // In memory storage, return mock data
    return {
      campaignname: "Sample Campaign",
      startdate: startDate,
      enddate: endDate,
      totalsites: 0,
      totalviews: 0,
      thumbnail: "",
      screeninfo: []
    };
  }
}

import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Create Supabase client using environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

// Make sure we have credentials
if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials!');
  console.error('Please set the following environment variables:');
  console.error('- VITE_SUPABASE_URL: Your Supabase project URL');
  console.error('- VITE_SUPABASE_ANON_KEY: Your Supabase anon/public key');
  console.error('');
  console.error('You can get these from your Supabase project dashboard:');
  console.error('https://app.supabase.com/project/[your-project]/settings/api');
  console.error('');
  console.error('Create a .env file in the project root with:');
  console.error('VITE_SUPABASE_URL=https://your-project-id.supabase.co');
  console.error('VITE_SUPABASE_ANON_KEY=your-anon-key-here');
  throw new Error('Missing Supabase credentials - see error message above for setup instructions');
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Database storage implementation
export class DatabaseStorage implements IStorage {

  // Profiles
  async getProfile(id: string): Promise<Profile | undefined> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      avatarUrl: data.avatar_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async getProfileByEmail(email: string): Promise<Profile | undefined> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      avatarUrl: data.avatar_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async createProfile(profile: InsertProfile): Promise<Profile> {
    const { data, error } = await supabase
      .from('profiles')
      .insert([{
        id: profile.id || uuidv4(),
        email: profile.email,
        first_name: profile.firstName,
        last_name: profile.lastName,
        avatar_url: profile.avatarUrl,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      avatarUrl: data.avatar_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async updateProfile(id: string, profile: Partial<InsertProfile>): Promise<Profile | undefined> {
    const updateData: any = {};
    if (profile.email) updateData.email = profile.email;
    if (profile.firstName) updateData.first_name = profile.firstName;
    if (profile.lastName) updateData.last_name = profile.lastName;

    // Handle the avatarUrl field specifically to allow null values
    if (profile.avatarUrl !== undefined) {
      updateData.avatar_url = profile.avatarUrl;
    }

    // Log for debugging
    console.log(`Updating profile ${id} with data:`, updateData);

    const { data, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      email: data.email,
      firstName: data.first_name,
      lastName: data.last_name,
      avatarUrl: data.avatar_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  // Teams
  async getTeam(id: string): Promise<Team | undefined> {
    const { data, error } = await supabase
      .from('teams')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      name: data.name,
      logoUrl: data.logo_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async getTeamsByProfileId(profileId: string): Promise<Team[]> {
    const { data, error } = await supabase
      .from('team_members')
      .select('team_id')
      .eq('profile_id', profileId);

    if (error || !data) return [];

    const teamIds = data.map(tm => tm.team_id);

    if (teamIds.length === 0) return [];

    const { data: teamsData, error: teamsError } = await supabase
      .from('teams')
      .select('*')
      .in('id', teamIds);

    if (teamsError || !teamsData) return [];

    return teamsData.map(team => ({
      id: team.id,
      name: team.name,
      logoUrl: team.logo_url,
      createdAt: new Date(team.created_at),
      updatedAt: new Date(team.updated_at),
    }));
  }

  async createTeam(team: InsertTeam): Promise<Team> {
    const { data, error } = await supabase
      .from('teams')
      .insert([{
        name: team.name,
        logo_url: team.logoUrl,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      name: data.name,
      logoUrl: data.logo_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async updateTeam(id: string, team: Partial<InsertTeam>): Promise<Team | undefined> {
    const updateData: any = {};
    if (team.name) updateData.name = team.name;
    if (team.logoUrl) updateData.logo_url = team.logoUrl;

    const { data, error } = await supabase
      .from('teams')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      name: data.name,
      logoUrl: data.logo_url,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  // Team Members
  async getTeamMembers(teamId: string): Promise<TeamMember[]> {
    const { data, error } = await supabase
      .from('team_members')
      .select(`
        *,
        profiles:profile_id(*)
      `)
      .eq('team_id', teamId);

    if (error || !data) return [];

    return data.map(item => ({
      profileId: item.profile_id,
      teamId: item.team_id,
      role: item.role,
      createdAt: new Date(item.created_at),
      profile: item.profiles ? {
        id: item.profiles.id,
        email: item.profiles.email,
        firstName: item.profiles.first_name,
        lastName: item.profiles.last_name,
        avatarUrl: item.profiles.avatar_url,
        createdAt: new Date(item.profiles.created_at),
        updatedAt: new Date(item.profiles.updated_at),
      } : undefined
    }));
  }

  async createTeamMember(teamMember: InsertTeamMember): Promise<TeamMember> {
    const { data, error } = await supabase
      .from('team_members')
      .insert([{
        profile_id: teamMember.profileId,
        team_id: teamMember.teamId,
        role: teamMember.role,
      }])
      .select(`
        *,
        profiles:profile_id(*)
      `)
      .single();

    if (error) throw error;

    return {
      profileId: data.profile_id,
      teamId: data.team_id,
      role: data.role,
      createdAt: new Date(data.created_at),
      profile: data.profiles ? {
        id: data.profiles.id,
        email: data.profiles.email,
        firstName: data.profiles.first_name,
        lastName: data.profiles.last_name,
        avatarUrl: data.profiles.avatar_url,
        createdAt: new Date(data.profiles.created_at),
        updatedAt: new Date(data.profiles.updated_at),
      } : undefined
    };
  }

  async removeTeamMember(profileId: string, teamId: string): Promise<boolean> {
    const { error } = await supabase
      .from('team_members')
      .delete()
      .eq('profile_id', profileId)
      .eq('team_id', teamId);

    return !error;
  }

  // Media Items
  async getMediaItem(id: string): Promise<MediaItem | undefined> {
    const { data, error } = await supabase
      .from('media_items')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      fileUrl: data.file_url,
      thumbnailUrl: data.thumbnail_url,
      fileType: data.file_type,
      fileSize: data.file_size,
      width: data.width,
      height: data.height,
      duration: data.duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async getMediaItemsByTeamId(teamId: string): Promise<MediaItem[]> {
    const { data, error } = await supabase
      .from('media_items')
      .select('*')
      .eq('team_id', teamId);

    if (error || !data) return [];

    return data.map(item => ({
      id: item.id,
      teamId: item.team_id,
      name: item.name,
      description: item.description,
      fileUrl: item.file_url,
      thumbnailUrl: item.thumbnail_url,
      fileType: item.file_type,
      fileSize: item.file_size,
      width: item.width,
      height: item.height,
      duration: item.duration,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
    }));
  }

  // Optimized function to get media items with their tags in a single query
  async getMediaItemsWithTagsByTeamId(teamId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('media_items')
      .select(`
        *,
        media_tags(
          tags(
            id,
            name,
            color
          )
        )
      `)
      .eq('team_id', teamId)
      .order('created_at', { ascending: false });

    if (error || !data) {
      console.error('Error fetching media with tags:', error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      teamId: item.team_id,
      name: item.name,
      description: item.description,
      fileUrl: item.file_url,
      thumbnailUrl: item.thumbnail_url,
      fileType: item.file_type,
      fileSize: item.file_size,
      width: item.width,
      height: item.height,
      duration: item.duration,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
      tags: item.media_tags
        ? item.media_tags
            .filter((mt: any) => mt.tags) // Filter out null tags
            .map((mt: any) => mt.tags.name)
        : []
    }));
  }

  async getMediaItemsByTagId(tagId: string): Promise<MediaItem[]> {
    // First get all media_tags that have this tag
    const { data: mediaTags, error: mediaTagsError } = await supabase
      .from('media_tags')
      .select('media_id')
      .eq('tag_id', tagId);

    if (mediaTagsError || !mediaTags || mediaTags.length === 0) {
      return [];
    }

    // Extract media IDs
    const mediaIds = mediaTags.map(mt => mt.media_id);

    // Get media items with those IDs
    const { data, error } = await supabase
      .from('media_items')
      .select('*')
      .in('id', mediaIds);

    if (error || !data) return [];

    return data.map(item => ({
      id: item.id,
      teamId: item.team_id,
      name: item.name,
      description: item.description,
      fileUrl: item.file_url,
      thumbnailUrl: item.thumbnail_url,
      fileType: item.file_type,
      fileSize: item.file_size,
      width: item.width,
      height: item.height,
      duration: item.duration,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at),
    }));
  }

  async createMediaItem(mediaItem: InsertMediaItem): Promise<MediaItem> {
    const { data, error } = await supabase
      .from('media_items')
      .insert([{
        team_id: mediaItem.teamId,
        name: mediaItem.name,
        description: mediaItem.description,
        file_url: mediaItem.fileUrl,
        thumbnail_url: mediaItem.thumbnailUrl,
        file_type: mediaItem.fileType,
        file_size: mediaItem.fileSize,
        width: mediaItem.width,
        height: mediaItem.height,
        duration: mediaItem.duration,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      fileUrl: data.file_url,
      thumbnailUrl: data.thumbnail_url,
      fileType: data.file_type,
      fileSize: data.file_size,
      width: data.width,
      height: data.height,
      duration: data.duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async updateMediaItem(id: string, mediaItem: Partial<InsertMediaItem>): Promise<MediaItem | undefined> {
    const updateData: any = {};

    if (mediaItem.name) updateData.name = mediaItem.name;
    if (mediaItem.description !== undefined) updateData.description = mediaItem.description;
    if (mediaItem.fileUrl) updateData.file_url = mediaItem.fileUrl;
    if (mediaItem.thumbnailUrl !== undefined) updateData.thumbnail_url = mediaItem.thumbnailUrl;
    if (mediaItem.fileType) updateData.file_type = mediaItem.fileType;
    if (mediaItem.fileSize !== undefined) updateData.file_size = mediaItem.fileSize;
    if (mediaItem.width !== undefined) updateData.width = mediaItem.width;
    if (mediaItem.height !== undefined) updateData.height = mediaItem.height;
    if (mediaItem.duration !== undefined) updateData.duration = mediaItem.duration;

    const { data, error } = await supabase
      .from('media_items')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      fileUrl: data.file_url,
      thumbnailUrl: data.thumbnail_url,
      fileType: data.file_type,
      fileSize: data.file_size,
      width: data.width,
      height: data.height,
      duration: data.duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async deleteMediaItem(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('media_items')
      .delete()
      .eq('id', id);

    return !error;
  }

  // Screens
  async getScreen(id: string): Promise<Screen | undefined> {
    const { data, error } = await supabase
      .from('screens')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      location: data.location,
      code: data.code,
      status: data.status,
      lastPingAt: data.last_ping_at ? new Date(data.last_ping_at) : undefined,
      startTime: data.start_time,
      endTime: data.end_time,
      updateFrequency: data.update_frequency,
      siteEmail: data.site_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      isRegistered: data.is_registered,
      updateguid: data.updateguid,
      health: data.health,
      isDeleted: data.is_deleted,
    };
  }

  async getScreensByTeamId(teamId: string): Promise<Screen[]> {
    const { data, error } = await supabase
      .from('screens')
      .select('*')
      .eq('team_id', teamId);

    if (error || !data) return [];

    return data.map(screen => ({
      id: screen.id,
      teamId: screen.team_id,
      name: screen.name,
      location: screen.location,
      code: screen.code,
      status: screen.status,
      lastPingAt: screen.last_ping_at ? new Date(screen.last_ping_at) : undefined,
      startTime: screen.start_time,
      endTime: screen.end_time,
      updateFrequency: screen.update_frequency,
      siteEmail: screen.site_email,
      createdAt: new Date(screen.created_at),
      updatedAt: new Date(screen.updated_at),
      isRegistered: screen.is_registered,
      updateguid: screen.updateguid,
      health: screen.health,
      isDeleted: screen.is_deleted,
    }));
  }

  // Optimized function to get screens with their tags in a single query
  async getScreensWithTagsByTeamId(teamId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('screens')
      .select(`
        *,
        screen_tags(
          tags(
            id,
            name,
            color
          )
        )
      `)
      .eq('team_id', teamId)
      .order('created_at', { ascending: false });

    if (error || !data) {
      console.error('Error fetching screens with tags:', error);
      return [];
    }

    return data.map(screen => ({
      id: screen.id,
      teamId: screen.team_id,
      name: screen.name,
      location: screen.location,
      code: screen.code,
      status: screen.status,
      lastPingAt: screen.last_ping_at ? new Date(screen.last_ping_at) : undefined,
      startTime: screen.start_time,
      endTime: screen.end_time,
      updateFrequency: screen.update_frequency,
      siteEmail: screen.site_email,
      createdAt: new Date(screen.created_at),
      updatedAt: new Date(screen.updated_at),
      isRegistered: screen.is_registered,
      updateguid: screen.updateguid,
      health: screen.health,
      isDeleted: screen.is_deleted,
      tags: screen.screen_tags
        ? screen.screen_tags
            .filter((st: any) => st.tags) // Filter out null tags
            .map((st: any) => st.tags.name)
        : []
    }));
  }

  // Optimized function to get tags with their associations in a single query
  async getTagsWithAssociationsByTeamId(teamId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('tags')
      .select(`
        *,
        media_tags(
          media_id
        ),
        screen_tags(
          screen_id
        )
      `)
      .eq('team_id', teamId)
      .order('created_at', { ascending: false });

    if (error || !data) {
      console.error('Error fetching tags with associations:', error);
      return [];
    }

    return data.map(tag => ({
      id: tag.id,
      teamId: tag.team_id,
      name: tag.name,
      color: tag.color,
      createdAt: new Date(tag.created_at),
      updatedAt: new Date(tag.updated_at),
      mediaIds: tag.media_tags ? tag.media_tags.map((mt: any) => mt.media_id) : [],
      screenIds: tag.screen_tags ? tag.screen_tags.map((st: any) => st.screen_id) : []
    }));
  }

  async getScreensByTagId(tagId: string): Promise<Screen[]> {
    // First get all screen_tags that have this tag
    const { data: screenTags, error: screenTagsError } = await supabase
      .from('screen_tags')
      .select('screen_id')
      .eq('tag_id', tagId);

    if (screenTagsError || !screenTags || screenTags.length === 0) {
      return [];
    }

    // Extract screen IDs
    const screenIds = screenTags.map(st => st.screen_id);

    // Get screens with those IDs
    const { data, error } = await supabase
      .from('screens')
      .select('*')
      .in('id', screenIds);

    if (error || !data) return [];

    return data.map(screen => ({
      id: screen.id,
      teamId: screen.team_id,
      name: screen.name,
      location: screen.location,
      code: screen.code,
      status: screen.status,
      lastPingAt: screen.last_ping_at ? new Date(screen.last_ping_at) : undefined,
      startTime: screen.start_time,
      endTime: screen.end_time,
      updateFrequency: screen.update_frequency,
      siteEmail: screen.site_email,
      createdAt: new Date(screen.created_at),
      updatedAt: new Date(screen.updated_at),
      isRegistered: screen.is_registered,
      updateguid: screen.updateguid,
      health: screen.health,
      isDeleted: screen.is_deleted,
    }));
  }

  async getScreenByCode(code: string): Promise<Screen | undefined> {
    const { data, error } = await supabase
      .from('screens')
      .select('*')
      .eq('code', code)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      location: data.location,
      code: data.code,
      status: data.status,
      lastPingAt: data.last_ping_at ? new Date(data.last_ping_at) : undefined,
      startTime: data.start_time,
      endTime: data.end_time,
      updateFrequency: data.update_frequency,
      siteEmail: data.site_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      isRegistered: data.is_registered,
      updateguid: data.updateguid,
      health: data.health,
      isDeleted: data.is_deleted,
    };
  }

  async createScreen(screen: InsertScreen): Promise<Screen> {
    const { data, error } = await supabase
      .from('screens')
      .insert([{
        team_id: screen.teamId,
        name: screen.name,
        location: screen.location,
        code: screen.code,
        status: screen.status,
        last_ping_at: screen.lastPingAt,
        start_time: screen.startTime,
        end_time: screen.endTime,
        update_frequency: screen.updateFrequency,
        site_email: screen.siteEmail,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      location: data.location,
      code: data.code,
      status: data.status,
      lastPingAt: data.last_ping_at ? new Date(data.last_ping_at) : undefined,
      startTime: data.start_time,
      endTime: data.end_time,
      updateFrequency: data.update_frequency,
      siteEmail: data.site_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      isRegistered: data.is_registered,
      updateguid: data.updateguid,
      health: data.health,
      isDeleted: data.is_deleted,
    };
  }

  async updateScreen(id: string, screen: Partial<InsertScreen>): Promise<Screen | undefined> {
    const updateData: any = {};

    if (screen.name) updateData.name = screen.name;
    if (screen.location !== undefined) updateData.location = screen.location;
    if (screen.code) updateData.code = screen.code;
    if (screen.status) updateData.status = screen.status;
    if (screen.lastPingAt) updateData.last_ping_at = screen.lastPingAt;
    if (screen.startTime !== undefined) updateData.start_time = screen.startTime;
    if (screen.endTime !== undefined) updateData.end_time = screen.endTime;
    if (screen.updateFrequency !== undefined) updateData.update_frequency = screen.updateFrequency;
    if (screen.siteEmail !== undefined) updateData.site_email = screen.siteEmail;

    const { data, error } = await supabase
      .from('screens')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      location: data.location,
      code: data.code,
      status: data.status,
      lastPingAt: data.last_ping_at ? new Date(data.last_ping_at) : undefined,
      startTime: data.start_time,
      endTime: data.end_time,
      updateFrequency: data.update_frequency,
      siteEmail: data.site_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      isRegistered: data.is_registered,
      updateguid: data.updateguid,
      health: data.health,
      isDeleted: data.is_deleted,
    };
  }

  async deleteScreen(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('screens')
      .delete()
      .eq('id', id);

    return !error;
  }

  // Enhanced screen function with LEFT JOINs for activities and registrations
  async getEnhancedScreen(id: string): Promise<any> {
    // Use raw SQL with LEFT JOINs for better performance
    const { data, error } = await supabase.rpc('get_enhanced_screen_by_id', {
      screen_id_param: id
    });

    if (error) {
      console.error('Error fetching enhanced screen with RPC:', error);
      // Fallback to the original method if RPC doesn't exist
      return this.getScreen(id);
    }

    if (!data || data.length === 0) {
      return undefined;
    }

    const row = data[0];
    return {
      id: row.id,
      teamId: row.team_id,
      name: row.name,
      location: row.location,
      code: row.code,
      status: row.status,
      lastPingAt: row.last_ping_at ? new Date(row.last_ping_at) : undefined,
      startTime: row.start_time,
      endTime: row.end_time,
      updateFrequency: row.update_frequency,
      siteEmail: row.site_email,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      isRegistered: row.is_registered,
      updateguid: row.updateguid,
      health: row.health,
      isDeleted: row.is_deleted,
      // Enhanced fields from LEFT JOINs
      maxFileDownloadCount: row.max_file_download_count || 0,
      maxTotalFileDownload: row.max_total_file_download || 0,
      trialEndsAt: row.trial_ends_at ? new Date(row.trial_ends_at) : undefined,
      subscriptionStatus: row.subscription_status || 'unregistered',
      lemonsqueezySubscriptionId: row.lemonsqueezy_subscription_id,
      billingCycle: row.billing_cycle,
    };
  }

  // Enhanced screens with tags function that includes activity and registration data using LEFT JOINs
  async getEnhancedScreensWithTagsByTeamId(teamId: string): Promise<any[]> {
    // Use raw SQL with LEFT JOINs for better performance
    const { data, error } = await supabase.rpc('get_enhanced_screens_with_tags', {
      team_id_param: teamId
    });

    if (error) {
      console.error('Error fetching enhanced screens with RPC:', error);
      // Fallback to the original method if RPC doesn't exist
      return this.getScreensWithTagsByTeamId(teamId);
    }

    if (!data || !Array.isArray(data)) {
      return [];
    }

    // Transform the data to match our expected format
    return data.map((row: any) => ({
      id: row.id,
      teamId: row.team_id,
      name: row.name,
      location: row.location,
      code: row.code,
      status: row.status,
      lastPingAt: row.last_ping_at ? new Date(row.last_ping_at) : undefined,
      startTime: row.start_time,
      endTime: row.end_time,
      updateFrequency: row.update_frequency,
      siteEmail: row.site_email,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
      isRegistered: row.is_registered,
      updateguid: row.updateguid,
      health: row.health,
      isDeleted: row.is_deleted,
      tags: row.tags ? row.tags.split(',').filter(Boolean) : [],
      // Enhanced fields from LEFT JOINs
      maxFileDownloadCount: row.max_file_download_count || 0,
      maxTotalFileDownload: row.max_total_file_download || 0,
      trialEndsAt: row.trial_ends_at ? new Date(row.trial_ends_at) : undefined,
      subscriptionStatus: row.subscription_status || 'unregistered',
      lemonsqueezySubscriptionId: row.lemonsqueezy_subscription_id,
      billingCycle: row.billing_cycle,
    }));
  }

  // Campaigns
  async getCampaign(id: string): Promise<Campaign | undefined> {
    const { data, error } = await supabase
      .rpc('get_campaign_with_counts', { campaign_id_param: id });

    if (error) {
      console.error("Error fetching campaign with counts:", error);

      // Fallback to basic query if the RPC function is not available
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('campaigns')
        .select('*')
        .eq('id', id)
        .single();

      if (fallbackError || !fallbackData) return undefined;

      // Calculate counts manually in fallback case
      let screenCount = 0;
      let mediaCount = 0;
      let slideCount = 0;

      try {
        // Get screen count
        const { data: screenData } = await supabase
          .from('campaign_screens')
          .select('*')
          .eq('campaign_id', id);
        screenCount = screenData?.length || 0;

        // Get media count (campaign_type = 0)
        const { data: mediaData } = await supabase
          .from('campaign_medias')
          .select('*')
          .eq('campaign_id', id)
          .eq('campaign_type', 0);
        mediaCount = mediaData?.length || 0;

        // Get slide count (campaign_type = 1)
        const { data: slideData } = await supabase
          .from('campaign_medias')
          .select('*')
          .eq('campaign_id', id)
          .eq('campaign_type', 1);
        slideCount = slideData?.length || 0;
      } catch (countError) {
        console.error("Error calculating counts in fallback:", countError);
        // Keep counts as 0 if there's an error
      }

      return {
        id: fallbackData.id,
        teamId: fallbackData.team_id,
        name: fallbackData.name,
        description: fallbackData.description,
        startDate: fallbackData.start_date ? new Date(fallbackData.start_date) : undefined,
        endDate: fallbackData.end_date ? new Date(fallbackData.end_date) : undefined,
        status: fallbackData.status,
        createdAt: new Date(fallbackData.created_at),
        updatedAt: new Date(fallbackData.updated_at),
        screenCount,
        mediaCount,
        slideCount
      };
    }

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      startDate: data.start_date ? new Date(data.start_date) : undefined,
      endDate: data.end_date ? new Date(data.end_date) : undefined,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      screenCount: data.screen_count || 0,
      mediaCount: data.media_count || 0,
      slideCount: data.slide_count || 0
    };
  }

  async getCampaignsByTeamId(teamId: string): Promise<Campaign[]> {
    console.log(`[getCampaignsByTeamId] Fetching campaigns with counts for team ID: ${teamId}`);

    // 1. Get all campaigns for the team
    const { data: campaigns, error } = await supabase
      .from('campaigns')
      .select('*')
      .eq('team_id', teamId);

    if (error || !campaigns) {
      console.error("[getCampaignsByTeamId] Error fetching campaigns:", error);
      return [];
    }

    console.log(`[getCampaignsByTeamId] Found ${campaigns.length} campaigns for team ${teamId}`);

    // If no campaigns, return empty array
    if (campaigns.length === 0) {
      return [];
    }

    // 2. Extract all campaign IDs for efficient lookup
    const campaignIds = campaigns.map(c => c.id);

    // For Supabase, we need to query campaign screens one by one for now
    // until we find a proper way to do count aggregations
    const screenCountMap = new Map();
    for (const campaignId of campaignIds) {
      try {
        const { data, error } = await supabase
          .from('campaign_screens')
          .select('*')
          .eq('campaign_id', campaignId);

        if (!error && data) {
          screenCountMap.set(campaignId, data.length);
        }
      } catch (err) {
        console.error(`Error fetching screen count for campaign ${campaignId}:`, err);
      }
    }

    // Do the same for media counts
    const mediaCountMap = new Map();
    for (const campaignId of campaignIds) {
      try {
        const { data, error } = await supabase
          .from('campaign_medias')
          .select('*')
          .eq('campaign_id', campaignId)
          .eq('campaign_type', 0);

        if (!error && data) {
          mediaCountMap.set(campaignId, data.length);
        }
      } catch (err) {
        console.error(`Error fetching media count for campaign ${campaignId}:`, err);
      }
    }

    // Do the same for slide counts
    const slideCountMap = new Map();
    for (const campaignId of campaignIds) {
      try {
        const { data, error } = await supabase
          .from('campaign_medias')
          .select('*')
          .eq('campaign_id', campaignId)
          .eq('campaign_type', 1);

        if (!error && data) {
          slideCountMap.set(campaignId, data.length);
        }
      } catch (err) {
        console.error(`Error fetching slide count for campaign ${campaignId}:`, err);
      }
    }

    // Note: We're using the slideCountMap that was created in the loop above

    // For debugging
    console.log(`[getCampaignsByTeamId] Count maps:`, {
      screens: Object.fromEntries(screenCountMap),
      media: Object.fromEntries(mediaCountMap),
      slides: Object.fromEntries(slideCountMap)
    });

    // 6. Combine all data into the final result
    return campaigns.map(campaign => ({
      id: campaign.id,
      teamId: campaign.team_id,
      name: campaign.name,
      description: campaign.description,
      startDate: campaign.start_date ? new Date(campaign.start_date) : undefined,
      endDate: campaign.end_date ? new Date(campaign.end_date) : undefined,
      status: campaign.status,
      createdAt: new Date(campaign.created_at),
      updatedAt: new Date(campaign.updated_at),
      screenCount: screenCountMap.get(campaign.id) || 0,
      mediaCount: mediaCountMap.get(campaign.id) || 0,
      slideCount: slideCountMap.get(campaign.id) || 0
    }));
  }

  async createCampaign(campaign: InsertCampaign): Promise<Campaign> {
    const { data, error } = await supabase
      .from('campaigns')
      .insert([{
        team_id: campaign.teamId,
        name: campaign.name,
        description: campaign.description,
        start_date: campaign.startDate,
        end_date: campaign.endDate,
        status: campaign.status,
      }])
      .select()
      .single();

    if (error) throw error;

    // Get the campaign with counts after creation
    return this.getCampaign(data.id) || {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      startDate: data.start_date ? new Date(data.start_date) : undefined,
      endDate: data.end_date ? new Date(data.end_date) : undefined,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      screenCount: 0,
      mediaCount: 0,
      slideCount: 0
    };
  }

  async updateCampaign(id: string, campaign: Partial<InsertCampaign>): Promise<Campaign | undefined> {
    const updateData: any = {};

    if (campaign.name) updateData.name = campaign.name;
    if (campaign.description !== undefined) updateData.description = campaign.description;
    if (campaign.startDate !== undefined) updateData.start_date = campaign.startDate;
    if (campaign.endDate !== undefined) updateData.end_date = campaign.endDate;
    if (campaign.status) updateData.status = campaign.status;

    const { data, error } = await supabase
      .from('campaigns')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    // Get the campaign with counts after update
    return this.getCampaign(id) || {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      description: data.description,
      startDate: data.start_date ? new Date(data.start_date) : undefined,
      endDate: data.end_date ? new Date(data.end_date) : undefined,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
      screenCount: 0,
      mediaCount: 0,
      slideCount: 0
    };
  }

  async deleteCampaign(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('campaigns')
      .delete()
      .eq('id', id);

    return !error;
  }

  // Campaign Media
  async getCampaignMedias(campaignId: string): Promise<CampaignMedia[]> {
    console.log(`[getCampaignMedias] Fetching media for campaign ID: ${campaignId}`);

    // Retrieve simple campaign_medias data without joins
    const { data, error } = await supabase
      .from('campaign_medias')
      .select('*')
      .eq('campaign_id', campaignId);

    if (error) {
      console.error('[getCampaignMedias] Error fetching campaign media:', error);
      return [];
    }

    if (!data || data.length === 0) {
      console.log('[getCampaignMedias] No media found for this campaign');
      return [];
    }

    console.log(`[getCampaignMedias] Found ${data.length} media items`);

    return data.map(item => {
      // Return basic campaign media data
      return {
        campaignId: item.campaign_id,
        mediaId: item.media_id,
        displayOrder: item.display_order,
        campaignType: item.campaign_type || 0, // Default to 0 (media) if not specified
        createdAt: new Date(item.created_at)
      };
    });
  }

  async createCampaignMedia(campaignMedia: InsertCampaignMedia): Promise<CampaignMedia> {
    const { data, error } = await supabase
      .from('campaign_medias')
      .insert([{
        campaign_id: campaignMedia.campaignId,
        media_id: campaignMedia.mediaId,
        display_order: campaignMedia.displayOrder,
        campaign_type: campaignMedia.campaignType,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      campaignId: data.campaign_id,
      mediaId: data.media_id,
      displayOrder: data.display_order,
      campaignType: data.campaign_type,
      createdAt: new Date(data.created_at)
    };
  }

  async deleteCampaignMedia(campaignId: string, mediaId: string): Promise<boolean> {
    const { error } = await supabase
      .from('campaign_medias')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('media_id', mediaId);

    return !error;
  }

  // Campaign Slides - Using campaign_medias table with campaign_type=1
  async getCampaignSlides(campaignId: string): Promise<CampaignSlide[]> {
    console.log(`[getCampaignSlides] Fetching slides for campaign ID: ${campaignId}`);

    // Retrieve from campaign_medias where campaign_type = 1 (slides)
    const { data, error } = await supabase
      .from('campaign_medias')
      .select('*')
      .eq('campaign_id', campaignId)
      .eq('campaign_type', 1);

    if (error) {
      console.error('[getCampaignSlides] Error fetching campaign slides:', error);
      return [];
    }

    if (!data || data.length === 0) {
      console.log('[getCampaignSlides] No slides found for this campaign');
      return [];
    }

    console.log(`[getCampaignSlides] Found ${data.length} slide items`);

    // Map them to the CampaignSlide format
    return data.map(item => ({
      campaignId: item.campaign_id,
      slideId: item.media_id, // slideId is in the media_id field
      displayOrder: item.display_order,
      createdAt: new Date(item.created_at),
    }));
  }

  // These functions use campaign-media table but are kept for
  // backwards compatibility with existing code
  async createCampaignSlide(campaignSlide: InsertCampaignSlide): Promise<CampaignSlide> {
    // Insert into campaign_medias with campaign_type=1
    const { data, error } = await supabase
      .from('campaign_medias')
      .insert([{
        campaign_id: campaignSlide.campaignId,
        media_id: campaignSlide.slideId,
        display_order: campaignSlide.displayOrder,
        campaign_type: 1 // 1 = slide
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      campaignId: data.campaign_id,
      slideId: data.media_id,
      displayOrder: data.display_order,
      createdAt: new Date(data.created_at),
    };
  }

  async deleteCampaignSlide(campaignId: string, slideId: string): Promise<boolean> {
    // Delete from campaign_medias
    const { error } = await supabase
      .from('campaign_medias')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('media_id', slideId)
      .eq('campaign_type', 1); // Make sure we only delete slides

    return !error;
  }

  // Campaign Screens
  async getCampaignScreens(campaignId: string): Promise<CampaignScreen[]> {
    const { data, error } = await supabase
      .from('campaign_screens')
      .select(`
        *,
        screen:screen_id(*)
      `)
      .eq('campaign_id', campaignId);

    if (error || !data) return [];

    return data.map(item => ({
      campaignId: item.campaign_id,
      screenId: item.screen_id,
      createdAt: new Date(item.created_at),
      screen: item.screen ? {
        id: item.screen.id,
        teamId: item.screen.team_id,
        name: item.screen.name,
        location: item.screen.location,
        code: item.screen.code,
        status: item.screen.status,
        lastPingAt: item.screen.last_ping_at ? new Date(item.screen.last_ping_at) : undefined,
        startTime: item.screen.start_time,
        endTime: item.screen.end_time,
        updateFrequency: item.screen.update_frequency,
        siteEmail: item.screen.site_email,
        createdAt: new Date(item.screen.created_at),
        updatedAt: new Date(item.screen.updated_at),
      } : undefined
    }));
  }

  async getScreenCampaigns(screenId: string): Promise<CampaignScreen[]> {
    const { data, error } = await supabase
      .from('campaign_screens')
      .select(`
        *,
        campaign:campaign_id(*)
      `)
      .eq('screen_id', screenId);

    if (error || !data) return [];

    return data.map(item => ({
      campaignId: item.campaign_id,
      screenId: item.screen_id,
      createdAt: new Date(item.created_at),
      campaign: item.campaign ? {
        id: item.campaign.id,
        teamId: item.campaign.team_id,
        name: item.campaign.name,
        description: item.campaign.description,
        startDate: item.campaign.start_date ? new Date(item.campaign.start_date) : null,
        endDate: item.campaign.end_date ? new Date(item.campaign.end_date) : null,
        status: item.campaign.status,
        createdAt: new Date(item.campaign.created_at),
        updatedAt: new Date(item.campaign.updated_at),
      } : undefined
    }));
  }

  async createCampaignScreen(campaignScreen: InsertCampaignScreen): Promise<CampaignScreen> {
    const { data, error } = await supabase
      .from('campaign_screens')
      .insert([{
        campaign_id: campaignScreen.campaignId,
        screen_id: campaignScreen.screenId,
      }])
      .select(`
        *,
        screen:screen_id(*)
      `)
      .single();

    if (error) throw error;

    return {
      campaignId: data.campaign_id,
      screenId: data.screen_id,
      createdAt: new Date(data.created_at),
      screen: data.screen ? {
        id: data.screen.id,
        teamId: data.screen.team_id,
        name: data.screen.name,
        location: data.screen.location,
        code: data.screen.code,
        status: data.screen.status,
        lastPingAt: data.screen.last_ping_at ? new Date(data.screen.last_ping_at) : undefined,
        startTime: data.screen.start_time,
        endTime: data.screen.end_time,
        updateFrequency: data.screen.update_frequency,
        siteEmail: data.screen.site_email,
        createdAt: new Date(data.screen.created_at),
        updatedAt: new Date(data.screen.updated_at),
      } : undefined
    };
  }

  async deleteCampaignScreen(campaignId: string, screenId: string): Promise<boolean> {
    const { error } = await supabase
      .from('campaign_screens')
      .delete()
      .eq('campaign_id', campaignId)
      .eq('screen_id', screenId);

    return !error;
  }

  // Slides
  async getSlide(id: string): Promise<Slide | undefined> {
    const { data, error } = await supabase
      .from('slides')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      content: data.content,
      thumbnailUrl: data.thumbnail_url,
      slideWidth: data.slide_width,
      slideHeight: data.slide_height,
      slideBackgroundColor: data.slide_background_color,
      apiUrl: data.api_url,
      apiDataPreviewDuration: data.api_data_preview_duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async getSlidesByTeamId(teamId: string): Promise<Slide[]> {
    const { data, error } = await supabase
      .from('slides')
      .select('*')
      .eq('team_id', teamId);

    if (error || !data) return [];

    return data.map(slide => ({
      id: slide.id,
      teamId: slide.team_id,
      name: slide.name,
      content: slide.content,
      thumbnailUrl: slide.thumbnail_url,
      slideWidth: slide.slide_width,
      slideHeight: slide.slide_height,
      slideBackgroundColor: slide.slide_background_color,
      apiUrl: slide.api_url,
      apiDataPreviewDuration: slide.api_data_preview_duration,
      createdAt: new Date(slide.created_at),
      updatedAt: new Date(slide.updated_at),
    }));
  }

  async createSlide(slide: InsertSlide): Promise<Slide> {
    const { data, error } = await supabase
      .from('slides')
      .insert([{
        team_id: slide.teamId,
        name: slide.name,
        content: slide.content,
        thumbnail_url: slide.thumbnailUrl,
        slide_width: slide.slideWidth,
        slide_height: slide.slideHeight,
        slide_background_color: slide.slideBackgroundColor,
        api_url: slide.apiUrl,
        api_data_preview_duration: slide.apiDataPreviewDuration,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      content: data.content,
      thumbnailUrl: data.thumbnail_url,
      slideWidth: data.slide_width,
      slideHeight: data.slide_height,
      slideBackgroundColor: data.slide_background_color,
      apiUrl: data.api_url,
      apiDataPreviewDuration: data.api_data_preview_duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async updateSlide(id: string, slide: Partial<InsertSlide>): Promise<Slide | undefined> {
    const updateData: any = {};

    if (slide.name) updateData.name = slide.name;
    if (slide.content) updateData.content = slide.content;
    if (slide.thumbnailUrl !== undefined) updateData.thumbnail_url = slide.thumbnailUrl;
    if (slide.slideWidth !== undefined) updateData.slide_width = slide.slideWidth;
    if (slide.slideHeight !== undefined) updateData.slide_height = slide.slideHeight;
    if (slide.slideBackgroundColor !== undefined) updateData.slide_background_color = slide.slideBackgroundColor;
    if (slide.apiUrl !== undefined) updateData.api_url = slide.apiUrl;
    if (slide.apiDataPreviewDuration !== undefined) updateData.api_data_preview_duration = slide.apiDataPreviewDuration;

    const { data, error } = await supabase
      .from('slides')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      content: data.content,
      thumbnailUrl: data.thumbnail_url,
      slideWidth: data.slide_width,
      slideHeight: data.slide_height,
      slideBackgroundColor: data.slide_background_color,
      apiUrl: data.api_url,
      apiDataPreviewDuration: data.api_data_preview_duration,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async deleteSlide(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('slides')
      .delete()
      .eq('id', id);

    return !error;
  }

  // Tags
  async getTag(id: string): Promise<Tag | undefined> {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      color: data.color,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async getTagsByTeamId(teamId: string): Promise<Tag[]> {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('team_id', teamId);

    if (error || !data) return [];

    return data.map(tag => ({
      id: tag.id,
      teamId: tag.team_id,
      name: tag.name,
      color: tag.color,
      createdAt: new Date(tag.created_at),
      updatedAt: new Date(tag.updated_at),
    }));
  }

  async createTag(tag: InsertTag): Promise<Tag> {
    const { data, error } = await supabase
      .from('tags')
      .insert([{
        team_id: tag.teamId,
        name: tag.name,
        color: tag.color,
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      color: data.color,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async updateTag(id: string, tag: Partial<InsertTag>): Promise<Tag | undefined> {
    const updateData: any = {};

    if (tag.name) updateData.name = tag.name;
    if (tag.color) updateData.color = tag.color;

    const { data, error } = await supabase
      .from('tags')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      color: data.color,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  async deleteTag(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('tags')
      .delete()
      .eq('id', id);

    return !error;
  }

  async getTagsByName(teamId: string, name: string): Promise<Tag | undefined> {
    const { data, error } = await supabase
      .from('tags')
      .select('*')
      .eq('team_id', teamId)
      .ilike('name', name)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      teamId: data.team_id,
      name: data.name,
      color: data.color,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    };
  }

  // Media Tags
  async getMediaTags(mediaId: string): Promise<Tag[]> {
    const { data, error } = await supabase
      .from('media_tags')
      .select(`
        tag_id,
        tags:tag_id(*)
      `)
      .eq('media_id', mediaId);

    if (error || !data) return [];

    return data
      .filter(item => item.tags) // Filter out null tags
      .map(item => ({
        id: item.tags.id,
        teamId: item.tags.team_id,
        name: item.tags.name,
        color: item.tags.color,
        createdAt: new Date(item.tags.created_at),
        updatedAt: new Date(item.tags.updated_at),
      }));
  }

  async addMediaTag(mediaId: string, tagId: string): Promise<MediaTag> {
    // Check if the association already exists
    const { data: existingData } = await supabase
      .from('media_tags')
      .select('*')
      .eq('media_id', mediaId)
      .eq('tag_id', tagId)
      .single();

    if (existingData) {
      return {
        mediaId: existingData.media_id,
        tagId: existingData.tag_id,
        createdAt: new Date(existingData.created_at)
      };
    }

    const { data, error } = await supabase
      .from('media_tags')
      .insert([{
        media_id: mediaId,
        tag_id: tagId
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      mediaId: data.media_id,
      tagId: data.tag_id,
      createdAt: new Date(data.created_at)
    };
  }

  async removeMediaTag(mediaId: string, tagId: string): Promise<boolean> {
    const { error } = await supabase
      .from('media_tags')
      .delete()
      .eq('media_id', mediaId)
      .eq('tag_id', tagId);

    return !error;
  }

  // Screen Tags
  async getScreenTags(screenId: string): Promise<Tag[]> {
    const { data, error } = await supabase
      .from('screen_tags')
      .select(`
        tag_id,
        tags:tag_id(*)
      `)
      .eq('screen_id', screenId);

    if (error || !data) return [];

    return data
      .filter(item => item.tags) // Filter out null tags
      .map(item => ({
        id: item.tags.id,
        teamId: item.tags.team_id,
        name: item.tags.name,
        color: item.tags.color,
        createdAt: new Date(item.tags.created_at),
        updatedAt: new Date(item.tags.updated_at),
      }));
  }

  async addScreenTag(screenId: string, tagId: string): Promise<ScreenTag> {
    // Check if the association already exists
    const { data: existingData } = await supabase
      .from('screen_tags')
      .select('*')
      .eq('screen_id', screenId)
      .eq('tag_id', tagId)
      .single();

    if (existingData) {
      return {
        screenId: existingData.screen_id,
        tagId: existingData.tag_id,
        createdAt: new Date(existingData.created_at)
      };
    }

    const { data, error } = await supabase
      .from('screen_tags')
      .insert([{
        screen_id: screenId,
        tag_id: tagId
      }])
      .select()
      .single();

    if (error) throw error;

    return {
      screenId: data.screen_id,
      tagId: data.tag_id,
      createdAt: new Date(data.created_at)
    };
  }

  async removeScreenTag(screenId: string, tagId: string): Promise<boolean> {
    const { error } = await supabase
      .from('screen_tags')
      .delete()
      .eq('screen_id', screenId)
      .eq('tag_id', tagId);

    return !error;
  }

  // Billing methods
  async getPricing(): Promise<Pricing[]> {
    const { data, error } = await supabase
      .from('pricing')
      .select('*')
      .order('id');

    if (error || !data) return [];

    return data.map(item => ({
      id: item.id,
      billingCycleText: item.billing_cycle_text,
      billingCycleValue: item.billing_cycle_value,
      price: item.price ? parseFloat(item.price) : 0, // Convert string to number
    }));
  }

  async getBillingSummary(teamId: string): Promise<{
    totalCreditsBought: number;
    totalCreditsUsed: number;
    creditsAvailable: number;
  }> {
    // Get total credits bought from paid invoices
    const { data: invoicesData, error: invoicesError } = await supabase
      .from('invoices')
      .select('qty, invoice_id')
      .eq('team_id', teamId)
      .not('paid_at', 'is', null)
      .eq('is_deleted', false);

    const totalCreditsBought = invoicesData?.reduce((sum, invoice) => sum + (invoice.qty || 0), 0) || 0;

    // Get total credits used - count screen registrations with invoice_id from this team's paid invoices
    let totalCreditsUsed = 0;

    if (invoicesData && invoicesData.length > 0) {
      const invoiceIds = invoicesData.map(invoice => invoice.invoice_id);

      const { data: usedCreditsData, error: usedCreditsError } = await supabase
        .from('screen_registrations')
        .select('id')
        .in('invoice_id', invoiceIds)
        .eq('is_deleted', false);

      totalCreditsUsed = usedCreditsData?.length || 0;
    }

    return {
      totalCreditsBought,
      totalCreditsUsed,
      creditsAvailable: totalCreditsBought - totalCreditsUsed,
    };
  }

  async getInvoicesByTeamId(teamId: string): Promise<Invoice[]> {
    // First, get all invoices for the team
    const { data: invoicesData, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .eq('team_id', teamId)
      .eq('is_deleted', false)
      .order('invoice_date', { ascending: false });

    if (invoicesError || !invoicesData) {
      console.error('Error fetching invoices:', invoicesError);
      return [];
    }

    // Get credit usage counts for all invoices in a single query
    const invoiceIds = invoicesData.map(invoice => invoice.invoice_id);

    let creditUsageCounts: { [key: number]: number } = {};

    if (invoiceIds.length > 0) {
      const { data: registrationsData, error: registrationsError } = await supabase
        .from('screen_registrations')
        .select('invoice_id')
        .in('invoice_id', invoiceIds)
        .eq('is_deleted', false);

      if (!registrationsError && registrationsData) {
        // Count registrations per invoice
        registrationsData.forEach(reg => {
          if (reg.invoice_id) {
            creditUsageCounts[reg.invoice_id] = (creditUsageCounts[reg.invoice_id] || 0) + 1;
          }
        });
      }
    }

    return invoicesData.map(item => ({
      invoiceId: item.invoice_id,
      teamId: item.team_id,
      invoiceDate: item.invoice_date ? new Date(item.invoice_date) : null,
      billingCycle: item.billing_cycle,
      qty: item.qty,
      totalAmount: item.total_amount ? parseFloat(item.total_amount) : 0, // Convert string to number
      externalSubscriptionId: item.external_subscription_id,
      paidAt: item.paid_at ? new Date(item.paid_at) : null,
      isDeleted: item.is_deleted,
      creditUsed: creditUsageCounts[item.invoice_id] || 0,
    }));
  }

  async createInvoice(invoice: InsertInvoice): Promise<Invoice> {
    console.log('Storage createInvoice called with:', invoice); // Debug log

    // Convert ISO strings to Date objects
    const insertData = {
      team_id: invoice.teamId,
      invoice_date: invoice.invoiceDate ? new Date(invoice.invoiceDate) : new Date(),
      billing_cycle: invoice.billingCycle,
      qty: invoice.qty,
      total_amount: invoice.totalAmount?.toString(),
      external_subscription_id: invoice.externalSubscriptionId,
      paid_at: invoice.paidAt ? new Date(invoice.paidAt) : new Date(),
      is_deleted: invoice.isDeleted || false,
    };

    console.log('Inserting data:', insertData);

    const { data, error } = await supabase
      .from('invoices')
      .insert([insertData])
      .select()
      .single();

    if (error) {
      console.error('Supabase error:', error);
      throw error;
    }

    return {
      invoiceId: data.invoice_id,
      teamId: data.team_id,
      invoiceDate: data.invoice_date ? new Date(data.invoice_date) : null,
      billingCycle: data.billing_cycle,
      qty: data.qty,
      totalAmount: data.total_amount ? parseFloat(data.total_amount) : 0, // Convert string to number
      externalSubscriptionId: data.external_subscription_id,
      paidAt: data.paid_at ? new Date(data.paid_at) : null,
      isDeleted: data.is_deleted,
    };
  }

  async updateInvoicePayment(invoiceId: string, paidAt: Date | null): Promise<Invoice | undefined> {
    const { data, error } = await supabase
      .from('invoices')
      .update({ paid_at: paidAt })
      .eq('invoice_id', invoiceId)
      .select()
      .single();

    if (error || !data) return undefined;

    return {
      invoiceId: data.invoice_id,
      teamId: data.team_id,
      invoiceDate: data.invoice_date ? new Date(data.invoice_date) : null,
      billingCycle: data.billing_cycle,
      qty: data.qty,
      totalAmount: data.total_amount ? parseFloat(data.total_amount) : 0, // Convert string to number
      externalSubscriptionId: data.external_subscription_id,
      paidAt: data.paid_at ? new Date(data.paid_at) : null,
      isDeleted: data.is_deleted,
    };
  }

  async deleteInvoice(invoiceId: string): Promise<boolean> {
    const { error } = await supabase
      .from('invoices')
      .update({ is_deleted: true })
      .eq('invoice_id', invoiceId);

    return !error;
  }

  async getScreenSubscriptionsByTeamId(teamId: string): Promise<any[]> {
    // Use separate queries approach to avoid join issues
    // First, get screen registrations that are not trial and have invoice_id
    const { data: registrationsData, error: registrationsError } = await supabase
      .from('screen_registrations')
      .select('screen_id, subscription_status, billing_cycle, invoice_id')
      .eq('is_deleted', false)
      .neq('subscription_status', 'trial')
      .not('invoice_id', 'is', null);

    if (registrationsError) {
      console.error('[getScreenSubscriptionsByTeamId] Error fetching registrations:', registrationsError);
      return [];
    }

    if (!registrationsData || registrationsData.length === 0) {
      return [];
    }

    // Get unique invoice IDs and screen IDs
    const invoiceIds = Array.from(new Set(registrationsData.map(reg => reg.invoice_id)));
    const screenIds = Array.from(new Set(registrationsData.map(reg => reg.screen_id)));

    // Get invoices for this team that are paid
    const { data: invoicesData, error: invoicesError } = await supabase
      .from('invoices')
      .select('invoice_id, team_id, invoice_date, paid_at, is_deleted')
      .eq('team_id', teamId)
      .not('paid_at', 'is', null)
      .eq('is_deleted', false)
      .in('invoice_id', invoiceIds);

    if (invoicesError) {
      console.error('[getScreenSubscriptionsByTeamId] Error fetching invoices:', invoicesError);
      return [];
    }

    // Get screens for this team
    const { data: screensData, error: screensError } = await supabase
      .from('screens')
      .select('id, name, location, code, is_deleted')
      .eq('team_id', teamId)
      .eq('is_deleted', false)
      .in('id', screenIds);

    if (screensError) {
      console.error('[getScreenSubscriptionsByTeamId] Error fetching screens:', screensError);
      return [];
    }

    if (!invoicesData || !screensData) {
      return [];
    }

    // Combine the data
    return registrationsData
      .map(registration => {
        const invoice = invoicesData.find(inv => inv.invoice_id === registration.invoice_id);
        const screen = screensData.find(scr => scr.id === registration.screen_id);

        // Only include if we have both invoice and screen data
        if (!invoice || !screen) {
          return null;
        }

        return {
          screenName: screen.name,
          screenLocation: screen.location,
          screenCode: screen.code,
          subscriptionStatus: registration.subscription_status,
          billingCycle: registration.billing_cycle,
          invoiceId: invoice.invoice_id,
          invoiceDate: invoice.invoice_date ? new Date(invoice.invoice_date) : null,
        };
      })
      .filter(item => item !== null); // Remove null entries
  }

  async getAvailableScreensForSubscription(teamId: string): Promise<any[]> {
    // Use separate queries approach to avoid join issues
    // First, get all screen registrations that are available for subscription
    const { data: registrationsData, error: registrationsError } = await supabase
      .from('screen_registrations')
      .select('screen_id, trial_ends_at, billing_cycle, subscription_status')
      .eq('is_deleted', false);

    if (registrationsError) {
      console.error('[getAvailableScreensForSubscription] Error fetching registrations:', registrationsError);
      return [];
    }

    if (!registrationsData || registrationsData.length === 0) {
      return [];
    }

    // Filter registrations that are available for subscription
    const availableRegistrations = registrationsData.filter(registration => {
      // Include if subscription status is 'trial'
      if (registration.subscription_status === 'trial') {
        return true;
      }

      // Include if subscription has expired (trial_ends_at <= current date)
      if (registration.trial_ends_at) {
        const trialEndsAt = new Date(registration.trial_ends_at);
        const now = new Date();
        if (trialEndsAt <= now) {
          return true;
        }
      }

      return false;
    });

    if (availableRegistrations.length === 0) {
      return [];
    }

    // Get screen IDs for available registrations
    const screenIds = availableRegistrations.map(reg => reg.screen_id);

    // Get screen details for these screen IDs
    const { data: screensData, error: screensError } = await supabase
      .from('screens')
      .select('id, name, location, code')
      .eq('team_id', teamId)
      .eq('is_deleted', false)
      .in('id', screenIds);

    if (screensError) {
      console.error('[getAvailableScreensForSubscription] Error fetching screens:', screensError);
      return [];
    }

    if (!screensData) {
      return [];
    }

    // Combine screen data with registration data
    return screensData.map(screen => {
      const registration = availableRegistrations.find(reg => reg.screen_id === screen.id);
      return {
        screenId: screen.id,
        screenName: screen.name,
        screenLocation: screen.location,
        screenCode: screen.code,
        trialEndsAt: registration?.trial_ends_at ? new Date(registration.trial_ends_at) : null,
        billingCycle: registration?.billing_cycle,
        subscriptionStatus: registration?.subscription_status,
      };
    });
  }

  async bulkUpdateScreenRegistrations(updates: {
    screenId: string;
    trialEndsAt: Date;
    subscriptionStatus: string;
    billingCycle: string;
    invoiceId: number;
  }[]): Promise<boolean> {
    try {
      // Perform bulk update using Promise.all for efficiency
      const updatePromises = updates.map(update =>
        supabase
          .from('screen_registrations')
          .update({
            trial_ends_at: update.trialEndsAt.toISOString(),
            subscription_status: update.subscriptionStatus,
            billing_cycle: update.billingCycle,
            invoice_id: update.invoiceId,
          })
          .eq('screen_id', update.screenId)
          .eq('is_deleted', false)
      );

      const results = await Promise.all(updatePromises);

      // Check if any update failed
      return results.every(result => !result.error);
    } catch (error) {
      console.error('Bulk update failed:', error);
      return false;
    }
  }

  // Campaign Log Summary methods for Content Engagement
  async getCampaignLogSummary(teamId: string, timeRange: 'week' | 'month' | 'year'): Promise<any[]> {
    const now = new Date();
    let startDate: Date;

    // Calculate date range based on timeRange
    switch (timeRange) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    const { data, error } = await supabase
      .from('campaign_log_summary_mv')
      .select('*')
      .eq('team_id', teamId)
      .gte('log_date', startDate.toISOString().split('T')[0])
      .order('log_date', { ascending: true });

    if (error) {
      console.error('Error fetching campaign log summary:', error);
      return [];
    }

    return data || [];
  }

  async getCampaignSummaryTimeseries(teamId: string, startDate: string, endDate: string, option: 'week' | 'month' | 'year'): Promise<any[]> {
    const { data, error } = await supabase.rpc('get_campaign_summary_timeseries', {
      input_team_id: teamId,
      input_start_date: startDate,
      input_end_date: endDate,
      input_option: option
    });

    if (error) {
      return [];
    }

    return data || [];
  }

  // Get campaign media items with media details for reports
  async getCampaignMediaItems(campaignId: string): Promise<any[]> {
    // First get campaign_medias for this campaign
    const { data: campaignMedias, error: campaignMediasError } = await supabase
      .from('campaign_medias')
      .select('media_id')
      .eq('campaign_id', campaignId)
      .eq('campaign_type', 0); // Only media items, not slides

    if (campaignMediasError || !campaignMedias || campaignMedias.length === 0) {
      console.error('Error fetching campaign medias or no media found:', campaignMediasError);
      return [];
    }

    // Extract media IDs
    const mediaIds = campaignMedias.map(cm => cm.media_id);

    // Then get the actual media items
    const { data: mediaItems, error: mediaItemsError } = await supabase
      .from('media_items')
      .select('id, name, thumbnail_url')
      .in('id', mediaIds);

    if (mediaItemsError) {
      console.error('Error fetching media items:', mediaItemsError);
      return [];
    }

    return mediaItems?.map(item => ({
      id: item.id,
      name: item.name,
      thumbnail: item.thumbnail_url
    })) || [];
  }

  // Get campaign summary for reports using RPC function
  async getCampaignSummary(campaignId: string, mediaId: string | null, teamId: string, startDate: string, endDate: string): Promise<any> {
    const { data, error } = await supabase.rpc('get_campaign_summary', {
      _campaignid: campaignId,
      _mediaid: mediaId,
      _teamid: teamId,
      _startdate: startDate,
      _enddate: endDate
    });

    if (error) {
      console.error('Error calling get_campaign_summary RPC:', error);
      throw error;
    }

    return data;
  }

  // Get screen performance for reports using RPC function
  async getScreenPerformance(teamId: string, startDate: string, endDate: string): Promise<any> {
    const { data, error } = await supabase.rpc('get_screen_performance', {
      p_team_id: teamId,
      p_reportstartdate: startDate,
      p_reportenddate: endDate
    });

    if (error) {
      console.error('Error calling get_screen_performance RPC:', error);
      throw error;
    }

    return data;
  }

  // Get screen activities for reports using separate queries
  async getScreenActivities(screenId: string, startDate: string, endDate: string): Promise<any> {
    console.log('Fetching screen activities for:', { screenId, startDate, endDate });

    try {
      // First get the screen details
      const { data: screenData, error: screenError } = await supabase
        .from('screens')
        .select('name, location')
        .eq('id', screenId)
        .single();

      if (screenError) {
        console.error('Error fetching screen details:', screenError);
        throw screenError;
      }

      console.log('Screen found:', screenData);

      // Then get the activities for this screen
      const { data: activitiesData, error: activitiesError } = await supabase
        .from('screen_activities')
        .select('log_details, log_datetime')
        .eq('screen_id', screenId)
        .gte('log_datetime', startDate + ' 00:00:00')
        .lte('log_datetime', endDate + ' 23:59:59')
        .order('log_datetime', { ascending: false });

      if (activitiesError) {
        console.error('Error fetching screen activities:', activitiesError);
        throw activitiesError;
      }

      console.log('Activities found:', activitiesData?.length || 0);

      // Transform the data to match the expected format
      return activitiesData?.map(activity => ({
        screen_name: screenData.name,
        screen_location: screenData.location,
        log_details: activity.log_details,
        log_datetime: activity.log_datetime
      })) || [];

    } catch (error) {
      console.error('Error in getScreenActivities:', error);
      throw error;
    }
  }
}

export const storage = new DatabaseStorage();
