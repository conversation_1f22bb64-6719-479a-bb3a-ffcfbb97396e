-- Fix invoice table defaults - ensure they are properly set

-- Check current defaults
SELECT column_name, column_default, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'invoices' 
AND column_name IN ('invoice_date', 'paid_at', 'is_deleted');

-- Remove any existing defaults first, then add them back
ALTER TABLE invoices ALTER COLUMN invoice_date DROP DEFAULT;
ALTER TABLE invoices ALTER COLUMN paid_at DROP DEFAULT;

-- Add the defaults back with proper syntax
ALTER TABLE invoices ALTER COLUMN invoice_date SET DEFAULT NOW();
ALTER TABLE invoices ALTER COLUMN paid_at SET DEFAULT NOW();

-- Verify the defaults are now set
SELECT column_name, column_default, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'invoices' 
AND column_name IN ('invoice_date', 'paid_at', 'is_deleted');

-- Test with a sample insert (replace team_id with actual value)
-- INSERT INTO invoices (team_id, billing_cycle, qty, total_amount, external_subscription_id, is_deleted)
-- VALUES (
--     'c3a76e6d-2f37-4b41-b5bc-acc9f244b439',
--     'monthly',
--     5,
--     50.00,
--     'TEST456',
--     false
-- ) RETURNING *;
