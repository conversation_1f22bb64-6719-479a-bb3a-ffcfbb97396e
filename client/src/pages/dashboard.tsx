import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { StatsCard } from "@/components/dashboard/stats-card";
import { ContentEngagementChart } from "@/components/dashboard/content-engagement-chart";
import { CampaignItem } from "@/components/dashboard/campaign-item";
import { ScreenStatus } from "@/components/dashboard/screen-status";
import { ActivityItem } from "@/components/dashboard/activity-item";
import { useAuth } from "@/hooks/use-auth";
import { useCampaigns } from "@/hooks/use-campaigns";
import { supabase } from "@/lib/supabase";

import {
  Monitor,
  Presentation,
  Image,
  Play,
  CloudUpload,
  PlayCircle,
  Computer,
  UserPlus
} from "lucide-react";

// Dashboard stats interface
interface DashboardStats {
  recent_screen_online_count: number;
  recent_screen_offline_count: number;
  active_campaign_count: number;
  media_item_count: number;
  media_storage_gb: number;
  total_monthly_play_count: number;
}

// Recent activity interface
interface RecentActivity {
  recentactivity: string;
  status: string;
  activitytype: 'campaign' | 'media' | 'screens';
}

// Custom hook for dashboard stats
function useDashboardStats(teamId: string) {
  return useQuery<DashboardStats>({
    queryKey: [`dashboard-stats-${teamId}`],
    queryFn: async () => {
      if (!teamId) throw new Error("Team ID is required");

      const { data, error } = await supabase.rpc('count_team_dashboard_stats', {
        input_team_id: teamId,
        current_time_utc: new Date().toISOString()
      });

      if (error) {
        console.error('Error fetching dashboard stats:', error);
        throw error;
      }

      return data?.[0] || {
        recent_screen_online_count: 0,
        recent_screen_offline_count: 0,
        active_campaign_count: 0,
        media_item_count: 0,
        media_storage_gb: 0,
        total_monthly_play_count: 0
      };
    },
    enabled: !!teamId,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

// Custom hook for recent activity
function useRecentActivity(teamId: string) {
  return useQuery<RecentActivity[]>({
    queryKey: [`recent-activity-${teamId}`],
    queryFn: async () => {
      if (!teamId) throw new Error("Team ID is required");

      const { data, error } = await supabase.rpc('get_recent_activity', {
        teamid_param: teamId
      });

      if (error) {
        console.error('Error fetching recent activity:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!teamId,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // Refetch every minute
  });
}

export default function Dashboard() {
  const { user } = useAuth();
  const [teamId, setTeamId] = useState<string>("");

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });

  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  // Use the new dashboard stats hook
  const { data: dashboardStats, isLoading: statsLoading } = useDashboardStats(teamId);

  // Use the new recent activity hook
  const { data: recentActivity, isLoading: activityLoading } = useRecentActivity(teamId);

  // Still need campaigns for the active campaigns section
  const { campaigns, isLoading: campaignsLoading } = useCampaigns(teamId);
  const activeCampaigns = campaigns?.filter(c => c.status === "active") || [];

  const isLoading = statsLoading || campaignsLoading || activityLoading;

  // Helper function to get activity item props based on activity type
  const getActivityItemProps = (activity: RecentActivity) => {
    switch (activity.activitytype) {
      case 'campaign':
        return {
          icon: PlayCircle,
          iconBgColor: "bg-green-100",
          iconColor: "text-green-500",
          message: <><span className="font-medium">Campaign:</span> {activity.recentactivity} started</>,
          timestamp: activity.status
        };
      case 'media':
        return {
          icon: CloudUpload,
          iconBgColor: "bg-blue-100",
          iconColor: "text-blue-500",
          message: <><span className="font-medium">Uploaded</span> {activity.recentactivity}</>,
          timestamp: activity.status
        };
      case 'screens':
        return {
          icon: Computer,
          iconBgColor: "bg-amber-100",
          iconColor: "text-amber-500",
          message: <><span className="font-medium">Screen:</span> {activity.recentactivity} connected</>,
          timestamp: activity.status
        };
      default:
        return {
          icon: UserPlus,
          iconBgColor: "bg-purple-100",
          iconColor: "text-purple-500",
          message: activity.recentactivity,
          timestamp: activity.status
        };
    }
  };

  if (!teamId && !isLoading) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] p-8">
          <h2 className="text-2xl font-bold mb-4">Welcome to AdLoopr</h2>
          <p className="text-muted-foreground text-center max-w-md mb-6">
            You don't have any teams yet. Please contact your administrator to get started.
          </p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatsCard
          title="Active Screens"
          value={dashboardStats?.recent_screen_online_count || 0}
          icon={<Monitor />}
          iconBgColor="bg-blue-50"
          iconColor="text-blue-500"
        />

        <StatsCard
          title="Active Campaigns"
          value={dashboardStats?.active_campaign_count || 0}
          icon={<Presentation />}
          iconBgColor="bg-purple-50"
          iconColor="text-purple-500"
        />

        <StatsCard
          title="Media Files"
          value={dashboardStats?.media_item_count || 0}
          icon={<Image />}
          iconBgColor="bg-amber-50"
          iconColor="text-amber-500"
          subtitle={`${dashboardStats?.media_storage_gb || 0} MB used`}
        />

        <StatsCard
          title="Total Plays"
          value={(dashboardStats?.total_monthly_play_count || 0).toLocaleString()}
          icon={<Play />}
          iconBgColor="bg-green-50"
          iconColor="text-green-500"
        />
      </div>

      {/* Content Engagement Chart */}
      <div className="mb-8">
        <ContentEngagementChart teamId={teamId} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Active Campaigns */}
        <div className="lg:col-span-2 space-y-6">
          <h3 className="text-lg font-semibold">Active Campaigns</h3>

          {activeCampaigns.length === 0 ? (
            <div className="bg-white rounded-lg shadow p-8 text-center">
              <Presentation className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No active campaigns</h3>
              <p className="text-muted-foreground mb-4">
                Create a campaign to start displaying content on your screens.
              </p>
              <a href="/campaigns" className="text-secondary font-medium">
                Create Campaign
              </a>
            </div>
          ) : (
            <div className="space-y-4">
              {activeCampaigns.slice(0, 3).map((campaign) => (
                <CampaignItem
                  key={campaign.id}
                  name={campaign.name}
                  startDate={campaign.startDate}
                  endDate={campaign.endDate}
                  status={"active"}
                  screenCount={5} // Example data
                  mediaCount={3} // Example data
                />
              ))}
            </div>
          )}
        </div>

        {/* Screen Status & Recent Activity */}
        <div className="space-y-8">
          {/* Screen Status */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Screen Status</h3>
            <ScreenStatus
              online={dashboardStats?.recent_screen_online_count || 0}
              offline={dashboardStats?.recent_screen_offline_count || 0}
            />
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>

            <div className="space-y-4">
              {recentActivity && recentActivity.length > 0 ? (
                recentActivity.map((activity, index) => {
                  const props = getActivityItemProps(activity);
                  return (
                    <ActivityItem
                      key={index}
                      icon={props.icon}
                      iconBgColor={props.iconBgColor}
                      iconColor={props.iconColor}
                      message={props.message}
                      timestamp={props.timestamp}
                    />
                  );
                })
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No recent activity</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
