// Execute this script to set up the Supabase database
import fs from 'node:fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupDatabase() {
  try {
    console.log('Starting database setup...');
    
    // Read the SQL file
    const sql = fs.readFileSync('./supabase_setup.sql', 'utf8');
    
    // Split the SQL into individual statements
    const statements = sql
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1} of ${statements.length}...`);
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement });
        if (error) throw error;
      } catch (err) {
        console.error(`Error executing statement ${i + 1}:`, err.message);
        console.log('Continuing with the next statement...');
      }
    }
    
    console.log('Database setup completed successfully.');
    
    // Create storage bucket
    console.log('Creating storage bucket...');
    const { error: bucketError } = await supabase.storage.createBucket('medialibrary', {
      public: false,
      allowedMimeTypes: ['image/*', 'video/*'],
      fileSizeLimit: 1024 * 1024 * 100 // 100MB
    });
    
    if (bucketError) {
      console.error('Error creating storage bucket:', bucketError.message);
      if (bucketError.message.includes('already exists')) {
        console.log('Bucket already exists, continuing...');
      }
    } else {
      console.log('Storage bucket created successfully.');
    }
    
    // Create a test user in the auth system
    console.log('Creating test user...');
    const { data: userData, error: userError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'password123',
      email_confirm: true // Auto-confirm the email
    });
    
    if (userError) {
      console.error('Error creating test user:', userError.message);
      if (userError.message.includes('already exists')) {
        console.log('User already exists, continuing...');
      }
    } else {
      console.log('Test user created successfully.');
      
      // Create profile for the test user
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: userData.user.id,
          email: userData.user.email,
          first_name: 'Demo',
          last_name: 'User'
        });
      
      if (profileError) {
        console.error('Error creating user profile:', profileError.message);
      } else {
        console.log('User profile created successfully.');
      }
      
      // Create a team for the test user
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .insert({
          name: 'Demo Company'
        })
        .select();
      
      if (teamError) {
        console.error('Error creating team:', teamError.message);
      } else {
        console.log('Team created successfully.');
        
        // Add the user to the team
        const { error: memberError } = await supabase
          .from('team_members')
          .insert({
            profile_id: userData.user.id,
            team_id: teamData[0].id,
            role: 'owner'
          });
        
        if (memberError) {
          console.error('Error adding user to team:', memberError.message);
        } else {
          console.log('User added to team successfully.');
        }
      }
    }
    
    console.log('Setup completed!');
  } catch (error) {
    console.error('Error during database setup:', error.message);
  }
}

setupDatabase().catch(console.error);