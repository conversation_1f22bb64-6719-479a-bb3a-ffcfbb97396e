Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Constructed URL from query key segments: api/profiles/b83d6c00-48f1-4db7-8c83-5d73a6676a42/teams original queryKey: 
(3) ['/api/profiles', 'b83d6c00-48f1-4db7-8c83-5d73a6676a42', 'teams']
Console.js:61 Teams data loaded: undefined
Console.js:61 No teams available
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 0}
Console.js:61 TeamId changed, resetting initialization state
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 0}
Console.js:61 Teams data loaded: 
[{…}]
Console.js:61 Setting teamId to: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/tags original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'tags']
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 TeamId changed, resetting initialization state
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 10
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: true, hasInitialized: true, isDataChanging: false, prevDataLength: 10, currentDataLength: 10
Console.js:61 Media page render state: 
{isLoading: true, mediaItemsLength: 0}
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: 10, currentDataLength: 10
Console.js:61 Media page render state: 
{isLoading: false, mediaItemsLength: 10}
﻿

