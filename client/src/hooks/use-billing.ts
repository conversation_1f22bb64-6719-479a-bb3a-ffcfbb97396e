import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Invoice, Pricing } from "@shared/schema";

export interface BillingSummary {
  totalCreditsBought: number;
  totalCreditsUsed: number;
  creditsAvailable: number;
}

export interface ScreenSubscription {
  screenName: string;
  screenLocation: string;
  screenCode: string;
  subscriptionStatus: string;
  billingCycle: string;
  invoiceId: number;
  invoiceDate: Date | null;
}

export interface AvailableScreen {
  screenId: string;
  screenName: string;
  screenLocation: string;
  screenCode: string;
  trialEndsAt: Date | null;
  billingCycle: string;
  subscriptionStatus: string;
}

export function useBilling(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get pricing options
  const {
    data: pricing,
    isLoading: pricingLoading,
    error: pricingError,
  } = useQuery<Pricing[]>({
    queryKey: ['/api/pricing'],
    enabled: true,
  });

  // Get billing summary
  const {
    data: billingSummary,
    isLoading: summaryLoading,
    error: summaryError,
    refetch: refetchSummary,
  } = useQuery<BillingSummary>({
    queryKey: [`/api/teams/${teamId}/billing-summary`],
    enabled: !!teamId,
  });

  // Get invoices
  const {
    data: invoices,
    isLoading: invoicesLoading,
    error: invoicesError,
    refetch: refetchInvoices,
  } = useQuery<Invoice[]>({
    queryKey: [`/api/teams/${teamId}/invoices`],
    enabled: !!teamId,
  });

  // Get screen subscriptions
  const {
    data: screenSubscriptions,
    isLoading: subscriptionsLoading,
    error: subscriptionsError,
    refetch: refetchSubscriptions,
  } = useQuery<ScreenSubscription[]>({
    queryKey: [`/api/teams/${teamId}/screen-subscriptions`],
    enabled: !!teamId,
  });

  // Get available screens for subscription
  const {
    data: availableScreens,
    isLoading: availableScreensLoading,
    error: availableScreensError,
    refetch: refetchAvailableScreens,
  } = useQuery<AvailableScreen[]>({
    queryKey: [`/api/teams/${teamId}/available-screens`],
    enabled: !!teamId,
  });

  // Create invoice mutation
  const createInvoiceMutation = useMutation({
    mutationFn: async (invoiceData: {
      teamId: string;
      billingCycle: string;
      qty: number;
      totalAmount: number;
      externalSubscriptionId: string;
    }) => {
      console.log('Creating invoice with data:', invoiceData); // Debug log
      const requestData = {
        teamId: invoiceData.teamId,
        invoiceDate: new Date().toISOString(), // Send ISO string
        billingCycle: invoiceData.billingCycle,
        qty: invoiceData.qty,
        totalAmount: invoiceData.totalAmount,
        externalSubscriptionId: invoiceData.externalSubscriptionId,
        paidAt: new Date().toISOString(), // Send ISO string (immediate payment)
        isDeleted: false, // Now properly boolean
      };
      return apiRequest("POST", "/api/invoices", requestData);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
      // Refetch all billing data
      refetchSummary();
      refetchInvoices();
      refetchSubscriptions();
    },
    onError: (error) => {
      console.error('Failed to create invoice:', error); // Debug log
      toast({
        title: "Error",
        description: "Failed to create invoice",
        variant: "destructive",
      });
    },
  });

  // Update invoice payment mutation
  const updateInvoicePaymentMutation = useMutation({
    mutationFn: async ({ invoiceId, paidAt }: { invoiceId: string; paidAt: Date | null }) => {
      return apiRequest("PATCH", `/api/invoices/${invoiceId}/payment`, {
        paidAt: paidAt ? paidAt.toISOString() : null
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Payment status updated successfully",
      });
      refetchInvoices();
      refetchSummary();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update payment status",
        variant: "destructive",
      });
    },
  });

  // Delete invoice mutation
  const deleteInvoiceMutation = useMutation({
    mutationFn: async (invoiceId: string) => {
      return apiRequest("DELETE", `/api/invoices/${invoiceId}`);
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Invoice deleted successfully",
      });
      refetchInvoices();
      refetchSummary();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete invoice",
        variant: "destructive",
      });
    },
  });

  // Bulk update screen registrations mutation
  const bulkUpdateScreenRegistrationsMutation = useMutation({
    mutationFn: async (updates: {
      screenId: string;
      trialEndsAt: Date;
      subscriptionStatus: string;
      billingCycle: string;
      invoiceId: number;
    }[]) => {
      return apiRequest("POST", "/api/screen-registrations/bulk-update", {
        updates: updates.map(update => ({
          ...update,
          trialEndsAt: update.trialEndsAt.toISOString(),
        }))
      });
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Screen subscriptions updated successfully",
      });
      refetchInvoices();
      refetchSummary();
      refetchSubscriptions();
      refetchAvailableScreens();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update screen subscriptions",
        variant: "destructive",
      });
    },
  });

  // Helper function to calculate next billing date
  const calculateNextBillingDate = (invoiceDate: Date | null, billingCycle: string): Date | null => {
    if (!invoiceDate) return null;

    const date = new Date(invoiceDate);

    switch (billingCycle?.toLowerCase()) {
      case 'monthly':
        date.setDate(date.getDate() + 30);
        break;
      case '6 months':
        date.setDate(date.getDate() + 180);
        break;
      case 'yearly':
        date.setDate(date.getDate() + 365);
        break;
      default:
        return null;
    }

    return date;
  };

  // Helper function to generate invoice ID
  const generateInvoiceId = (teamName: string, invoiceId: number): string => {
    const firstWord = teamName.split(' ')[0];
    return `${firstWord}-${invoiceId}`;
  };

  return {
    // Data
    pricing: pricing || [],
    billingSummary,
    invoices: invoices || [],
    screenSubscriptions: screenSubscriptions || [],
    availableScreens: availableScreens || [],

    // Loading states
    pricingLoading,
    summaryLoading,
    invoicesLoading,
    subscriptionsLoading,
    availableScreensLoading,

    // Errors
    pricingError,
    summaryError,
    invoicesError,
    subscriptionsError,
    availableScreensError,

    // Mutations
    createInvoice: createInvoiceMutation.mutate,
    updateInvoicePayment: updateInvoicePaymentMutation.mutate,
    deleteInvoice: deleteInvoiceMutation.mutate,
    bulkUpdateScreenRegistrations: bulkUpdateScreenRegistrationsMutation.mutate,

    // Loading states for mutations
    isCreatingInvoice: createInvoiceMutation.isPending,
    isUpdatingPayment: updateInvoicePaymentMutation.isPending,
    isDeletingInvoice: deleteInvoiceMutation.isPending,
    isBulkUpdating: bulkUpdateScreenRegistrationsMutation.isPending,

    // Refetch functions
    refetchSummary,
    refetchInvoices,
    refetchSubscriptions,
    refetchAvailableScreens,

    // Helper functions
    calculateNextBillingDate,
    generateInvoiceId,
  };
}
