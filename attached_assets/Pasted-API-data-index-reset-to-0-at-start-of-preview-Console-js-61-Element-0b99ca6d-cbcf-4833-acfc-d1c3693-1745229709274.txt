API data index reset to 0 at start of preview
Console.js:61 Element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a has API mapping: true 
{productname: true}
Console.js:61 Element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a has data field: true productname
Console.js:61 Found 1 API elements for data cycling
Console.js:61 Using top-level array with 23 records
Console.js:61 Processing data array with 23 records at index 0
Console.js:61 Updating element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a with data record at index 0/22 (global index: 0)
Console.js:61 Updating text element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a with field "productname" at index 0 (global: 0)
Console.js:61 Field value: "Jarritos Tutti Fruttiiiiii"
Console.js:61 Updated text element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a with "Jarritos <PERSON>iiii" (index: 0)
Console.js:61 Setting up API data cycling interval with 5s duration
Console.js:61 API data cycling interval set with ID: 197
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Preview active: Looking for video elements to play
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 0b99ca6d-cbcf-4833-acfc-d1c36930e35a
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 API cycling interval triggered - checking if preview still active
Console.js:61 Preview no longer active, will stop on next interval check
Console.js:61 API cycling interval triggered - checking if preview still active
Console.js:61 Preview marked as inactive, stopping API data cycling interval