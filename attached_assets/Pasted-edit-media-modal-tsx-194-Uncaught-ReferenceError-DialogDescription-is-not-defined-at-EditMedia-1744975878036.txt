edit-media-modal.tsx:194 Uncaught ReferenceError: DialogDescription is not defined
    at EditMediaModal (edit-media-modal.tsx:194:12)

Console.js:61 useMedia hook called with teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439 valid: true
Console.js:61 MediaQuery state: 
{isLoading: false, isFetching: false, isError: false, error: null, data: Array(3), …}
edit-media-modal.tsx:194 Uncaught ReferenceError: DialogDescription is not defined
    at EditMediaModal (edit-media-modal.tsx:194:12)
Console.js:61 The above error occurred in the <EditMediaModal> component:

    at EditMediaModal (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/media/edit-media-modal.tsx?t=1744975697463:39:3)
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Media (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/media.tsx?t=1744975697463:50:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1744975697463&v=Wmd7G0g6zGm7fQV9aI0WI:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=090e15c5:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=090e15c5:247:17)
    at App
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=090e15c5:2805:3)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-276SZO74.js?v=090e15c5:9129 Uncaught ReferenceError: DialogDescription is not defined
    at EditMediaModal (edit-media-modal.tsx:194:12)
chunk-276SZO74.js?v=090e15c5:3750 Uncaught ReferenceError: DialogDescription is not defined
    at EditMediaModal (edit-media-modal.tsx:194:12)
