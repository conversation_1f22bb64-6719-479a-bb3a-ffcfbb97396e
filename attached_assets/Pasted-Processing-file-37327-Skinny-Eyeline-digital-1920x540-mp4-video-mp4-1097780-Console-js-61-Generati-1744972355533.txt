Processing file: 37327 Skinny Eyeline digital 1920x540.mp4 video/mp4 1097780
Console.js:61 Generating video thumbnail...
Console.js:61 Starting video thumbnail generation...
Console.js:61 Video metadata loaded 1920 x 540
Console.js:61 Video seeked to position: 1
Console.js:61 Attempting to capture video frame...
Console.js:61 Generated video frame thumbnail
Console.js:61 File object to upload: 
{id: 'df44265c-0ea0-4d7d-a531-4b869a06cb2b', name: '37327 Skinny Eyeline digital 1920x540.mp4', size: 1097780, type: 'video/mp4'}
Console.js:61 Processing video file thumbnail...
Console.js:61 Using pre-generated thumbnail blob from preview step
Console.js:61 Uploading video thumbnail to: c3a76e6d-2f37-4b41-b5bc-acc9f244b439/37327 Skinny Eyeline digital 1920x540-thumb.jpg
Console.js:61 Could not extract video metadata from file
Console.js:61 Video thumbnail URL: https://oatyudgnhndxrwwlbxsj.supabase.co/storage/v1/object/public/medialibr…-b5bc-acc9f244b439/37327%20Skinny%20Eyeline%20digital%201920x540-thumb.jpg
Console.js:61 Creating media item: 
{teamId: 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', name: '37327 Skinny Eyeline digital 1920x540.mp4', description: null, fileUrl: 'https://oatyudgnhndxrwwlbxsj.supabase.co/storage/v…37327%20Skinny%20Eyeline%20digital%201920x540.mp4', thumbnailUrl: 'https://oatyudgnhndxrwwlbxsj.supabase.co/storage/v…20Skinny%20Eyeline%20digital%201920x540-thumb.jpg', …}
Console.js:61 Error during file uploads: TypeError: Failed to execute 'fetch' on 'Window': '/api/media' is not a valid HTTP method.
    at apiRequest (queryClient.ts:15:21)
    at upload-modal.tsx:616:41
    at async Promise.all (29810b14-7e88-452d-9…ev/__replco/index 0)
    at async uploadFiles (upload-modal.tsx:671:25)
Network.js:219 Uncaught (in promise) TypeError: Failed to execute 'fetch' on 'Window': '/api/media' is not a valid HTTP method.
    at apiRequest (queryClient.ts:15:21)
    at upload-modal.tsx:616:41
    at async Promise.all (29810b14-7e88-452d-9…ev/__replco/index 0)
    at async uploadFiles (upload-modal.tsx:671:25)
