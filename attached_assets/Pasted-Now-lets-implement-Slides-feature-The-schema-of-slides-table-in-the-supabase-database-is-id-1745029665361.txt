Now lets implement Slides feature.

-The schema of "slides" table in the supabase database is  "id", "team_id", "name", "content", "thumbnail_url", "created_at", "updated_at", "slide_width", "slide_height", "slide_background_color", "api_url", "api_data_preview_duration", "isdeleted". Please modify shared/schema.ts.
- When I click on the "Slide Designer" menu from the sidebar, getting following errors;

Uncaught TypeError: Cannot read properties of undefined (reading 'startsWith')
    at toolbox.tsx:181:34
    at Array.map (<anonymous>)
    at Toolbox (toolbox.tsx:176:29)

eruda.js:sourcemap:2 The above error occurred in the <Toolbox> component:

    at Toolbox (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/toolbox.tsx?t=1745029000498:40:27)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745029000498:55:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745029000498&v=0BbbKXyoFD7jbN7NmOl3o:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=090e15c5:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=090e15c5:247:17)
    at App
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=090e15c5:2805:3)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-276SZO74.js?v=090e15c5:19413 Uncaught TypeError: Cannot read properties of undefined (reading 'startsWith')
    at toolbox.tsx:181:34
    at Array.map (<anonymous>)
    at Toolbox (toolbox.tsx:176:29)
