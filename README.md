# Digital Signage Manager

A comprehensive digital signage content management system built with React, Express, and Supabase.

## Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- A Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd DigitalSignageManager
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase credentials**

   ⚠️ **IMPORTANT**: You must configure Supabase credentials before running the application.

   - Copy the environment template:
     ```bash
     cp .env.example .env
     ```

   - Follow the detailed setup guide: [SUPABASE_SETUP.md](./SUPABASE_SETUP.md)

4. **Start the application**

   For development:
   ```bash
   npm run dev
   ```

   For production:
   ```bash
   npm run build
   npm start
   ```

5. **Access the application**

   Open your browser and navigate to `http://localhost:5000`

## Features

- **User Management**: Authentication and user profiles
- **Team Management**: Multi-tenant team support
- **Media Library**: Upload and manage media files
- **Screen Management**: Configure and monitor digital screens
- **Campaign Management**: Create and schedule content campaigns
- **Slide Designer**: Visual editor for creating custom slides
- **Real-time Updates**: Live content synchronization

## Project Structure

```
├── client/          # React frontend application
├── server/          # Express backend API
├── shared/          # Shared types and schemas
├── dist/            # Built application files
└── supabase_*.sql   # Database setup scripts
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run check` - Type checking
- `npm run db:push` - Push database schema changes

## Environment Variables

Required environment variables (see `.env.example`):

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anon/public key
- `DATABASE_URL` - PostgreSQL connection string (optional)

## Troubleshooting

### "Missing Supabase credentials" Error

This error occurs when the required Supabase environment variables are not set. Follow these steps:

1. Ensure you have a `.env` file in the project root
2. Check that `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are set
3. Verify the values are correct (no extra spaces or quotes)
4. After updating `.env`, rebuild the application: `npm run build`
5. See [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) for detailed instructions

**Note**: If you update your `.env` file, you must rebuild the application for production mode (`npm run build`) as the frontend is statically built with the environment variables.

### Application Won't Start

1. Check that all dependencies are installed: `npm install`
2. Verify Node.js version: `node --version` (should be v18+)
3. Ensure port 5000 is available
4. Check the console for specific error messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Enhance useScreen Hook

1. We need to fetch following fields from the database for each screen;
   - "health" field from the "screens" table
   - MAX value of "file_download_count" and MAX value of "total_file_download" fields from the "screen_activities" table for the selected screen
   - "trial_ends_at", "subscription_status", "lemonslemonsqueezy_subscription_id" and "billing_cycle" fields from the "screen_registrations" table

2. If the "subscription_status" is "trial";
   - Show a "Trial" badge in red color on the screen card next to the "Online" or "Offline" badge
   - If the "trial_ends_at" is in the past;
      - disable "Configure" and "Campaigns" buttons on the screen card
      - disable "Screen Options" --> "Configure" menu.

## Screen Details Modal Window
1. Click on the "View Screen" menu to launch the modal window.
2. The modal window will display the screen details.
3. You can close the modal window by clicking on the "Close" button.
4. The modal window title will display the screen name & code.
5. There will be 2 tabs in the modal window:
   - "Details": This tab will display the screen details.
   - "Subscriptions": This tab will display the subscriptions associated with the screen.
6. "Details" tab will display the following information:
   - On the Left side of the tab, read the 'health' field of the screen (already fetched via useScreen hook) and display the details as labels. The 'health' field is a json field and contains the following information;
     
     Android Device Data:
     
     - App Version: 0.1.0
     - Build Number: 1
     - Package Name: com.app.signage
     - Memory Total: 7901040
     - Memory Free: 229696
     - Memory Available: 2996748
     - Memory Used: 7671344
     - Memory Usage Percentage: 97.09
     - Internal Storage Total: 111G
     - Internal Storage Used: 67G
     - Internal Storage Available: 44G
     - Internal Storage Usage Percentage: 61
     - External Storage Total: 111G
     - External Storage Used: 68G
     - External Storage Available: 44G

     Desktop Device Data Sample;

      - App Version: 0.1.0
      - Build Number: 1
      - Package Name: com.app.signage
      - CPU Usage Percentage: 26.99
      - Memory Total: 7517436
      - Memory Free: 254892
      - Memory Used: 7262544
      - Memory Usage Percentage: 96.61
      - Disk Filesystem: /dev/sda1
      - Disk Total: 121399984
      - Disk Used: 49022120
      - Disk Available: 66164900
      - Disk Usage Percentage: 43
      - Platform: Linux

      Show these information in some meaningful way.

   - On the Right side of the tab;
      - Header should be "File Download Progress"
      - read the value of 'file_download_count' and 'total_file_download' retrieved via useScreen hookfor the selected screen and display the information as a circular progressbar showing file download progress.
      - Inside the circular progressbar, show the "file_download_count" of "total_file_download" values as labels. i.e. 10 of 100

7. "Subscriptions" tab
   - The "Subscriptions" tab will display the subscriptions associated with the screen.
   - You can add a new subscription by clicking on the "Add Subscription" button.

## License

MIT License - see LICENSE file for details
