import { useState, useEffect, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON><PERSON>, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CardContent, Card } from "@/components/ui/card";
import { ColorPicker } from "@/components/ui/color-picker";
import { useMedia } from "@/hooks/use-media";
import { 
  AlignLeft, 
  AlignCenter, 
  AlignRight, 
  Bold, 
  Italic, 
  Underline,
  Trash2,
  MoveRight,
  ZoomIn,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Play,
  ArrowRight,
  ArrowLeft,
  ArrowUp,
  ArrowDown,
  Eye,
  Image,
  Video,
  Plus,
  MinusCircle,
  Gauge as GaugeIcon,
  X,
  ChevronUp,
  ChevronDown,
  Clock
} from "lucide-react";
import { CanvasElement } from "./canvas";
import { cn } from "@/lib/utils";
import { PlaylistControls } from "./playlist-controls";

// Helper function to determine the default tab based on element type
const getDefaultTab = (element: CanvasElement | null): string => {
  if (!element) return "style";
  
  switch (element.type) {
    case 'playlist':
      return "playlist";
    case 'multimedia':
      return "position";
    case 'api':
      return "style";
    case 'text':
    case 'shape':
    case 'date':
      return "style";
    default:
      return "style";
  }
};

interface ElementPropertiesProps {
  selectedElement: CanvasElement | null;
  onUpdateElement: (id: string, updates: Partial<CanvasElement>) => void;
  onDeleteElement: (id: string) => void;
  apiData?: any;
  apiPreviewDuration?: number;
}

export function ElementProperties({
  selectedElement,
  onUpdateElement,
  onDeleteElement,
  apiData,
  apiPreviewDuration,
}: ElementPropertiesProps) {
  const [localContent, setLocalContent] = useState<any>(null);
  const [localStyle, setLocalStyle] = useState<any>(null);
  // Add state for the active tab
  const [activeTab, setActiveTab] = useState<string>("style");

  // Track previous element ID to only update tab when element changes
  const prevElementIdRef = useRef<string | null>(null);
  
  useEffect(() => {
    if (selectedElement) {
      // Initialize content with default values if they don't exist
      const content = selectedElement.content || {};
      
      // Only update the active tab when a different element is selected
      if (prevElementIdRef.current !== selectedElement.id) {
        setActiveTab(getDefaultTab(selectedElement));
        prevElementIdRef.current = selectedElement.id;
      }
      
      // Add defensive coding to handle missing properties based on element type
      switch (selectedElement.type) {
        case 'text':
          if (!content.text) {
            content.text = 'Text';
          }
          if (!content.style) {
            content.style = {};
          }
          break;
        case 'date':
          if (!content.options) {
            content.options = { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            };
          }
          if (!content.style) {
            content.style = {};
          }
          break;
        case 'shape':
          if (!content.shape) {
            content.shape = 'rectangle';
          }
          if (!content.fill) {
            content.fill = '#f3f4f6';
          }
          if (content.strokeWidth === undefined) {
            content.strokeWidth = 0;
          }
          if (!content.stroke) {
            content.stroke = '#000000';
          }
          break;
        case 'image':
          if (!content.src) {
            content.src = '';
          }
          if (!content.alt) {
            content.alt = '';
          }
          break;
        case 'video':
          if (!content.src) {
            content.src = '';
          }
          break;
        default:
          break;
      }
      
      setLocalContent(content);
      setLocalStyle(selectedElement.style || {});
    } else {
      setLocalContent(null);
      setLocalStyle(null);
    }
  }, [selectedElement]);
  
  // Update Value Label when API data changes for gauge elements
  useEffect(() => {
    console.log("API data useEffect triggered");
    console.log("API data:", apiData);
    console.log("Selected element type:", selectedElement?.type);
    console.log("localContent subtype:", localContent?.subtype);
    console.log("localContent dataField:", localContent?.dataField);
    
    if (selectedElement && 
        selectedElement.type === 'api' && 
        localContent?.subtype === 'api.gauge' && 
        localContent?.dataField && 
        apiData) {
      
      // Helper function to get nested value from path
      const getNestedValue = (obj: any, path: string): any => {
        if (!path || !obj) return undefined;
        
        try {
          const keys = path.split('.');
          let current = obj;
          
          for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            if (current === undefined || current === null) return undefined;
            
            if (Array.isArray(current)) {
              // If array, take first item
              current = current[0];
              // Continue with this key on the first array item
              i--;
            } else {
              current = current[key];
            }
          }
          
          return current;
        } catch (error) {
          console.error(`Error getting nested value at path: ${path}`, error);
          return undefined;
        }
      };
      
      // Get value from complex nested path if needed
      const fieldValue = getNestedValue(apiData, localContent.dataField);
      
      if (fieldValue !== undefined) {
        console.log("Updating valueLabel with API data:", fieldValue.toString());
        
        // Update the valueLabel property to show the actual API data value
        updateContent({ 
          valueLabel: fieldValue.toString() 
        });
      }
    }
  }, [apiData, selectedElement?.id, localContent?.dataField]);

  if (!selectedElement) {
    return (
      <div className="w-64 border-l bg-background h-full flex flex-col overflow-hidden">
        <div className="p-4 border-b shrink-0">
          <h3 className="font-medium">Properties</h3>
        </div>
        <div className="p-4 text-center text-muted-foreground flex-1 overflow-y-auto">
          Select an element to edit its properties
        </div>
      </div>
    );
  }

  const updateContent = (updates: any) => {
    const newContent = { ...localContent, ...updates };
    setLocalContent(newContent);
    onUpdateElement(selectedElement.id, { content: newContent });
  };

  const updateStyle = (updates: any) => {
    const newStyle = { ...localStyle, ...updates };
    setLocalStyle(newStyle);
    onUpdateElement(selectedElement.id, { style: newStyle });
  };

  const updateTextStyle = (updates: any) => {
    if (!localContent) return;
    const currentStyle = localContent.style || {};
    const newTextStyle = { ...currentStyle, ...updates };
    updateContent({ style: newTextStyle });
  };

  const renderPositionControls = () => {
    if (!selectedElement) return null;
    
    // Ensure we're displaying rounded values
    const roundedX = Math.round(selectedElement.x || 0);
    const roundedY = Math.round(selectedElement.y || 0);
    const roundedWidth = Math.round(selectedElement.width || 100);
    const roundedHeight = Math.round(selectedElement.height || 100);
    
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-2">
            <Label>X Position</Label>
            <Input 
              type="number" 
              value={roundedX} 
              onChange={(e) => onUpdateElement(selectedElement.id, { x: parseInt(e.target.value) || 0 })}
            />
          </div>
          <div className="space-y-2">
            <Label>Y Position</Label>
            <Input 
              type="number" 
              value={roundedY} 
              onChange={(e) => onUpdateElement(selectedElement.id, { y: parseInt(e.target.value) || 0 })}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <div className="space-y-2">
            <Label>Width</Label>
            <Input 
              type="number" 
              value={roundedWidth} 
              onChange={(e) => onUpdateElement(selectedElement.id, { width: parseInt(e.target.value) || 100 })}
            />
          </div>
          <div className="space-y-2">
            <Label>Height</Label>
            <Input 
              type="number" 
              value={roundedHeight} 
              onChange={(e) => onUpdateElement(selectedElement.id, { height: parseInt(e.target.value) || 100 })}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderTextControls = () => {
    if (!selectedElement || !localContent || (selectedElement.type !== 'text' && selectedElement.type !== 'date')) return null;
    
    // Ensure style object exists
    const style = localContent.style || {};
    
    return (
      <div className="space-y-4">
        {selectedElement.type === 'text' && (
          <div className="space-y-2">
            <Label>Text Content</Label>
            <Input
              value={localContent.text || ''}
              onChange={(e) => updateContent({ text: e.target.value })}
            />
          </div>
        )}
        
        <div className="space-y-2">
          <Label>Font Family</Label>
          <Select
            value={style.fontFamily || 'Inter, sans-serif'}
            onValueChange={(value) => updateTextStyle({ fontFamily: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a font" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Inter, sans-serif">Inter</SelectItem>
              <SelectItem value="Arial, sans-serif">Arial</SelectItem>
              <SelectItem value="Georgia, serif">Georgia</SelectItem>
              <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
              <SelectItem value="Verdana, sans-serif">Verdana</SelectItem>
              <SelectItem value="Trebuchet MS, sans-serif">Trebuchet MS</SelectItem>
              <SelectItem value="Courier New, monospace">Courier New</SelectItem>
              <SelectItem value="Tahoma, sans-serif">Tahoma</SelectItem>
              <SelectItem value="Palatino, serif">Palatino</SelectItem>
              <SelectItem value="Helvetica, sans-serif">Helvetica</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label>Font Size</Label>
          <Input
            type="text"
            value={style.fontSize || '16px'}
            onChange={(e) => updateTextStyle({ fontSize: e.target.value })}
          />
        </div>
        
        <div className="space-y-2">
          <ColorPicker
            label="Text Color"
            value={style.color || '#000000'}
            onChange={(value) => updateTextStyle({ color: value })}
          />
        </div>
        
        <div className="space-y-2">
          <Label>Horizontal Alignment</Label>
          <div className="flex gap-2 mt-1">
            <Button
              variant={style.textAlign === 'left' ? 'default' : 'outline'}
              size="sm"
              className="flex-1"
              onClick={() => updateTextStyle({ textAlign: 'left' })}
            >
              <AlignLeft className="h-4 w-4" />
            </Button>
            <Button
              variant={style.textAlign === 'center' ? 'default' : 'outline'}
              size="sm"
              className="flex-1"
              onClick={() => updateTextStyle({ textAlign: 'center' })}
            >
              <AlignCenter className="h-4 w-4" />
            </Button>
            <Button
              variant={style.textAlign === 'right' ? 'default' : 'outline'}
              size="sm"
              className="flex-1"
              onClick={() => updateTextStyle({ textAlign: 'right' })}
            >
              <AlignRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Vertical Alignment</Label>
          <Select
            value={style.verticalAlign || 'middle'}
            onValueChange={(value) => updateTextStyle({ verticalAlign: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select alignment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="top">Top</SelectItem>
              <SelectItem value="middle">Middle</SelectItem>
              <SelectItem value="bottom">Bottom</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <ColorPicker
            label="Background Color"
            value={style.backgroundColor || 'transparent'}
            onChange={(value) => updateTextStyle({ backgroundColor: value })}
          />
        </div>
        
        <div className="flex gap-2 mt-2">
          <Button
            variant={style.fontWeight === 'bold' ? 'default' : 'outline'}
            size="sm"
            className="flex-1"
            onClick={() => updateTextStyle({ fontWeight: style.fontWeight === 'bold' ? 'normal' : 'bold' })}
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant={style.fontStyle === 'italic' ? 'default' : 'outline'}
            size="sm"
            className="flex-1"
            onClick={() => updateTextStyle({ fontStyle: style.fontStyle === 'italic' ? 'normal' : 'italic' })}
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant={style.textDecoration === 'underline' ? 'default' : 'outline'}
            size="sm"
            className="flex-1"
            onClick={() => updateTextStyle({ textDecoration: style.textDecoration === 'underline' ? 'none' : 'underline' })}
          >
            <Underline className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  };

  const renderShapeControls = () => {
    if (!selectedElement || !localContent || selectedElement.type !== 'shape') return null;
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Shape Type</Label>
          <Select
            value={localContent.shape || 'rectangle'}
            onValueChange={(value) => updateContent({ shape: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a shape" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="rectangle">Rectangle</SelectItem>
              <SelectItem value="circle">Circle</SelectItem>
              <SelectItem value="triangle">Triangle</SelectItem>
              <SelectItem value="pentagon">Pentagon</SelectItem>
              <SelectItem value="hexagon">Hexagon</SelectItem>
              <SelectItem value="octagon">Octagon</SelectItem>
              <SelectItem value="oval">Oval</SelectItem>
              <SelectItem value="semicircle">Semicircle</SelectItem>
              <SelectItem value="star">Star</SelectItem>
              <SelectItem value="heart">Heart</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <ColorPicker
            label="Fill Color"
            value={localContent.fill || '#f3f4f6'}
            onChange={(value) => updateContent({ fill: value })}
          />
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Border Width</Label>
            <span className="text-xs text-muted-foreground">{localContent.strokeWidth || 0}px</span>
          </div>
          <Input
            type="range"
            min="0"
            max="10"
            value={localContent.strokeWidth || 0}
            onChange={(e) => updateContent({ strokeWidth: parseInt(e.target.value) })}
          />
        </div>
        
        {(localContent.strokeWidth || 0) > 0 && (
          <div className="space-y-2">
            <ColorPicker
              label="Border Color"
              value={localContent.stroke || '#000000'}
              onChange={(value) => updateContent({ stroke: value })}
            />
          </div>
        )}
      </div>
    );
  };
  
  const renderAnimationControls = () => {
    if (!selectedElement) return null;
    
    // Get current animation settings or use defaults
    const animation = selectedElement.animation || {
      type: 'none',
      duration: 1000,
      delay: 0,
      easing: 'easeOut'
    };
    
    // Update animation settings
    const updateAnimation = (updates: Partial<typeof animation>) => {
      const newAnimation = { ...animation, ...updates };
      onUpdateElement(selectedElement.id, { animation: newAnimation });
    };
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Animation Type</Label>
          <Select
            value={animation.type}
            onValueChange={(value: any) => updateAnimation({ 
              type: value,
              // Reset direction if not needed for chosen animation
              direction: ['slide-in'].includes(value) ? (animation.direction || 'left') : undefined
            })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select animation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">
                <div className="flex items-center">
                  <Play className="mr-2 h-4 w-4" />
                  <span>None</span>
                </div>
              </SelectItem>
              <SelectItem value="fade-in">
                <div className="flex items-center">
                  <Eye className="mr-2 h-4 w-4" />
                  <span>Fade In</span>
                </div>
              </SelectItem>
              <SelectItem value="slide-in">
                <div className="flex items-center">
                  <MoveRight className="mr-2 h-4 w-4" />
                  <span>Slide In</span>
                </div>
              </SelectItem>
              <SelectItem value="zoom-in">
                <div className="flex items-center">
                  <ZoomIn className="mr-2 h-4 w-4" />
                  <span>Zoom In</span>
                </div>
              </SelectItem>
              <SelectItem value="bounce">
                <div className="flex items-center">
                  <ArrowDown className="mr-2 h-4 w-4" />
                  <span>Bounce</span>
                </div>
              </SelectItem>
              <SelectItem value="rotate">
                <div className="flex items-center">
                  <RotateCw className="mr-2 h-4 w-4" />
                  <span>Rotate</span>
                </div>
              </SelectItem>
              <SelectItem value="flip-x">
                <div className="flex items-center">
                  <FlipHorizontal className="mr-2 h-4 w-4" />
                  <span>Flip-X</span>
                </div>
              </SelectItem>
              <SelectItem value="flip-y">
                <div className="flex items-center">
                  <FlipVertical className="mr-2 h-4 w-4" />
                  <span>Flip-Y</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {animation.type === 'slide-in' && (
          <div className="space-y-2">
            <Label>Direction</Label>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant={animation.direction === 'left' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ direction: 'left' })}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Left
              </Button>
              <Button
                variant={animation.direction === 'right' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ direction: 'right' })}
              >
                <ArrowRight className="mr-2 h-4 w-4" />
                Right
              </Button>
              <Button
                variant={animation.direction === 'top' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ direction: 'top' })}
              >
                <ArrowUp className="mr-2 h-4 w-4" />
                Top
              </Button>
              <Button
                variant={animation.direction === 'bottom' ? 'default' : 'outline'}
                size="sm"
                onClick={() => updateAnimation({ direction: 'bottom' })}
              >
                <ArrowDown className="mr-2 h-4 w-4" />
                Bottom
              </Button>
            </div>
          </div>
        )}
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Duration</Label>
            <span className="text-xs text-muted-foreground">{animation.duration}ms</span>
          </div>
          <Input
            type="range"
            min="100"
            max="5000"
            step="100"
            value={animation.duration}
            onChange={(e) => updateAnimation({ duration: parseInt(e.target.value) })}
          />
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label>Delay</Label>
            <span className="text-xs text-muted-foreground">{animation.delay}ms</span>
          </div>
          <Input
            type="range"
            min="0"
            max="5000"
            step="100"
            value={animation.delay}
            onChange={(e) => updateAnimation({ delay: parseInt(e.target.value) })}
          />
        </div>
        
        <div className="space-y-2">
          <Label>Easing</Label>
          <Select
            value={animation.easing || 'easeOut'}
            onValueChange={(value) => updateAnimation({ easing: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select easing" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="linear">Linear</SelectItem>
              <SelectItem value="easeIn">Ease In</SelectItem>
              <SelectItem value="easeOut">Ease Out</SelectItem>
              <SelectItem value="easeInOut">Ease In Out</SelectItem>
              <SelectItem value="circIn">Circular In</SelectItem>
              <SelectItem value="circOut">Circular Out</SelectItem>
              <SelectItem value="circInOut">Circular In Out</SelectItem>
              <SelectItem value="backIn">Back In</SelectItem>
              <SelectItem value="backOut">Back Out</SelectItem>
              <SelectItem value="backInOut">Back In Out</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {animation.type !== 'none' && (
          <div className="pt-2">
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => {
                console.log("Preview Animation clicked");
                
                // Create a temporary animation to preview it
                // Make sure we have all the required properties for the animation object
                const currentAnimation = { 
                  ...selectedElement.animation,
                  type: selectedElement.animation?.type || 'none',
                  duration: selectedElement.animation?.duration || 1000,
                  delay: selectedElement.animation?.delay || 0
                };
                
                // First, remove the animation completely
                onUpdateElement(selectedElement.id, { animation: undefined });
                
                // Wait a frame to ensure React has updated the DOM
                requestAnimationFrame(() => {
                  // Then apply a new animation with a unique key to force the animation to replay
                  const resetAnimation = {
                    ...currentAnimation,
                    _previewKey: Date.now() // Add a unique key to force re-render
                  };
                  
                  console.log("Applying animation with key:", resetAnimation._previewKey);
                  onUpdateElement(selectedElement.id, { animation: resetAnimation });
                  
                  // After animation completes, remove the preview key (but keep the animation settings)
                  const animationDuration = currentAnimation.duration + currentAnimation.delay + 200; // add buffer
                  setTimeout(() => {
                    console.log("Animation preview complete, removing _previewKey");
                    const finalAnimation = { ...currentAnimation };
                    onUpdateElement(selectedElement.id, { animation: finalAnimation });
                  }, animationDuration);
                });
              }}
            >
              <Play className="mr-2 h-4 w-4" />
              Preview Animation
            </Button>
          </div>
        )}
      </div>
    );
  };

  const renderGaugeSegmentControls = () => {
    if (!selectedElement || !localContent || selectedElement.type !== 'api' || localContent.subtype !== 'api.gauge') return null;
    
    // Ensure segments, colors, and labels are initialized
    const segments = localContent.segments || 4;
    const segmentColors = localContent.segmentColors || ['#00BFFF', '#89EC5B', '#FFC371','#FF5F6D'];
    const customSegmentLabels = localContent.customSegmentLabels || [];
    const minValue = localContent.minValue || 0;
    const maxValue = localContent.maxValue || 100;
    
    // Use custom segment stops if available, otherwise generate equally spaced stops
    let stops = [];
    
    if (localContent.segmentStops && localContent.segmentStops.length === segments - 1) {
      // Use the custom segment stops
      stops = [...localContent.segmentStops];
    } else {
      // Generate stops based on segment count
      const segmentStep = (maxValue - minValue) / segments;
      stops = Array.from({ length: segments - 1 }).map((_, index) => {
        return minValue + (index + 1) * segmentStep;
      });
    }
    
    // Add the maxValue as the final stop (not editable)
    stops.push(maxValue);
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Segment Configuration</Label>
          </div>
          <p className="text-xs text-muted-foreground">
            Configure colors and labels for each gauge segment
          </p>
        </div>
        
        {/* Segments list */}
        <div className="space-y-3">
          {Array.from({ length: segments }).map((_, index) => {
            // Calculate the stop value for this segment
            const stopValue = stops[index];
            
            // Get or create the segment label
            const segmentLabel = customSegmentLabels[index] || {
              text: stopValue.toString(),
              position: "INSIDE",
              color: "#666"
            };
            
            // Get or create the segment color
            const segmentColor = segmentColors[index] || '#00BFFF';
            
            return (
              <Card key={index} className="overflow-hidden">
                <CardContent className="p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Segment {index + 1}</Label>
                    <div className="text-xs text-muted-foreground">
                      {index === 0 ? minValue : stops[index-1]} - {stopValue}
                    </div>
                  </div>
                  
                  {/* Segment Text */}
                  <div className="space-y-1">
                    <Label className="text-xs">Label Text</Label>
                    <Input
                      value={segmentLabel.text}
                      onChange={(e) => {
                        // Update the label text for this segment
                        const updatedLabels = [...customSegmentLabels];
                        
                        // Make sure the label exists
                        if (!updatedLabels[index]) {
                          updatedLabels[index] = {
                            text: stopValue.toString(),
                            position: "INSIDE",
                            color: "#666"
                          };
                        }
                        
                        // Update the text
                        updatedLabels[index].text = e.target.value;
                        
                        // Update the content
                        updateContent({ customSegmentLabels: updatedLabels });
                      }}
                    />
                  </div>
                  
                  {/* Current Value Text for this segment */}
                  <div className="space-y-1">
                    <Label className="text-xs">Current Value Text</Label>
                    <Input
                      value={segmentLabel.currentValueText || ""}
                      onChange={(e) => {
                        // Update the current value text for this segment
                        const updatedLabels = [...customSegmentLabels];
                        
                        // Make sure the label exists
                        if (!updatedLabels[index]) {
                          updatedLabels[index] = {
                            text: stopValue.toString(),
                            position: "INSIDE",
                            color: "#666"
                          };
                        }
                        
                        // Update the current value text
                        updatedLabels[index].currentValueText = e.target.value;
                        
                        // Update the content
                        updateContent({ customSegmentLabels: updatedLabels });
                      }}
                      placeholder={`Text for values in segment ${index + 1}`}
                    />
                    <p className="text-xs text-muted-foreground">
                      Text to display when value falls in this segment.
                    </p>
                  </div>
                  
                  {/* Segment Color */}
                  <div className="space-y-1">
                    <Label className="text-xs">Segment Background Color</Label>
                    <ColorPicker
                      value={segmentColor}
                      onChange={(value) => {
                        // Update the color for this segment
                        const updatedColors = [...segmentColors];
                        updatedColors[index] = value;
                        
                        // Update the content
                        updateContent({ segmentColors: updatedColors });
                      }}
                    />
                  </div>
                  
                  {/* Segment Text Color */}
                  <div className="space-y-1">
                    <Label className="text-xs">Label Text Color</Label>
                    <ColorPicker
                      value={segmentLabel.color || "#666"}
                      onChange={(value) => {
                        // Update the label color for this segment
                        const updatedLabels = [...customSegmentLabels];
                        
                        // Make sure the label exists
                        if (!updatedLabels[index]) {
                          updatedLabels[index] = {
                            text: stopValue.toString(),
                            position: "INSIDE",
                            color: "#666"
                          };
                        }
                        
                        // Update the color
                        updatedLabels[index].color = value;
                        
                        // Update the content
                        updateContent({ customSegmentLabels: updatedLabels });
                      }}
                    />
                  </div>
                  
                  {/* Segment Stop Value - only for segments except the last one */}
                  {index < segments - 1 && (
                    <div className="space-y-1">
                      <Label className="text-xs">Segment Stop</Label>
                      <Input
                        type="number"
                        min={index === 0 ? minValue : stops[index - 1]}
                        max={maxValue}
                        value={stopValue}
                        onChange={(e) => {
                          const newValue = parseFloat(e.target.value);
                          
                          // Validate the value
                          if (isNaN(newValue)) return;
                          
                          // Check if the value is within the valid range
                          const minLimit = index === 0 ? minValue : stops[index - 1];
                          const maxLimit = maxValue;
                          
                          if (newValue <= minLimit || newValue >= maxLimit) {
                            // Invalid range, don't update
                            return;
                          }
                          
                          // Update segment stops
                          const updatedStops = [...(localContent.segmentStops || [])];
                          
                          // If segment stops array doesn't exist or has wrong length, create it from current stops
                          if (!updatedStops.length || updatedStops.length !== segments - 1) {
                            // We need to initialize from current stops (excluding maxValue)
                            for (let i = 0; i < segments - 1; i++) {
                              updatedStops[i] = stops[i];
                            }
                          }
                          
                          // Update the stop value for this segment
                          updatedStops[index] = newValue;
                          
                          // Update the content and force a re-render by adding a timestamp
                          updateContent({ 
                            segmentStops: updatedStops,
                            forceRender: Date.now() // Add timestamp to force re-render
                          });
                        }}
                      />
                      <p className="text-xs text-muted-foreground">
                        The value where this segment ends (must be between {index === 0 ? minValue : stops[index - 1]} and {maxValue})
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    );
  };

  const renderApiControls = () => {
    if (!selectedElement || !localContent || selectedElement.type !== 'api') return null;
    
    return (
      <div className="space-y-4">
        {/* Data Type Selector */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Data Type</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={localContent.subtype === 'api.text' ? 'default' : 'outline'}
              size="sm"
              className="flex items-center justify-start space-x-2 py-4"
              onClick={() => updateContent({ subtype: 'api.text' })}
            >
              <span className="text-sm">Text</span>
            </Button>
            <Button
              variant={localContent.subtype === 'api.image' ? 'default' : 'outline'}
              size="sm"
              className="flex items-center justify-start space-x-2 py-4"
              onClick={() => updateContent({ subtype: 'api.image' })}
            >
              <Image className="h-4 w-4 mr-2" />
              <span className="text-sm">Image</span>
            </Button>
            <Button
              variant={localContent.subtype === 'api.video' ? 'default' : 'outline'}
              size="sm"
              className="flex items-center justify-start space-x-2 py-4"
              onClick={() => updateContent({ subtype: 'api.video' })}
            >
              <Video className="h-4 w-4 mr-2" />
              <span className="text-sm">Video</span>
            </Button>
            <Button
              variant={localContent.subtype === 'api.gauge' ? 'default' : 'outline'}
              size="sm"
              className="flex items-center justify-start space-x-2 py-4"
              onClick={() => {
                // Calculate default segment stops
                const minValue = 0;
                const maxValue = 100;
                const segments = 4;
                const segmentStep = (maxValue - minValue) / segments;
                
                // Create segment stops (exclude maxValue which is handled separately)
                const segmentStops = [];
                for (let i = 1; i < segments; i++) {
                  segmentStops.push(minValue + i * segmentStep);
                }
                
                updateContent({ 
                  subtype: 'api.gauge', 
                  minValue: minValue,
                  maxValue: maxValue,
                  segments: segments,
                  needleColor: "#4682B4",
                  ringWidth: 60,
                  textColor: "#666",
                  labelFontSize: 32,
                  valueTextFontSize: 48,
                  valueTextFontWeight: "bold",
                  paddingHorizontal: 15,
                  paddingVertical: 15,
                  segmentColors: ['#00BFFF', '#89EC5B', '#FFC371','#FF5F6D'],
                  segmentStops: segmentStops,
                  customSegmentLabels: [],
                  currentValueText: '',
                  fluidWidth: true,
                  forceRender: true
                });
              }}
            >
              <GaugeIcon className="h-4 w-4 mr-2" />
              <span className="text-sm">Gauge</span>
            </Button>
          </div>
        </div>
        
        {/* Data Mapping */}
        <div className="space-y-1">
          <Label className="text-sm font-medium">Data Mapping</Label>
          <Select
            value={localContent.dataField || '_none'}
            onValueChange={(value) => {
              if (value === '_none') {
                updateContent({ dataField: '' });
              } else {
                // When selecting a data field for gauge, also update the valueLabel
                // so the API value is displayed properly
                if (localContent.subtype === 'api.gauge' && apiData && apiData[value] !== undefined) {
                  updateContent({ 
                    dataField: value,
                    valueLabel: apiData[value].toString()
                  });
                } else {
                  updateContent({ dataField: value });
                }
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select data field" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="_none">None</SelectItem>
              {window.__API_DATA_FIELDS__ && window.__API_DATA_FIELDS__.length > 0 ? (
                window.__API_DATA_FIELDS__.map((field: string) => (
                  <SelectItem key={field} value={field}>
                    {field}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="_no_data">No API data available</SelectItem>
              )}
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground mt-1">
            The field from the API response to display in this element
          </p>
        </div>
        
        {/* Preview Placeholder URL for image and video subtypes */}
        {(localContent.subtype === 'api.image' || localContent.subtype === 'api.video') && (
          <div className="space-y-1">
            <Label className="text-sm font-medium">Preview Placeholder URL</Label>
            <Input
              value={localContent.placeholderUrl || ''}
              onChange={(e) => updateContent({ placeholderUrl: e.target.value })}
              placeholder={`https://example.com/images/{${localContent.dataField || 'field'}}`}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Use {`{field_name}`} to insert API values. Example: https://openweathermap.org/img/wn/{`{weather.icon}`}@2x.png
            </p>
          </div>
        )}
        
        {/* Gauge Settings (for api.gauge subtype) */}
        {localContent.subtype === 'api.gauge' && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Gauge Settings</Label>
            
            {/* Min Value */}
            <div className="space-y-1">
              <Label className="text-xs">Min Value</Label>
              <Input
                type="number"
                value={localContent.minValue !== undefined ? localContent.minValue : 0}
                onChange={(e) => {
                  const minValue = Number(e.target.value);
                  const maxValue = localContent.maxValue || 100;
                  const segments = localContent.segments || 4;
                  
                  // Regenerate segment stops based on new min value
                  const segmentStep = (maxValue - minValue) / segments;
                  const segmentStops = [];
                  for (let i = 1; i < segments; i++) {
                    segmentStops.push(minValue + i * segmentStep);
                  }
                  
                  updateContent({ 
                    minValue: minValue,
                    segmentStops: segmentStops,
                    forceRender: Date.now() // Add timestamp to force re-render
                  });
                }}
              />
            </div>
            
            {/* Max Value */}
            <div className="space-y-1">
              <Label className="text-xs">Max Value</Label>
              <Input
                type="number"
                value={localContent.maxValue !== undefined ? localContent.maxValue : 100}
                onChange={(e) => {
                  const maxValue = Number(e.target.value);
                  const minValue = localContent.minValue || 0;
                  const segments = localContent.segments || 4;
                  
                  // Regenerate segment stops based on new max value
                  const segmentStep = (maxValue - minValue) / segments;
                  const segmentStops = [];
                  for (let i = 1; i < segments; i++) {
                    segmentStops.push(minValue + i * segmentStep);
                  }
                  
                  updateContent({ 
                    maxValue: maxValue,
                    segmentStops: segmentStops,
                    forceRender: Date.now() // Add timestamp to force re-render
                  });
                }}
              />
            </div>
            
            {/* Segments Count */}
            <div className="space-y-1">
              <Label className="text-xs">Segments</Label>
              <Input
                type="number"
                value={localContent.segments !== undefined ? localContent.segments : 4}
                onChange={(e) => {
                  const segmentCount = Number(e.target.value);
                  if (segmentCount <= 0) return;
                  
                  // Update segment colors and labels as needed
                  const currentColors = localContent.segmentColors || [];
                  const currentLabels = localContent.customSegmentLabels || [];
                  
                  // Generate default segment colors if needed
                  const defaultColors = ['#00BFFF', '#89EC5B', '#FFC371','#FF5F6D'];
                  const segmentColors = [...Array(segmentCount)].map((_, i) => 
                    currentColors[i] || defaultColors[i % defaultColors.length]
                  );
                  
                  // Generate default segment labels if needed
                  const customSegmentLabels = [...Array(segmentCount)].map((_, i) => {
                    // If existing label exists, use it; otherwise create a default one
                    if (currentLabels[i]) return currentLabels[i];
                    
                    // Calculate stop value for this segment
                    const minValue = localContent.minValue || 0;
                    const maxValue = localContent.maxValue || 100;
                    const step = (maxValue - minValue) / segmentCount;
                    const stopValue = minValue + (i + 1) * step;
                    
                    return {
                      text: stopValue.toString(),
                      position: "INSIDE",
                      color: "#666"
                    };
                  });
                  
                  // Generate new segment stops
                  const minValue = localContent.minValue || 0;
                  const maxValue = localContent.maxValue || 100;
                  const segmentStep = (maxValue - minValue) / segmentCount;
                  
                  // Create segment stops (exclude maxValue which is handled separately)
                  const segmentStops = [];
                  for (let i = 1; i < segmentCount; i++) {
                    segmentStops.push(minValue + i * segmentStep);
                  }
                  
                  updateContent({ 
                    segments: segmentCount,
                    segmentColors,
                    customSegmentLabels,
                    segmentStops: segmentStops,
                    forceRender: Date.now() // Add timestamp to force re-render
                  });
                }}
              />
            </div>
            
            {/* Needle Color */}
            <div className="space-y-1">
              <Label className="text-xs">Needle Color</Label>
              <ColorPicker
                value={localContent.needleColor || "#4682B4"}
                onChange={(value) => updateContent({ needleColor: value })}
              />
            </div>
            
            {/* Ring Width */}
            <div className="space-y-1">
              <Label className="text-xs">Ring Width</Label>
              <Input
                type="number"
                value={localContent.ringWidth !== undefined ? localContent.ringWidth : 60}
                onChange={(e) => updateContent({ ringWidth: Number(e.target.value) })}
              />
            </div>
            
            {/* Text Color */}
            <div className="space-y-1">
              <Label className="text-xs">Text Color</Label>
              <ColorPicker
                value={localContent.textColor || "#666"}
                onChange={(value) => updateContent({ textColor: value })}
              />
            </div>
            
            {/* Value Text Font Size */}
            <div className="space-y-1">
              <Label className="text-xs">Value Text Font Size</Label>
              <Input
                type="number"
                value={localContent.valueTextFontSize !== undefined ? localContent.valueTextFontSize : 48}
                onChange={(e) => updateContent({ valueTextFontSize: Number(e.target.value) })}
              />
            </div>
            
            {/* Label Font Size */}
            <div className="space-y-1">
              <Label className="text-xs">Label Font Size</Label>
              <Input
                type="number"
                value={localContent.labelFontSize !== undefined ? localContent.labelFontSize : 32}
                onChange={(e) => updateContent({ labelFontSize: Number(e.target.value) })}
              />
            </div>
            
            {/* Value Text Font Weight */}
            <div className="space-y-1">
              <Label className="text-xs">Value Text Font Weight</Label>
              <Select
                value={localContent.valueTextFontWeight || "bold"}
                onValueChange={(value) => updateContent({ valueTextFontWeight: value })}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select font weight" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="bold">Bold</SelectItem>
                  <SelectItem value="italic">Italic</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Padding Horizontal */}
            <div className="space-y-1">
              <Label className="text-xs">Padding Horizontal</Label>
              <Input
                type="number"
                value={localContent.paddingHorizontal !== undefined ? localContent.paddingHorizontal : 15}
                onChange={(e) => updateContent({ paddingHorizontal: Number(e.target.value) })}
              />
            </div>
            
            {/* Padding Vertical */}
            <div className="space-y-1">
              <Label className="text-xs">Padding Vertical</Label>
              <Input
                type="number"
                value={localContent.paddingVertical !== undefined ? localContent.paddingVertical : 15}
                onChange={(e) => updateContent({ paddingVertical: Number(e.target.value) })}
              />
            </div>
            
            {/* Value Label */}
            <div className="space-y-1">
              <Label className="text-xs">Value Label</Label>
              <Input
                value={
                  // Get the formatted value from the nested path if data mapping is used
                  (localContent.dataField && apiData) 
                    ? (() => {
                        // Helper function to get nested value
                        const getNestedValue = (obj: any, path: string): any => {
                          if (!path || !obj) return undefined;
                          
                          try {
                            const keys = path.split('.');
                            let current = obj;
                            
                            for (let i = 0; i < keys.length; i++) {
                              const key = keys[i];
                              if (current === undefined || current === null) return undefined;
                              
                              if (Array.isArray(current)) {
                                current = current[0];
                                i--;
                              } else {
                                current = current[key];
                              }
                            }
                            
                            return current;
                          } catch (error) {
                            return undefined;
                          }
                        };
                        
                        const value = getNestedValue(apiData, localContent.dataField);
                        return value !== undefined ? value.toString() : (localContent.valueLabel || "Value");
                      })()
                    : (localContent.valueLabel || "Value")
                }
                onChange={(e) => updateContent({ valueLabel: e.target.value })}
                placeholder="Enter value label or select data mapping"
              />
              <p className="text-xs text-muted-foreground">
                The actual data value from the API response will be displayed here
              </p>
            </div>
          </div>
        )}
        
        {/* Text Style Controls (for api.text subtype) */}
        {localContent.subtype === 'api.text' && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">Text Style</Label>
            
            {/* Font Family */}
            <div>
              <Label className="text-xs">Font Family</Label>
              <Select
                value={localContent.style?.fontFamily || 'Inter, sans-serif'}
                onValueChange={(value) => {
                  const style = localContent.style || {};
                  updateContent({ style: { ...style, fontFamily: value } });
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Font" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                  <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                  <SelectItem value="Georgia, serif">Georgia</SelectItem>
                  <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                  <SelectItem value="Verdana, sans-serif">Verdana</SelectItem>
                  <SelectItem value="Trebuchet MS, sans-serif">Trebuchet MS</SelectItem>
                  <SelectItem value="Courier New, monospace">Courier New</SelectItem>
                  <SelectItem value="Tahoma, sans-serif">Tahoma</SelectItem>
                  <SelectItem value="Palatino, serif">Palatino</SelectItem>
                  <SelectItem value="Helvetica, sans-serif">Helvetica</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Font Size */}
            <div>
              <Label className="text-xs">Font Size</Label>
              <div className="flex items-center gap-2">
                <Input
                  type="text"
                  value={localContent.style?.fontSize || '16px'}
                  onChange={(e) => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, fontSize: e.target.value } });
                  }}
                />
              </div>
            </div>
            
            {/* Text Color */}
            <div>
              <Label className="text-xs">Text Color</Label>
              <ColorPicker
                value={localContent.style?.color || '#000000'}
                onChange={(value) => {
                  const style = localContent.style || {};
                  updateContent({ style: { ...style, color: value } });
                }}
              />
            </div>
            
            {/* Horizontal Alignment */}
            <div>
              <Label className="text-xs">Horizontal Alignment</Label>
              <div className="flex mt-1 gap-2">
                <Button
                  variant={localContent.style?.textAlign === 'left' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, textAlign: 'left' } });
                  }}
                >
                  <AlignLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant={localContent.style?.textAlign === 'center' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, textAlign: 'center' } });
                  }}
                >
                  <AlignCenter className="h-4 w-4" />
                </Button>
                <Button
                  variant={localContent.style?.textAlign === 'right' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, textAlign: 'right' } });
                  }}
                >
                  <AlignRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {/* Vertical Alignment */}
            <div>
              <Label className="text-xs">Vertical Alignment</Label>
              <Select
                value={localContent.style?.verticalAlign || 'middle'}
                onValueChange={(value) => {
                  const style = localContent.style || {};
                  updateContent({ style: { ...style, verticalAlign: value } });
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select alignment" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="top">Top</SelectItem>
                  <SelectItem value="middle">Middle</SelectItem>
                  <SelectItem value="bottom">Bottom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Background Color */}
            <div>
              <Label className="text-xs">Background Color</Label>
              <ColorPicker
                value={localContent.style?.backgroundColor || 'transparent'}
                onChange={(value) => {
                  const style = localContent.style || {};
                  updateContent({ style: { ...style, backgroundColor: value } });
                }}
              />
            </div>
            
            {/* Text Decoration */}
            <div>
              <Label className="text-xs">Text Style</Label>
              <div className="flex gap-2 mt-1">
                <Button
                  variant={localContent.style?.fontWeight === 'bold' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, fontWeight: style.fontWeight === 'bold' ? 'normal' : 'bold' } });
                  }}
                >
                  <Bold className="h-4 w-4" />
                </Button>
                <Button
                  variant={localContent.style?.fontStyle === 'italic' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, fontStyle: style.fontStyle === 'italic' ? 'normal' : 'italic' } });
                  }}
                >
                  <Italic className="h-4 w-4" />
                </Button>
                <Button
                  variant={localContent.style?.textDecoration === 'underline' ? 'default' : 'outline'}
                  size="sm"
                  className="flex-1"
                  onClick={() => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, textDecoration: style.textDecoration === 'underline' ? 'none' : 'underline' } });
                  }}
                >
                  <Underline className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMediaControls = () => {
    if (!selectedElement || !localContent || (
      selectedElement.type !== 'image' && 
      selectedElement.type !== 'video' && 
      selectedElement.type !== 'multimedia'
    )) return null;
    
    // Common media controls for image and video
    if (selectedElement.type === 'image' || selectedElement.type === 'video') {
      return (
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>{selectedElement.type === 'image' ? 'Image Source' : 'Video Source'}</Label>
            <Input
              value={localContent.src || ''}
              onChange={(e) => updateContent({ src: e.target.value })}
              disabled
            />
          </div>
          
          {selectedElement.type === 'image' && (
            <div className="space-y-2">
              <Label>Alt Text</Label>
              <Input
                value={localContent.alt || ''}
                onChange={(e) => updateContent({ alt: e.target.value })}
                placeholder="Describe the image"
              />
            </div>
          )}
        </div>
      );
    }
    
    // Multimedia specific controls
    if (selectedElement.type === 'multimedia') {
      // Get field data from API response if available
      let fieldData = null;
      let actualSrc = '';
      if (localContent.dataField && window.__API_DATA__) {
        try {
          // Define a recursive function to navigate complex nested paths
          const getNestedValue = (obj: any, path: string): any => {
            // Split the path into parts
            const parts = path.split('.');
            
            // Helper function to deeply access a specific path in an object
            const accessPath = (currentObj: any, pathParts: string[]): any => {
              // Base case - if we have no path parts left or the object is null/undefined
              if (!pathParts.length || !currentObj) return currentObj;
              
              const currentPart = pathParts[0];
              const remainingParts = pathParts.slice(1);
              
              // If current part doesn't exist in object
              if (currentObj[currentPart] === undefined) return undefined;
              
              // Get the value at current path part
              const value = currentObj[currentPart];
              
              // If this is an array and we have more path parts
              if (Array.isArray(value) && remainingParts.length > 0) {
                // Try each item in the array
                for (const item of value) {
                  // Recursively try to access the remaining path
                  const result = accessPath(item, remainingParts);
                  // If we found a valid result, return it
                  if (result !== undefined) return result;
                }
                
                // If we didn't find a valid item in any array element,
                // default to first array item if available
                if (value.length > 0) {
                  return accessPath(value[0], remainingParts);
                }
                
                // No valid items found in array
                return undefined;
              }
              
              // For regular properties, just continue accessing the path
              return accessPath(value, remainingParts);
            };
            
            return accessPath(obj, parts);
          };
          
          // Use our recursive function to get the value
          fieldData = getNestedValue(window.__API_DATA__, localContent.dataField);
          
          // For image and video, determine actual source URL
          if ((localContent.dataType === 'image' || localContent.dataType === 'video') && fieldData) {
            const isValidUrl = (url: string) => {
              try {
                new URL(url);
                return true;
              } catch {
                return false;
              }
            };
            
            // Check if fieldData is a string URL
            if (typeof fieldData === 'string') {
              if (isValidUrl(fieldData)) {
                actualSrc = fieldData;
              } else if (localContent.placeholderUrl) {
                actualSrc = localContent.placeholderUrl.replace(`{${localContent.dataField}}`, fieldData);
              }
            } 
            // Check if fieldData is an array of URLs (take the first one)
            else if (Array.isArray(fieldData) && fieldData.length > 0) {
              // If it's an array, use the first item
              const firstItem = fieldData[0];
              if (typeof firstItem === 'string') {
                if (isValidUrl(firstItem)) {
                  actualSrc = firstItem;
                } else if (localContent.placeholderUrl) {
                  actualSrc = localContent.placeholderUrl.replace(`{${localContent.dataField}}`, firstItem);
                }
              }
            }
          }
        } catch (error) {
          console.error('Error processing API data field:', error);
        }
      }
      
      return (
        <div className="space-y-4">
          {/* Data Type - First as requested */}
          <div className="space-y-1">
            <Label className="text-sm font-medium">Data Type</Label>
            <div className="flex items-center mt-1 space-x-4">
              <div className="flex items-center">
                <input
                  type="radio"
                  id="multimedia-text"
                  name="multimedia-type"
                  className="mr-2"
                  checked={localContent.dataType === 'text'}
                  onChange={() => updateContent({ dataType: 'text' })}
                />
                <label htmlFor="multimedia-text" className="text-sm">Text</label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="multimedia-image"
                  name="multimedia-type"
                  className="mr-2"
                  checked={localContent.dataType === 'image'}
                  onChange={() => updateContent({ dataType: 'image' })}
                />
                <label htmlFor="multimedia-image" className="text-sm">Image</label>
              </div>
              <div className="flex items-center">
                <input
                  type="radio"
                  id="multimedia-video"
                  name="multimedia-type"
                  className="mr-2"
                  checked={localContent.dataType === 'video'}
                  onChange={() => updateContent({ dataType: 'video' })}
                />
                <label htmlFor="multimedia-video" className="text-sm">Video</label>
              </div>
            </div>
          </div>
          
          {/* Data Mapping - Second as requested */}
          <div className="space-y-1">
            <Label className="text-sm font-medium">Data Mapping</Label>
            <Select
              value={localContent.dataField || ''}
              onValueChange={(value) => {
                // Initialize or update the apiMapping property when a data field is selected
                const apiMapping = localContent.apiMapping || {};
                
                // If selecting "_none", clear the mapping, otherwise set the field to true
                if (value === "_none") {
                  updateContent({ dataField: "" });
                } else {
                  // Set the mapping for this field and update the dataField
                  apiMapping[value] = true;
                  updateContent({ 
                    dataField: value,
                    apiMapping: apiMapping
                  });
                }
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select data field" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="_none">None</SelectItem>
                {window.__API_DATA_FIELDS__ && window.__API_DATA_FIELDS__.length > 0 ? (
                  window.__API_DATA_FIELDS__.map((field) => (
                    <SelectItem key={field} value={field}>
                      {field}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="_no_data">No API data available</SelectItem>
                )}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              The field from the API response to display in this element
            </p>
          </div>
          
          {/* Text styling controls for text data type */}
          {localContent.dataType === 'text' && (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Text Style</Label>
              
              {/* Font Family */}
              <div>
                <Label className="text-xs">Font Family</Label>
                <Select
                  value={localContent.style?.fontFamily || 'Inter, sans-serif'}
                  onValueChange={(value) => {
                    const style = localContent.style || {};
                    updateContent({ style: { ...style, fontFamily: value } });
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Font" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Inter, sans-serif">Inter</SelectItem>
                    <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                    <SelectItem value="Georgia, serif">Georgia</SelectItem>
                    <SelectItem value="'Times New Roman', serif">Times New Roman</SelectItem>
                    <SelectItem value="'Courier New', monospace">Courier New</SelectItem>
                    <SelectItem value="'Roboto', sans-serif">Roboto</SelectItem>
                    <SelectItem value="'Open Sans', sans-serif">Open Sans</SelectItem>
                    <SelectItem value="'Montserrat', sans-serif">Montserrat</SelectItem>
                    <SelectItem value="'Lato', sans-serif">Lato</SelectItem>
                    <SelectItem value="'Poppins', sans-serif">Poppins</SelectItem>
                    <SelectItem value="'Playfair Display', serif">Playfair Display</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {/* Font Size */}
              <div>
                <Label className="text-xs">Font Size</Label>
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    min="8"
                    max="200"
                    value={parseInt(localContent.style?.fontSize || '16')}
                    onChange={(e) => {
                      const style = localContent.style || {};
                      updateContent({ style: { ...style, fontSize: `${e.target.value}px` } });
                    }}
                    className="w-20"
                  />
                  <span className="text-sm">px</span>
                </div>
              </div>
              
              {/* Text Alignment */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Text Align</Label>
                  <div className="flex mt-1 border rounded-md overflow-hidden">
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.textAlign === 'left' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, textAlign: 'left' } });
                      }}
                    >
                      <AlignLeft className="h-4 w-4 mx-auto" />
                    </button>
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.textAlign === 'center' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, textAlign: 'center' } });
                      }}
                    >
                      <AlignCenter className="h-4 w-4 mx-auto" />
                    </button>
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.textAlign === 'right' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, textAlign: 'right' } });
                      }}
                    >
                      <AlignRight className="h-4 w-4 mx-auto" />
                    </button>
                  </div>
                </div>
                
                <div>
                  <Label className="text-xs">Vertical Align</Label>
                  <div className="flex mt-1 border rounded-md overflow-hidden">
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.verticalAlign === 'top' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, verticalAlign: 'top' } });
                      }}
                    >
                      <ArrowUp className="h-4 w-4 mx-auto" />
                    </button>
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.verticalAlign === 'middle' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, verticalAlign: 'middle' } });
                      }}
                    >
                      <ArrowDown className="h-4 w-4 mx-auto transform rotate-90" />
                    </button>
                    <button
                      type="button"
                      className={`flex-1 p-1 ${localContent.style?.verticalAlign === 'bottom' ? 'bg-muted' : ''}`}
                      onClick={() => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, verticalAlign: 'bottom' } });
                      }}
                    >
                      <ArrowDown className="h-4 w-4 mx-auto" />
                    </button>
                  </div>
                </div>
              </div>
              
              {/* Text Styling */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Font Weight</Label>
                  <Select
                    value={localContent.style?.fontWeight || 'normal'}
                    onValueChange={(value) => {
                      const style = localContent.style || {};
                      updateContent({ style: { ...style, fontWeight: value } });
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Weight" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="bold">Bold</SelectItem>
                      <SelectItem value="lighter">Lighter</SelectItem>
                      <SelectItem value="bolder">Bolder</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="200">200</SelectItem>
                      <SelectItem value="300">300</SelectItem>
                      <SelectItem value="400">400</SelectItem>
                      <SelectItem value="500">500</SelectItem>
                      <SelectItem value="600">600</SelectItem>
                      <SelectItem value="700">700</SelectItem>
                      <SelectItem value="800">800</SelectItem>
                      <SelectItem value="900">900</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-xs">Text Color</Label>
                  <div className="flex items-center mt-1">
                    <input
                      type="color"
                      value={localContent.style?.color || '#000000'}
                      onChange={(e) => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, color: e.target.value } });
                      }}
                      className="w-full h-8 rounded cursor-pointer"
                    />
                  </div>
                </div>
              </div>
              
              {/* Text Decoration & Background Color */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Text Decoration</Label>
                  <Select
                    value={localContent.style?.textDecoration || 'none'}
                    onValueChange={(value) => {
                      const style = localContent.style || {};
                      updateContent({ style: { ...style, textDecoration: value } });
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Decoration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="underline">Underline</SelectItem>
                      <SelectItem value="overline">Overline</SelectItem>
                      <SelectItem value="line-through">Strikethrough</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label className="text-xs">Background Color</Label>
                  <div className="flex items-center mt-1">
                    <input
                      type="color"
                      value={localContent.style?.backgroundColor || 'transparent'}
                      onChange={(e) => {
                        const style = localContent.style || {};
                        updateContent({ style: { ...style, backgroundColor: e.target.value } });
                      }}
                      className="w-full h-8 rounded cursor-pointer"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Image/Video Source Display */}
          {(localContent.dataType === 'image' || localContent.dataType === 'video') && (
            <>
              {/* Show the actual source URL if available */}
              {actualSrc && (
                <div className="space-y-1">
                  <Label className="text-sm font-medium">Source</Label>
                  <div className="flex items-center p-2 bg-muted rounded-md text-xs overflow-hidden">
                    <div className="truncate flex-1">{actualSrc}</div>
                  </div>
                </div>
              )}
              
              {/* Placeholder URL input */}
              <div className="space-y-1">
                <Label className="text-sm font-medium">Placeholder URL</Label>
                <Input
                  value={localContent.placeholderUrl || ''}
                  onChange={(e) => updateContent({ placeholderUrl: e.target.value })}
                  placeholder="https://example.com/images/{fieldName}.png"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  URL with placeholders like {"{placeholder}"} that will be replaced with API data.
                  Example: https://picsum.photos/id/237/200/300
                </p>
              </div>
            </>
          )}
        </div>
      );
    }
    
    return null;
  };

  const renderDateControls = () => {
    if (!selectedElement || !localContent || selectedElement.type !== 'date') return null;
    
    // Default date options if not set
    const defaultOptions = { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    };
    
    // Function to normalize options for comparison by sorting keys
    const normalizeOptions = (options: any) => {
      if (!options) return JSON.stringify(defaultOptions);
      
      try {
        // If options is a string, parse it first
        const optionsObj = typeof options === 'string' ? JSON.parse(options) : options;
        
        // Create a stable JSON representation by sorting keys
        const stableJson = JSON.stringify(optionsObj, Object.keys(optionsObj).sort());
        return stableJson;
      } catch (e) {
        console.error('Error normalizing options:', e);
        return JSON.stringify(defaultOptions);
      }
    };
    
    // Define matched format finder function
    const findMatchingFormat = (optionsToMatch: any) => {
      // Normalize our input options
      const normalizedInput = normalizeOptions(optionsToMatch);
      
      // Define our format constants to compare against
      const formatOptions = [
        { 
          format: JSON.stringify({ 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          }),
          normalizedFormat: normalizeOptions({ 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })
        },
        { 
          format: JSON.stringify({ 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          }),
          normalizedFormat: normalizeOptions({ 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })
        },
        { 
          format: JSON.stringify({ 
            year: 'numeric', 
            month: 'numeric', 
            day: 'numeric' 
          }),
          normalizedFormat: normalizeOptions({ 
            year: 'numeric', 
            month: 'numeric', 
            day: 'numeric' 
          })
        },
        { 
          format: JSON.stringify({ 
            hour: 'numeric', 
            minute: 'numeric',
            second: 'numeric',
            hour12: true,
            timeOnly: true
          }),
          normalizedFormat: normalizeOptions({ 
            hour: 'numeric', 
            minute: 'numeric',
            second: 'numeric',
            hour12: true,
            timeOnly: true
          })
        },
        { 
          format: JSON.stringify({ 
            hour: 'numeric', 
            minute: 'numeric',
            hour12: true,
            timeOnly: true
          }),
          normalizedFormat: normalizeOptions({ 
            hour: 'numeric', 
            minute: 'numeric',
            hour12: true,
            timeOnly: true
          })
        },
        { 
          format: JSON.stringify({ 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: 'numeric', 
            minute: 'numeric',
            hour12: true
          }),
          normalizedFormat: normalizeOptions({ 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: 'numeric', 
            minute: 'numeric',
            hour12: true
          })
        }
      ];
      
      // Find a matching format, or return the default format
      const match = formatOptions.find(format => {
        return format.normalizedFormat === normalizedInput;
      });
      
      return match ? match.format : JSON.stringify(defaultOptions);
    };
    
    // Get the best match for the current options
    const optionsString = findMatchingFormat(localContent.options);
    
    // Try to get the locale, or use default
    const locale = localContent.locale || 'en-US';
    
    // Define the date format options as constants
    const DATE_FORMAT_FULL = JSON.stringify({ 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    
    const DATE_FORMAT_LONG = JSON.stringify({ 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    
    const DATE_FORMAT_SHORT = JSON.stringify({ 
      year: 'numeric', 
      month: 'numeric', 
      day: 'numeric' 
    });
    
    const TIME_FORMAT_FULL = JSON.stringify({ 
      hour: 'numeric', 
      minute: 'numeric',
      second: 'numeric',
      hour12: true,
      timeOnly: true
    });
    
    const TIME_FORMAT_SHORT = JSON.stringify({ 
      hour: 'numeric', 
      minute: 'numeric',
      hour12: true,
      timeOnly: true
    });
    
    const DATETIME_FORMAT = JSON.stringify({ 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: 'numeric', 
      minute: 'numeric',
      hour12: true
    });
    
    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label>Locale</Label>
          <Select
            value={locale}
            onValueChange={(value) => {
              updateContent({ locale: value });
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select locale" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en-US">English (US)</SelectItem>
              <SelectItem value="en-GB">English (UK)</SelectItem>
              <SelectItem value="fr-FR">French</SelectItem>
              <SelectItem value="de-DE">German</SelectItem>
              <SelectItem value="es-ES">Spanish</SelectItem>
              <SelectItem value="zh-CN">Chinese (Simplified)</SelectItem>
              <SelectItem value="ja-JP">Japanese</SelectItem>
              <SelectItem value="ko-KR">Korean</SelectItem>
              <SelectItem value="ar-SA">Arabic</SelectItem>
              <SelectItem value="ru-RU">Russian</SelectItem>
              <SelectItem value="pt-BR">Portuguese (Brazil)</SelectItem>
              <SelectItem value="it-IT">Italian</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label>Date Format</Label>
          <Select
            value={optionsString}
            onValueChange={(value) => {
              try {
                updateContent({ options: JSON.parse(value) });
              } catch (e) {
                // If JSON parsing fails, use default options
                updateContent({ options: defaultOptions });
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={DATE_FORMAT_FULL}>
                Monday, January 1, 2023
              </SelectItem>
              <SelectItem value={DATE_FORMAT_LONG}>
                January 1, 2023
              </SelectItem>
              <SelectItem value={DATE_FORMAT_SHORT}>
                01/01/2023
              </SelectItem>
              <SelectItem value={TIME_FORMAT_FULL}>
                12:00:00 PM (Time only)
              </SelectItem>
              <SelectItem value={TIME_FORMAT_SHORT}>
                12:00 PM (Short time)
              </SelectItem>
              <SelectItem value={DATETIME_FORMAT}>
                Monday, January 1, 2023, 12:00 PM
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  return (
    <div className="w-80 border-l bg-background h-full flex flex-col overflow-hidden">
      <div className="p-4 border-b flex justify-between items-center shrink-0">
        <h3 className="font-medium">Properties</h3>
        <Button 
          variant="ghost" 
          size="icon" 
          className="text-destructive hover:text-destructive"
          onClick={() => onDeleteElement(selectedElement.id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
      
      <Tabs 
        value={activeTab} 
        onValueChange={setActiveTab}
        className="flex-1 flex flex-col overflow-hidden"
      >
        <TabsList className={cn(
          "grid mx-4 mt-2 shrink-0",
          selectedElement?.type === 'playlist' ? "grid-cols-2" : 
          (selectedElement?.type === 'api' && selectedElement?.content?.subtype === 'api.gauge') ? "grid-cols-3" :
          (selectedElement?.type === 'multimedia' || selectedElement?.type === 'api') ? "grid-cols-2" : 
          "grid-cols-3"
        )}>
          <TabsTrigger 
            value="style" 
            className={selectedElement?.type === 'playlist' ? "hidden" : ""}
          >Style</TabsTrigger>
          <TabsTrigger value="position">Position</TabsTrigger>
          <TabsTrigger 
            value="animation" 
            className={selectedElement?.type === 'playlist' || selectedElement?.type === 'multimedia' || selectedElement?.type === 'api' ? "hidden" : ""}
          >Animation</TabsTrigger>
          <TabsTrigger value="playlist" className={selectedElement?.type !== 'playlist' ? "hidden" : ""}>Playlist</TabsTrigger>
          <TabsTrigger 
            value="segments" 
            className={(selectedElement?.type === 'api' && selectedElement?.content?.subtype === 'api.gauge') ? "" : "hidden"}
          >Segments</TabsTrigger>
        </TabsList>
        
        <div className="flex-1 overflow-y-auto">
          <TabsContent value="style" className="mt-0 space-y-4 p-4">
            {renderTextControls()}
            {renderShapeControls()}
            {renderMediaControls()}
            {renderDateControls()}
            {renderApiControls()}
          </TabsContent>
          
          <TabsContent value="position" className="mt-0 p-4">
            {renderPositionControls()}
          </TabsContent>
          
          <TabsContent value="animation" className="mt-0 p-4">
            {renderAnimationControls()}
          </TabsContent>
          
          <TabsContent value="playlist" className="mt-0 p-4 flex-1 overflow-hidden">
            {selectedElement && selectedElement.type === 'playlist' && (
              <PlaylistControls 
                teamId={selectedElement.teamId || ''}
                content={localContent || {}}
                updateContent={updateContent}
              />
            )}
          </TabsContent>
          
          <TabsContent value="segments" className="mt-0 p-4 flex-1 overflow-auto">
            {selectedElement && selectedElement.type === 'api' && selectedElement.content?.subtype === 'api.gauge' && renderGaugeSegmentControls()}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
