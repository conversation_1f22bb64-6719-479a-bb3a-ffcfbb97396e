import { useState } from "react";
import { useLocation } from "wouter";
import {
  Search,
  Bell,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  Menu
} from "lucide-react";

// Import logo assets
import logoFull from "../../assets/Final-adloopr-V2-light.png";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useSidebar } from "@/contexts/sidebar-context";
import { useIsMobile } from "@/hooks/use-mobile";

interface HeaderProps {
  title: string;
}

export function Header({ title }: HeaderProps) {
  const [location] = useLocation();
  const [searchOpen, setSearchOpen] = useState(false);
  const { isCollapsed, toggleCollapse, toggleMobileSidebar } = useSidebar();
  const isMobile = useIsMobile();

  const getPageTitle = () => {
    if (title) return title;

    switch (location) {
      case "/dashboard":
        return "Dashboard";
      case "/media":
        return "Media";
      case "/screens":
        return "Screens";
      case "/campaigns":
        return "Campaigns";
      case "/designer":
        return "Slide Designer";
      case "/reports":
        return "Reports";
      case "/settings":
        return "Settings";
      case "/settings/tags":
        return "Tags";
      default:
        return "AdLoopr";
    }
  };

  return (
    <header className="bg-primary text-[#05DAC3] shadow-sm z-20 sticky top-0">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center">
            {/* Desktop Sidebar Toggle Button - only show on large screens */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleCollapse}
              className="hidden lg:flex mr-3 text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
              title={isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
            <h1 className="text-xl font-semibold text-[#05DAC3]">{getPageTitle()}</h1>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Mobile Menu Button */}
          {isMobile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMobileSidebar}
              className="text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}

          {searchOpen ? (
            <div className="relative">
              <Input
                className="w-[200px] md:w-[300px] pl-9 bg-[#020626]/80 border-[#05DAC3]/50 text-[#05DAC3] focus-visible:ring-[#05DAC3]"
                placeholder="Search..."
                autoFocus
                onBlur={() => setSearchOpen(false)}
              />
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-[#05DAC3]" />
            </div>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSearchOpen(true)}
              className="text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
            >
              <Search className="h-5 w-5" />
            </Button>
          )}

          <Button
            variant="ghost"
            size="icon"
            className="text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
          >
            <Bell className="h-5 w-5" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
          >
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      </div>
    </header>
  );
}
