- First remove all the header on the element buttons from toolbox i.e. "text", "Shapes", "Date & Time" and "API Elements"
- Remove "Add" word from all the element buttons. Replace "Add Multimedia" button text to "API Elements"

The API Elements should be implemented as follows;
- On the Style tab, add a label called "Data Mapping"
- For data mapping, add a dropdown and fill with all the data columns/keys derived from the API URL data response first row. If the api data response has nested data, separate each key with "." (dot). User will select a data column/key to be mapped. For the nested nodes, data column/key should be fetched from 1st row only.
- Replace "Media Type" label with "Data Type". Along with Image, Video add another item "Text". By default "Text" should be selected.
- When user selects data type;

  -- If "Text" type is selected, 
     --- Fetch mapped data field value from the API response and set as "Text Content" of the element.
     --- all the properties defined for "Text" element are applicable to "Text" data type so they should be visible.

  -- If "Image" or "Video" data type is selected, display Placeholder URL input box.

  -- If "Image" type is selected,
     --- Fetch mapped data field value from the API response and validate if it has a valid image url. If the validation is successful, add an <img> element inside the "API Element" and set its src. If the validation is unsuccessful, check if "Placeholder URL" value is entered by the user and if its provided, replace the field data with in "{}" placeholder and validate the new URL and if its valid, set the <img> src. Image should fill entire parent container by ignoring the aspect ratio.

  -- If "Video" type is selected,
     --- Fetch mapped data field value from the API response and validate if it has a valid video url. If the validation is successful, add an <video> element inside the "API Element" and set its src. If the validation is unsuccessful, check if "Placeholder URL" value is entered by the user and if its provided, replace the field data with in "{}" placeholder and validate the new URL and if its valid, set the <video> src. Video should fill entire parent container by ignoring the aspect ratio.

-- Position and Animation tabs and properties for API Element remains the same as other elements