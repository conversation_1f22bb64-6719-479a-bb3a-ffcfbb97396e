Help me create a digital signage content management admin panel web application in React/Next.js with modern UX/UI component library and supabase database as backend using supabase/ssr package for supabase authentication. Please don't use any depreciated package or library. The application has following features;

1. **Authentication**

1. Login page with authentication form. Implement user authentication using best coding practice suggested for supabase authentication using ssr package. This should be the landing page.
2. Sign Up page with registration form

When a user sign up, user's details should be intersted into following tables upon confirmation of the email;

- 'profiles'
- 'teams'
- 'team_members'

The team_id should be used in all the tables to distinguish the records.

When user sign in successfully, redirect to the dashboard.

2. **Member Dashboard**

2.1. Main dashboard with analytics overview and recent activity

2.2. Media management with image/video upload. When file is being uploaded, it should include media information such as resolution (width/height), file size, video file duration and generate a thumbnail and store the information in the supabase database. The file should be uploaded in the user's folder under supabase storage bucket called 'medialibrary'. There should be an ability to add tags while uploading the file. Tag input is to be added in the upload window. When user starts entering text in the input box, matching tag list should be presented. If matching tag not found, then user should be able press enter to create new tag and assign it to the file. The media details drawer should display all the above information and the user should be able to add/remove tags. There should be a search box (tag input style) above the media grid view where user can enter one or more search terms. Use "OR" operator to filter the grid view. There should also be a 'lazy load' option for the grid view i.e. show 8 items a time and when user clicks on 'Load More', it should load next 8 items so on so forth.

2.3. Screen management for device control. Ability to add a new screen device using 5 digit number with name and it's location. Ability to set other configurations such as update frequency in "hh:mm:ss" format, device start time and end time, site email. Ability to assign tags just like media page. Ability to search/filter the screen list using name, location, status ("On"/"Off") or tags using search box which is tag input style just like media page. The status should be determined by comparing 'lastpingdatetime' with current date time. 

2.4. Campaign management for scheduling content with start date and end date. Ability to select one or more media files. Ability to add/remove screens where the campaign should be published. Show campaing progress bar with status such as 'Active', 'Scheduled', 'completed'.

2.5. Slide Designer with drag-and-drop functionality. Element such as 'Text', 'Multimedia', 'Date', 'Shapes' can be drag and dropped on a canvas and configure its properties such as font formatting, size, position etc. Ability to zoom in/out the canvas. Ability to resize the dropped element using mourse.

2.6. Reports section with analytics

2.7. Settings page with account, security, notifications, team, and billing options and tag management

The design features:

- Clean visual hierarchy with proper spacing
- Neutral color palette with subtle accents such as primary color #020626 and secondary color #05DAC3. Please check http://adloopr.html-5.me/?i=1 for styling and branding.
- Modern typography using Inter font
- Responsive layout that works on mobile and desktop
- Sidebar navigation that collapses on mobile

Please provide complete supabase database creation script including tables, indexes, foreign key associations, RLS policies etc. 