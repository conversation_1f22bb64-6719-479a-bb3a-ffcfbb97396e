import { useState, useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { MediaCard } from "@/components/media/media-card";
import { UploadModal } from "@/components/media/upload-modal";
import { EditMediaModal } from "@/components/media/edit-media-modal";
import { TagInput } from "@/components/media/tag-input";
import { useMedia } from "@/hooks/use-media";
import { useAuth } from "@/hooks/use-auth";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { UploadCloud, Search, Grid2X2, List, FileImage, SlidersHorizontal } from "lucide-react";

export default function Media() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [teamId, setTeamId] = useState<string>("");
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [fileTypeFilter, setFileTypeFilter] = useState<string>("all");
  const [sortOrder, setSortOrder] = useState<string>("recentlyAdded");
  const [showFilters, setShowFilters] = useState(false);
  const [mediaPreview, setMediaPreview] = useState<{ url: string; type: string } | null>(null);
  const [editingMedia, setEditingMedia] = useState<{ id: string; name: string; tags: string[] } | null>(null);

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: ['/api/profiles', user?.id, 'teams'],
    enabled: !!user?.id,
  });

  // Get all available tags for the current team
  const { data: availableTags } = useQuery<any[]>({
    queryKey: ['/api/teams', teamId, 'tags'],
    enabled: !!teamId,
  });

  useEffect(() => {
    console.log('Teams data loaded:', teams);
    if (teams && teams.length > 0) {
      console.log('Setting teamId to:', teams[0].id);
      setTeamId(teams[0].id);
    } else {
      console.log('No teams available');
    }
  }, [teams]);

  const {
    mediaItems,
    isLoading,
    isEmpty,
    deleteMedia,
    refetch
  } = useMedia(teamId);

  // Filter media items
  const filteredMediaItems = mediaItems?.filter(item => {
    // Filter by search query
    const matchesSearch = !searchQuery ||
      item.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by file type, with null checks
    const fileType = item.fileType || item.file_type || ''; // Accept both property names
    const matchesFileType = fileTypeFilter === "all" ||
      (fileTypeFilter === "images" && fileType.startsWith("image/")) ||
      (fileTypeFilter === "videos" && fileType.startsWith("video/")) ||
      (fileTypeFilter === "others" && !fileType.startsWith("image/") && !fileType.startsWith("video/"));

    // Filter by tags - show items that have ANY of the selected tags (OR logic)
    const matchesTags = selectedTags.length === 0 ||
      (item.tags && Array.isArray(item.tags) &&
        // Check if the item has ANY of the selected tags (OR logic)
        selectedTags.some(selectedTag =>
          item.tags.some(itemTag =>
            itemTag.toLowerCase() === selectedTag.toLowerCase()
          )
        )
      );

    return matchesSearch && matchesFileType && matchesTags;
  });

  // Sort media items
  const sortedMediaItems = [...(filteredMediaItems || [])].sort((a, b) => {
    switch (sortOrder) {
      case "nameAZ":
        return a.name.localeCompare(b.name);
      case "nameZA":
        return b.name.localeCompare(a.name);
      case "sizeSmallest":
        return (a.fileSize || 0) - (b.fileSize || 0);
      case "sizeLargest":
        return (b.fileSize || 0) - (a.fileSize || 0);
      case "recentlyAdded":
      default:
        // Add null checks for createdAt
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return dateB - dateA;
    }
  });

  const handleViewMedia = (url: string, type: string) => {
    setMediaPreview({ url, type });
  };

  const handleEditMedia = (id: string) => {
    console.log("Edit media:", id);

    // Find the media item by id
    const mediaItem = mediaItems?.find(item => item.id === id);
    if (mediaItem) {
      // Set the editing media state
      setEditingMedia({
        id: mediaItem.id,
        name: mediaItem.name,
        tags: mediaItem.tags || []
      });
    }
  };

  const [mediaToDelete, setMediaToDelete] = useState<string | null>(null);

  const handleDeleteMedia = (id: string) => {
    setMediaToDelete(id);
  };

  const confirmDeleteMedia = () => {
    if (mediaToDelete) {
      deleteMedia(mediaToDelete);
      setMediaToDelete(null);
    }
  };

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Media Library</h2>
          <p className="text-muted-foreground">Manage your images, videos and other media files</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button onClick={() => setIsUploadModalOpen(true)}>
            <UploadCloud className="mr-2 h-4 w-4" /> Upload Media
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Search media..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={showFilters ? "default" : "outline"}
                size="icon"
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal className="h-4 w-4" />
              </Button>

              <div className="flex border border-input rounded-md overflow-hidden">
                <Button
                  variant={viewMode === "grid" ? "secondary" : "ghost"}
                  size="icon"
                  onClick={() => setViewMode("grid")}
                  className="rounded-none h-9 w-9"
                >
                  <Grid2X2 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "secondary" : "ghost"}
                  size="icon"
                  onClick={() => setViewMode("list")}
                  className="rounded-none h-9 w-9"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <label className="text-sm font-medium block mb-2">File Type</label>
                <Select value={fileTypeFilter} onValueChange={setFileTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="images">Images</SelectItem>
                    <SelectItem value="videos">Videos</SelectItem>
                    <SelectItem value="others">Others</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">Sort By</label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue placeholder="Recently Added" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="recentlyAdded">Recently Added</SelectItem>
                    <SelectItem value="nameAZ">Name (A-Z)</SelectItem>
                    <SelectItem value="nameZA">Name (Z-A)</SelectItem>
                    <SelectItem value="sizeSmallest">Size (Smallest)</SelectItem>
                    <SelectItem value="sizeLargest">Size (Largest)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="md:col-span-2">
                <label className="text-sm font-medium block mb-2">Tags</label>
                <TagInput
                  value={selectedTags}
                  onChange={setSelectedTags}
                  placeholder="Filter by tags..."
                  teamId={teamId}
                />
                {availableTags && availableTags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {availableTags.map((tag) => (
                      <button
                        key={tag.id}
                        type="button"
                        onClick={() => {
                          // Check if tag is already selected (case-insensitive)
                          const isSelected = selectedTags.some(
                            t => t.toLowerCase() === tag.name.toLowerCase()
                          );

                          if (isSelected) {
                            setSelectedTags(selectedTags.filter(
                              t => t.toLowerCase() !== tag.name.toLowerCase()
                            ));
                          } else {
                            setSelectedTags([...selectedTags, tag.name]);
                          }
                        }}
                        className={`text-xs px-2 py-1 rounded-full ${
                          // Check if tag is already selected (case-insensitive)
                          selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted hover:bg-muted/80'
                        }`}
                        style={{
                          backgroundColor: selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? tag.color
                            : undefined,
                          color: selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? '#fff'
                            : undefined
                        }}
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Media Content */}
      {isLoading ? (
        <div className="py-20 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading media...</p>
        </div>
      ) : isEmpty ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <FileImage className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No media files yet</h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Upload images, videos, and other files to create content for your digital signage displays.
          </p>
          <Button onClick={() => setIsUploadModalOpen(true)}>
            <UploadCloud className="mr-2 h-4 w-4" /> Upload Media
          </Button>
        </div>
      ) : (
        <>
          {/* Grid view */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {sortedMediaItems.map((item) => (
                <MediaCard
                  key={item.id}
                  id={item.id}
                  name={item.name}
                  type={item.fileType || item.file_type || ''}
                  fileUrl={item.fileUrl}
                  thumbnailUrl={item.thumbnailUrl || undefined}
                  fileSize={item.fileSize || 0}
                  width={item.width || undefined}
                  height={item.height || undefined}
                  duration={item.duration || undefined}
                  uploadedDate={item.createdAt || new Date()}
                  tags={item.tags || []}
                  onEdit={handleEditMedia}
                  onDelete={handleDeleteMedia}
                  onView={handleViewMedia}
                />
              ))}
            </div>
          ) : (
            // List view
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="min-w-full">
                <table className="min-w-full divide-y divide-border">
                  <thead>
                    <tr className="bg-muted/50">
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Size</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Dimensions</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Uploaded</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Tags</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-border">
                    {sortedMediaItems.map((item) => (
                      <tr key={item.id} className="hover:bg-muted/20">
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="flex items-center">
                            <div className="h-10 w-10 bg-muted rounded mr-3 flex items-center justify-center overflow-hidden">
                              {item.thumbnailUrl ? (
                                <img src={item.thumbnailUrl} alt="" className="h-10 w-10 object-cover" />
                              ) : (item.fileType || item.file_type || '').startsWith("image/") ? (
                                <FileImage className="h-5 w-5 text-muted-foreground" />
                              ) : (
                                <FileImage className="h-5 w-5 text-muted-foreground" />
                              )}
                            </div>
                            <div className="truncate max-w-[200px]">{item.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {(() => {
                            const type = item.fileType || item.file_type;
                            return type ? type.split("/")[0] : "Unknown";
                          })()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {Math.round((item.fileSize || 0) / 1024)} KB
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {item.width && item.height ? `${item.width} × ${item.height}` : "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="flex flex-wrap gap-1">
                            {item.tags && item.tags.length > 0 ? (
                              item.tags.map((tag, i) => (
                                <span key={i} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-muted text-foreground">
                                  {tag}
                                </span>
                              ))
                            ) : (
                              <span className="text-muted-foreground">No tags</span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm" onClick={() => handleViewMedia(item.fileUrl, item.fileType || item.file_type || "unknown/unknown")}>
                              View
                            </Button>
                            <Button variant="ghost" size="sm" onClick={() => handleDeleteMedia(item.id)}>
                              Delete
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {/* Upload Modal */}
      <UploadModal
        open={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        teamId={teamId}
        onSuccess={() => {
          // Media list should already be refreshed by the upload modal
          setIsUploadModalOpen(false);
        }}
      />

      {/* Media Preview Dialog */}
      <Dialog open={!!mediaPreview} onOpenChange={() => setMediaPreview(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Media Preview</DialogTitle>
            <DialogDescription>Preview your media content</DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center p-2">
            {mediaPreview && mediaPreview.type && mediaPreview.type.startsWith("image/") ? (
              <img
                src={mediaPreview.url}
                alt="Preview"
                className="max-h-[70vh] max-w-full object-contain"
              />
            ) : mediaPreview && mediaPreview.type && mediaPreview.type.startsWith("video/") ? (
              <video
                src={mediaPreview.url}
                controls
                className="max-h-[70vh] max-w-full"
              />
            ) : (
              <div className="p-12 text-center">
                <p>This file type cannot be previewed</p>
                <Button className="mt-4" asChild>
                  <a href={mediaPreview?.url} target="_blank" rel="noopener noreferrer">
                    Open File
                  </a>
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!mediaToDelete} onOpenChange={(open) => !open && setMediaToDelete(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this media item? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setMediaToDelete(null)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteMedia}>
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Media Modal */}
      {editingMedia && (
        <EditMediaModal
          open={!!editingMedia}
          onClose={() => setEditingMedia(null)}
          mediaId={editingMedia.id}
          mediaName={editingMedia.name}
          teamId={teamId}
          initialTags={editingMedia.tags}
        />
      )}
    </MainLayout>
  );
}
