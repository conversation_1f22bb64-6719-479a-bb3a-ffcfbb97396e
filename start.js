#!/usr/bin/env node

// Load environment variables from .env file
import dotenv from 'dotenv';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Get the directory of this script
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
dotenv.config({ path: join(__dirname, '.env') });

// Set NODE_ENV if not already set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'production';
}

// Start the application with the loaded environment variables
const child = spawn('node', ['dist/index.js'], {
  stdio: 'inherit',
  env: process.env,
  cwd: __dirname
});

child.on('exit', (code) => {
  process.exit(code);
});

child.on('error', (err) => {
  console.error('Failed to start application:', err);
  process.exit(1);
});
