import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";

import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  PlusCircle, 
  Search, 
  Calendar,
  Check,
  X,
  Presentation,
  CalendarRange,
  Monitor,
  Image as ImageIcon,
  ArrowUp,
  ArrowDown,
  Layers,
  FileVideo,
  Video,
  FolderSearch,
  Trash2,
  MonitorSmartphone,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

// Schema for validation
const campaignSchema = z.object({
  name: z.string().min(1, "Name is required"),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
  status: z.enum(["active", "scheduled", "draft", "paused", "completed"]),
});

type CampaignFormValues = z.infer<typeof campaignSchema>;

interface CampaignEditModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  campaign: any; // The campaign to edit
  teamId: string;
  screens: any[]; // All available screens
  mediaItems: any[]; // All available media items
  slides: any[]; // All available slides
  selectedScreens: string[];
  setSelectedScreens: (ids: string[]) => void;
  selectedMedia: string[];
  setSelectedMedia: (ids: string[]) => void;
  screenTags: any[];
  mediaTags: any[];
  onSave: (data: CampaignFormValues, mediaOrder?: string[]) => void;
}

export function CampaignEditModal({
  isOpen,
  onOpenChange,
  campaign,
  teamId,
  screens,
  mediaItems,
  slides,
  selectedScreens,
  setSelectedScreens,
  selectedMedia,
  setSelectedMedia,
  screenTags,
  mediaTags,
  onSave,
}: CampaignEditModalProps) {
  // State for the popover controls
  const [screenSearchOpen, setScreenSearchOpen] = useState(false);
  const [mediaSearchOpen, setMediaSearchOpen] = useState(false);
  const [screenSearchQuery, setScreenSearchQuery] = useState("");
  const [mediaSearchQuery, setMediaSearchQuery] = useState("");
  const [selectedScreenStatusTag, setSelectedScreenStatusTag] = useState<string>("");
  const [selectedScreenTag, setSelectedScreenTag] = useState<string>("");
  const [selectedMediaTag, setSelectedMediaTag] = useState<string>("");
  // Use local state for media selection and ordering - decoupled from parent
  const [localSelectedMedia, setLocalSelectedMedia] = useState<string[]>([]);
  const [mediaDisplayOrder, setMediaDisplayOrder] = useState<string[]>([]);

  // Form to handle campaign settings
  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: campaign?.name || "",
      startDate: campaign?.startDate ? new Date(campaign.startDate) : new Date(),
      endDate: campaign?.endDate ? new Date(campaign.endDate) : new Date(new Date().setDate(new Date().getDate() + 7)),
      status: campaign?.status || "draft",
    },
  });

  // Update form and local state when campaign or selected media changes
  useEffect(() => {
    if (campaign) {
      form.reset({
        name: campaign.name,
        startDate: new Date(campaign.startDate),
        endDate: new Date(campaign.endDate),
        status: campaign.status as any,
      });
    }
    
    // Initialize local state from props
    setLocalSelectedMedia([...selectedMedia]);
    
    // Set initial display order based on selected media
    setMediaDisplayOrder([...selectedMedia]);
  }, [campaign, form, selectedMedia, isOpen]); // Include isOpen to reset when modal reopens

  // Selection helpers
  const toggleScreenSelection = (screenId: string) => {
    setSelectedScreens(
      selectedScreens.includes(screenId)
        ? selectedScreens.filter(id => id !== screenId)
        : [...selectedScreens, screenId]
    );
  };

  const toggleMediaSelection = (mediaId: string) => {
    if (localSelectedMedia.includes(mediaId)) {
      // Remove the media
      setLocalSelectedMedia(localSelectedMedia.filter(id => id !== mediaId));
      setMediaDisplayOrder(mediaDisplayOrder.filter(id => id !== mediaId));
    } else {
      // Add the media
      setLocalSelectedMedia([...localSelectedMedia, mediaId]);
      setMediaDisplayOrder([...mediaDisplayOrder, mediaId]);
    }
  };

  // Media reordering functions - only update local state
  const moveMediaUp = (mediaId: string) => {
    const index = mediaDisplayOrder.indexOf(mediaId);
    if (index > 0) {
      const newOrder = [...mediaDisplayOrder];
      [newOrder[index], newOrder[index - 1]] = [newOrder[index - 1], newOrder[index]];
      setMediaDisplayOrder(newOrder);
    }
  };

  const moveMediaDown = (mediaId: string) => {
    const index = mediaDisplayOrder.indexOf(mediaId);
    if (index < mediaDisplayOrder.length - 1) {
      const newOrder = [...mediaDisplayOrder];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
      setMediaDisplayOrder(newOrder);
    }
  };

  // Submit handler - pass local state with form data
  const handleSubmit = (data: CampaignFormValues) => {
    // Instead of updating parent state (which won't be reflected immediately),
    // pass our mediaDisplayOrder directly with the data to the onSave function
    
    // Add detailed logging to track the media ordering process
    console.log(`Submitting form with ${mediaDisplayOrder.length} ordered media items:`);
    if (mediaDisplayOrder.length > 0) {
      // Log each media item and its position for debugging
      mediaDisplayOrder.forEach((mediaId, idx) => {
        const isSlide = slides?.some(slide => slide.id === mediaId) || false;
        const type = isSlide ? "slide" : "media";
        const name = isSlide 
          ? slides?.find(slide => slide.id === mediaId)?.name 
          : mediaItems?.find(media => media.id === mediaId)?.name;
        
        console.log(`  ${idx+1}. ${type}: ${name} (ID: ${mediaId})`);
      });
    } else {
      console.log("  No media items selected");
    }
    
    // Pass both the form data and the ordered media IDs
    try {
      onSave(data, mediaDisplayOrder);
    } catch (error) {
      console.error("Error in campaign save:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl w-[95%] p-0 overflow-hidden">
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-6 border-b">
          <DialogHeader className="mb-2">
            <div className="flex items-center space-x-2">
              <div className="bg-primary/20 rounded-full p-2">
                <Presentation className="h-5 w-5 text-primary" />
              </div>
              <DialogTitle className="text-xl font-semibold">Edit Campaign</DialogTitle>
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Configure your campaign settings, content and target screens
            </p>
          </DialogHeader>
        </div>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="p-6">
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="inline-flex h-12 items-center justify-center rounded-lg bg-muted p-1 mb-6">
                <TabsTrigger value="general" className="rounded-md px-5 py-2.5 data-[state=active]:shadow">
                  <div className="flex items-center">
                    <CalendarRange className="h-4 w-4 mr-2" /> 
                    <span>General Settings</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="media" className="rounded-md px-5 py-2.5 data-[state=active]:shadow">
                  <div className="flex items-center">
                    <ImageIcon className="h-4 w-4 mr-2" /> 
                    <span>Content</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger value="screens" className="rounded-md px-5 py-2.5 data-[state=active]:shadow">
                  <div className="flex items-center">
                    <Monitor className="h-4 w-4 mr-2" /> 
                    <span>Screens</span>
                  </div>
                </TabsTrigger>
              </TabsList>
              
              {/* General Settings Tab */}
              <TabsContent value="general" className="space-y-6">
                <div className="bg-background rounded-lg border shadow-sm p-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                      <CalendarRange className="h-4 w-4 text-primary" />
                    </span>
                    Campaign Details
                  </h3>
                  
                  <div className="space-y-5">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Campaign Name</FormLabel>
                          <FormControl>
                            <Input 
                              {...field} 
                              className="h-11 transition-all focus-visible:ring-primary/20 focus-visible:ring-4"
                              placeholder="Enter a descriptive name for your campaign"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                      <FormField
                        control={form.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className="text-sm font-medium">Start Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={`w-full pl-3 h-11 text-left font-normal border-input transition-all hover:bg-accent hover:text-accent-foreground ${!field.value && "text-muted-foreground"}`}
                                  >
                                    {field.value ? (
                                      <span className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 text-primary" />
                                        {format(field.value, "PPP")}
                                      </span>
                                    ) : (
                                      <span className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 opacity-70" />
                                        Pick a date
                                      </span>
                                    )}
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  initialFocus
                                  className="rounded-md border"
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="endDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel className="text-sm font-medium">End Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={`w-full pl-3 h-11 text-left font-normal border-input transition-all hover:bg-accent hover:text-accent-foreground ${!field.value && "text-muted-foreground"}`}
                                  >
                                    {field.value ? (
                                      <span className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 text-primary" />
                                        {format(field.value, "PPP")}
                                      </span>
                                    ) : (
                                      <span className="flex items-center">
                                        <Calendar className="h-4 w-4 mr-2 opacity-70" />
                                        Pick a date
                                      </span>
                                    )}
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  initialFocus
                                  className="rounded-md border"
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
                
                <div className="bg-background rounded-lg border shadow-sm p-6">
                  <h3 className="text-lg font-medium mb-4 flex items-center">
                    <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                      <Check className="h-4 w-4 text-primary" />
                    </span>
                    Campaign Status
                  </h3>
                  
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium">Status</FormLabel>
                        <div className="mt-1 mb-2 text-sm text-muted-foreground">
                          Set the current status of this campaign
                        </div>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-11 transition-all focus-visible:ring-primary/20 focus-visible:ring-4">
                              <SelectValue placeholder="Select a status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft" className="flex items-center h-9">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-muted-foreground mr-2"></div>
                                Draft
                              </div>
                            </SelectItem>
                            <SelectItem value="scheduled" className="flex items-center h-9">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-amber-500 mr-2"></div>
                                Scheduled
                              </div>
                            </SelectItem>
                            <SelectItem value="active" className="flex items-center h-9">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                Active
                              </div>
                            </SelectItem>
                            <SelectItem value="paused" className="flex items-center h-9">
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                                Paused
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>
              
              {/* Media/Slides Tab */}
              <TabsContent value="media" className="space-y-6">
                <div className="bg-background rounded-lg border shadow-sm">
                  <div className="p-6 border-b">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium flex items-center">
                          <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                            <ImageIcon className="h-4 w-4 text-primary" />
                          </span>
                          Campaign Content
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          Select and arrange media files and slides for this campaign
                        </p>
                      </div>
                      <Popover open={mediaSearchOpen} onOpenChange={setMediaSearchOpen}>
                        <PopoverTrigger asChild>
                          <Button className="gap-1.5 h-9">
                            <PlusCircle className="h-4 w-4" /> 
                            Add Content
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-96 p-0" align="end" sideOffset={5}>
                          <Command className="rounded-lg border shadow-md">
                            <div className="flex items-center border-b p-2">
                              <Search className="w-4 h-4 mr-2 flex-shrink-0 text-muted-foreground" />
                              <CommandInput 
                                placeholder="Search media and slides..." 
                                value={mediaSearchQuery} 
                                onValueChange={setMediaSearchQuery} 
                                className="h-9 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                              />
                            </div>
                            
                            <div className="border-t border-border p-3">
                              <div className="mb-3">
                                <h4 className="text-xs font-semibold mb-2 text-muted-foreground tracking-wide uppercase">Type</h4>
                                <div className="flex flex-wrap gap-1.5">
                                  <Button 
                                    variant={selectedMediaTag === "" ? "default" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedMediaTag("")}
                                    className="h-8 text-xs rounded-full"
                                  >
                                    All Types
                                  </Button>
                                  <Button 
                                    variant={selectedMediaTag === "image" ? "default" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedMediaTag("image")}
                                    className="h-8 text-xs rounded-full flex items-center gap-1"
                                  >
                                    <ImageIcon className="h-3 w-3" /> Images
                                  </Button>
                                  <Button 
                                    variant={selectedMediaTag === "video" ? "default" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedMediaTag("video")}
                                    className="h-8 text-xs rounded-full flex items-center gap-1"
                                  >
                                    <FileVideo className="h-3 w-3" /> Videos
                                  </Button>
                                  <Button 
                                    variant={selectedMediaTag === "slide" ? "default" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedMediaTag("slide")}
                                    className="h-8 text-xs rounded-full flex items-center gap-1"
                                  >
                                    <Presentation className="h-3 w-3" /> Slides
                                  </Button>
                                </div>
                              </div>
                              
                              {/* Media tag filter */}
                              {mediaTags.length > 0 && (
                                <div>
                                  <h4 className="text-xs font-semibold mb-2 text-muted-foreground tracking-wide uppercase">Tags</h4>
                                  <div className="flex flex-wrap gap-1.5">
                                    <Button 
                                      variant={selectedMediaTag === "" ? "default" : "outline"} 
                                      size="sm" 
                                      onClick={() => setSelectedMediaTag("")}
                                      className="h-8 text-xs rounded-full"
                                    >
                                      All Tags
                                    </Button>
                                    {mediaTags.map(tag => (
                                      <Button 
                                        key={tag.id}
                                        variant={selectedMediaTag === tag.id ? "default" : "outline"} 
                                        size="sm" 
                                        onClick={() => setSelectedMediaTag(tag.id)}
                                        className="h-8 text-xs rounded-full"
                                      >
                                        {tag.name}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                            
                            <CommandList className="max-h-[320px] overflow-auto py-2">
                              <CommandEmpty className="py-6 text-center">
                                <FolderSearch className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                                <p className="text-sm">No matching content found</p>
                              </CommandEmpty>
                              <CommandGroup heading="Media Files">
                                {mediaItems
                                  ?.filter(media => {
                                    // Filter by search query
                                    const matchesQuery = !mediaSearchQuery || 
                                      media.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());
                                    
                                    // Filter by type
                                    const matchesType = !selectedMediaTag || 
                                      selectedMediaTag === "image" && media.fileType.startsWith("image/") ||
                                      selectedMediaTag === "video" && media.fileType.startsWith("video/") ||
                                      (mediaTags.find(tag => tag.id === selectedMediaTag)?.media || []).includes(media.id);
                                    
                                    return matchesQuery && matchesType && selectedMediaTag !== "slide";
                                  })
                                  .map(media => (
                                    <CommandItem
                                      key={media.id}
                                      onSelect={() => {
                                        toggleMediaSelection(media.id);
                                      }}
                                      className="flex items-center px-4 py-2 aria-selected:bg-primary/5"
                                    >
                                      <div className="flex items-center gap-2 w-full">
                                        <Checkbox 
                                          checked={localSelectedMedia.includes(media.id)}
                                          className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                        />
                                        <div className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-2 bg-muted flex items-center justify-center border">
                                          {media.fileType.startsWith("image/") ? (
                                            <img 
                                              src={media.thumbnailUrl || media.fileUrl} 
                                              alt={media.name} 
                                              className="h-full w-full object-cover" 
                                            />
                                          ) : (
                                            <FileVideo className="h-5 w-5 text-muted-foreground" />
                                          )}
                                        </div>
                                        <div className="flex-1 overflow-hidden">
                                          <p className="truncate text-sm font-medium">{media.name}</p>
                                          <p className="text-xs text-muted-foreground truncate flex items-center mt-0.5">
                                            <Badge className="rounded-full py-0 px-2 h-4 font-normal text-[10px] bg-muted text-muted-foreground">
                                              {media.fileType.split("/")[0]}
                                            </Badge>
                                          </p>
                                        </div>
                                      </div>
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                              
                              {/* Add slides section when implemented */}
                              {slides && slides.length > 0 && (
                                <CommandGroup heading="Slides">
                                  {slides
                                    ?.filter(slide => {
                                      // Filter by search query
                                      const matchesQuery = !mediaSearchQuery || 
                                        slide.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());
                                      
                                      // Only show slides when slide filter is selected or no filter is selected
                                      return matchesQuery && (selectedMediaTag === "slide" || !selectedMediaTag);
                                    })
                                    .map(slide => (
                                      <CommandItem
                                        key={slide.id}
                                        onSelect={() => {
                                          toggleMediaSelection(slide.id);
                                        }}
                                        className="flex items-center px-4 py-2 aria-selected:bg-primary/5"
                                      >
                                        <div className="flex items-center gap-2 w-full">
                                          <Checkbox 
                                            checked={localSelectedMedia.includes(slide.id)}
                                            className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                          />
                                          <div className="flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-2 bg-primary/5 flex items-center justify-center border">
                                            <Presentation className="h-5 w-5 text-primary" />
                                          </div>
                                          <div className="flex-1 overflow-hidden">
                                            <p className="truncate text-sm font-medium">{slide.name}</p>
                                            <p className="text-xs text-muted-foreground truncate flex items-center mt-0.5">
                                              <Badge className="rounded-full py-0 px-2 h-4 font-normal text-[10px] bg-primary/10 text-primary">
                                                Slide
                                              </Badge>
                                            </p>
                                          </div>
                                        </div>
                                      </CommandItem>
                                    ))}
                                </CommandGroup>
                              )}
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                  
                  {mediaDisplayOrder.length === 0 ? (
                    <div className="p-8 text-center">
                      <div className="w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mx-auto mb-4">
                        <ImageIcon className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium">No content selected</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto mb-4">
                        Add media files and slides to be displayed in this campaign
                      </p>
                      <Button onClick={() => setMediaSearchOpen(true)}>
                        <PlusCircle className="h-4 w-4 mr-1.5" /> Select Content
                      </Button>
                    </div>
                  ) : (
                    <>
                      <div className="px-6 py-3 border-y bg-muted/30">
                        <div className="flex justify-between items-center">
                          <div>
                            <h4 className="text-sm font-medium">Content Display Order</h4>
                            <p className="text-xs text-muted-foreground mt-0.5">
                              Drag items to set the display sequence
                            </p>
                          </div>
                          <Badge variant="outline" className="text-xs font-normal gap-1.5">
                            <Layers className="h-3 w-3" /> {mediaDisplayOrder.length} Items
                          </Badge>
                        </div>
                      </div>
                      <div className="divide-y max-h-[350px] overflow-y-auto">
                        {mediaDisplayOrder.map((mediaId, index) => {
                          // Find the media item or slide
                          const mediaItem = mediaItems?.find(m => m.id === mediaId);
                          const slideItem = slides?.find(s => s.id === mediaId);
                          const item = mediaItem || slideItem;
                          
                          if (!item) return null;
                          
                          const isSlide = !!slideItem;
                          
                          return (
                            <div key={mediaId} className="flex items-center p-4 hover:bg-muted/30 transition-colors group">
                              <div className="mr-3 font-mono text-xs bg-primary/10 text-primary h-6 w-6 rounded-full flex items-center justify-center font-medium">
                                {index + 1}
                              </div>
                              {isSlide ? (
                                <div className="mr-3 h-14 w-14 overflow-hidden rounded-md border shadow-sm flex-shrink-0 bg-primary/5 flex items-center justify-center">
                                  <Presentation className="h-6 w-6 text-primary" />
                                </div>
                              ) : (
                                <div className="mr-3 h-14 w-14 overflow-hidden rounded-md border shadow-sm flex-shrink-0">
                                  <img 
                                    src={mediaItem.thumbnailUrl || mediaItem.fileUrl} 
                                    alt={mediaItem.name} 
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              )}
                              <div className="flex-1 min-w-0 ml-1">
                                <p className="text-sm font-medium truncate">{item.name}</p>
                                <div className="flex items-center mt-1">
                                  <Badge className={cn(
                                    "rounded-full text-xs font-normal py-0 px-2",
                                    isSlide 
                                      ? "bg-primary/10 text-primary" 
                                      : "bg-secondary/80 text-secondary-foreground"
                                  )}>
                                    {isSlide ? "Slide" : mediaItem.fileType.split("/")[0]}
                                  </Badge>
                                  {/* Add file size or duration if available */}
                                </div>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-8 w-8 text-muted-foreground hover:text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity" 
                                  title="Move up"
                                  onClick={() => moveMediaUp(mediaId)}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-8 w-8 text-muted-foreground hover:text-foreground rounded-full opacity-0 group-hover:opacity-100 transition-opacity" 
                                  title="Move down"
                                  onClick={() => moveMediaDown(mediaId)}
                                  disabled={index === mediaDisplayOrder.length - 1}
                                >
                                  <ArrowDown className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-full opacity-0 group-hover:opacity-100 transition-opacity" 
                                  onClick={() => toggleMediaSelection(mediaId)}
                                  title="Remove"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </>
                  )}
                </div>
              </TabsContent>
              
              {/* Screens Tab */}
              <TabsContent value="screens" className="space-y-6">
                <div className="bg-background rounded-lg border shadow-sm">
                  <div className="p-6 border-b">
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-lg font-medium flex items-center">
                          <span className="bg-primary/10 rounded-full p-1.5 mr-2">
                            <Monitor className="h-4 w-4 text-primary" />
                          </span>
                          Display Screens
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          Select screens to display this campaign content
                        </p>
                      </div>
                      <Button 
                        className="gap-1.5 h-9"
                        onClick={() => setScreenSearchOpen(true)}
                      >
                        <PlusCircle className="h-4 w-4" /> 
                        Add Screens
                      </Button>
                    </div>
                  </div>
                  
                  {selectedScreens.length === 0 ? (
                    <div className="p-8 text-center">
                      <div className="w-16 h-16 rounded-full bg-muted/40 flex items-center justify-center mx-auto mb-4">
                        <Monitor className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium">No screens selected</h3>
                      <p className="text-sm text-muted-foreground mt-1 max-w-md mx-auto mb-4">
                        Select screens where this campaign content will be displayed
                      </p>
                      <Button onClick={() => setScreenSearchOpen(true)}>
                        <PlusCircle className="h-4 w-4 mr-1.5" /> Select Screens
                      </Button>
                    </div>
                  ) : (
                    <>
                      <div className="px-6 py-3 border-y bg-muted/30">
                        <div className="flex justify-between items-center">
                          <h4 className="text-sm font-medium">Selected Screens</h4>
                          <Badge variant="outline" className="text-xs font-normal gap-1.5">
                            <Monitor className="h-3 w-3" /> {selectedScreens.length} Selected
                          </Badge>
                        </div>
                      </div>
                      <div className="divide-y max-h-[350px] overflow-y-auto">
                        {screens
                          ?.filter(screen => selectedScreens.includes(screen.id))
                          .map(screen => (
                            <div key={screen.id} className="flex items-center p-4 hover:bg-muted/30 transition-colors group">
                              <div className={cn(
                                "flex-shrink-0 h-12 w-12 rounded-md overflow-hidden mr-3 flex items-center justify-center border",
                                screen.status === "online" ? "bg-green-500/10 border-green-500/30" : "bg-muted border-muted-foreground/20"
                              )}>
                                <Monitor className={cn(
                                  "h-5 w-5",
                                  screen.status === "online" ? "text-green-500" : "text-muted-foreground/60"
                                )} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center">
                                  <p className="text-sm font-medium truncate">{screen.name}</p>
                                  <Badge className={cn(
                                    "ml-2 rounded-full py-0 h-4 px-2 text-[10px] font-normal",
                                    screen.status === "online" 
                                      ? "bg-green-500/15 text-green-600 border-green-600/20" 
                                      : "bg-gray-200 text-gray-500 border-gray-300"
                                  )}>
                                    {screen.status === "online" ? "Online" : "Offline"}
                                  </Badge>
                                </div>
                                <p className="text-xs text-muted-foreground truncate mt-1">
                                  {screen.location || "No location specified"}
                                </p>
                              </div>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-muted-foreground hover:text-destructive rounded-full opacity-0 group-hover:opacity-100 transition-opacity" 
                                onClick={() => toggleScreenSelection(screen.id)}
                                title="Remove"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                      </div>
                    </>
                  )}
                </div>
                
                {/* Screen Selection Dialog */}
                <Dialog open={screenSearchOpen} onOpenChange={setScreenSearchOpen}>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Select Screens</DialogTitle>
                    </DialogHeader>
                    
                    <div className="flex items-center px-3 py-2 border rounded-md">
                      <Search className="mr-2 h-4 w-4 text-muted-foreground" />
                      <Input
                        value={screenSearchQuery}
                        onChange={(e) => setScreenSearchQuery(e.target.value)}
                        placeholder="Search screens by name or location..."
                        className="border-0 outline-none focus-visible:ring-0 focus-visible:ring-offset-0"
                      />
                    </div>
                    
                    <div className="flex flex-wrap gap-2 my-2">
                      <Button 
                        variant={selectedScreenStatusTag === "" ? "default" : "outline"} 
                        size="sm" 
                        onClick={() => setSelectedScreenStatusTag("")}
                        className="h-8 text-xs rounded-full"
                      >
                        All Status
                      </Button>
                      <Button 
                        variant={selectedScreenStatusTag === "online" ? "default" : "outline"} 
                        size="sm" 
                        onClick={() => setSelectedScreenStatusTag("online")}
                        className="h-8 text-xs rounded-full flex items-center gap-1"
                      >
                        <span className="h-2 w-2 rounded-full bg-green-500"></span> Online
                      </Button>
                      <Button 
                        variant={selectedScreenStatusTag === "offline" ? "default" : "outline"} 
                        size="sm" 
                        onClick={() => setSelectedScreenStatusTag("offline")}
                        className="h-8 text-xs rounded-full flex items-center gap-1"
                      >
                        <span className="h-2 w-2 rounded-full bg-gray-400"></span> Offline
                      </Button>
                      
                      {screenTags.length > 0 && screenTags.map(tag => (
                        <Button 
                          key={tag.id}
                          variant={selectedScreenTag === tag.id ? "default" : "outline"} 
                          size="sm" 
                          onClick={() => setSelectedScreenTag(tag.id)}
                          className="h-8 text-xs rounded-full"
                        >
                          {tag.name}
                        </Button>
                      ))}
                    </div>
                    
                    <div className="border rounded-md mt-2">
                      <div className="p-3 border-b bg-muted/40 flex justify-between items-center">
                        <span className="text-sm font-medium">Available Screens</span>
                        <Badge variant="outline">
                          Selected: {selectedScreens.length}
                        </Badge>
                      </div>
                      <div className="max-h-[300px] overflow-y-auto divide-y">
                        {screens
                          ?.filter(screen => {
                            // Filter by search query
                            const matchesQuery = !screenSearchQuery || 
                              screen.name.toLowerCase().includes(screenSearchQuery.toLowerCase()) ||
                              (screen.location && screen.location.toLowerCase().includes(screenSearchQuery.toLowerCase()));
                            
                            // Filter by status tag
                            const matchesStatusTag = !selectedScreenStatusTag || screen.status === selectedScreenStatusTag;
                            
                            // Filter by screen tag
                            const matchesScreenTag = !selectedScreenTag || 
                              (screenTags.find(tag => tag.id === selectedScreenTag)?.screens || []).includes(screen.id);
                            
                            return matchesQuery && matchesStatusTag && matchesScreenTag;
                          })
                          .map(screen => (
                            <div 
                              key={screen.id} 
                              className="flex items-center p-3 hover:bg-muted/30 cursor-pointer"
                              onClick={() => toggleScreenSelection(screen.id)}
                            >
                              <Checkbox 
                                checked={selectedScreens.includes(screen.id)} 
                                className="mr-3"
                              />
                              <div className={cn(
                                "flex-shrink-0 h-10 w-10 rounded-md overflow-hidden mr-3 flex items-center justify-center border",
                                screen.status === "online" ? "bg-green-500/10 border-green-500/30" : "bg-muted border-muted-foreground/20"
                              )}>
                                <Monitor className={cn(
                                  "h-4 w-4",
                                  screen.status === "online" ? "text-green-500" : "text-muted-foreground/60"
                                )} />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{screen.name}</p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {screen.location || "No location"}
                                </p>
                              </div>
                              <Badge className={cn(
                                "rounded-full py-0 h-5 px-2 text-xs font-normal",
                                screen.status === "online" 
                                  ? "bg-green-500/15 text-green-600 border-green-600/20" 
                                  : "bg-gray-200 text-gray-500 border-gray-300"
                              )}>
                                {screen.status}
                              </Badge>
                            </div>
                          ))}
                          
                        {screens?.filter(screen => {
                          const matchesQuery = !screenSearchQuery || 
                            screen.name.toLowerCase().includes(screenSearchQuery.toLowerCase()) ||
                            (screen.location && screen.location.toLowerCase().includes(screenSearchQuery.toLowerCase()));
                          
                          const matchesStatusTag = !selectedScreenStatusTag || screen.status === selectedScreenStatusTag;
                          
                          const matchesScreenTag = !selectedScreenTag || 
                            (screenTags.find(tag => tag.id === selectedScreenTag)?.screens || []).includes(screen.id);
                          
                          return matchesQuery && matchesStatusTag && matchesScreenTag;
                        }).length === 0 && (
                          <div className="p-6 text-center">
                            <FolderSearch className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                            <p className="text-muted-foreground">No screens found matching your criteria</p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setScreenSearchOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={() => setScreenSearchOpen(false)}>
                        Done
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </TabsContent>
            </Tabs>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}