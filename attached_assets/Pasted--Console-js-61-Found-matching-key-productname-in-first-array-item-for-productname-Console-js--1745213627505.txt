﻿
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Cleared API data interval
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiii
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Cleared API data interval
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Cleared API data interval
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Cleared API data interval
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii