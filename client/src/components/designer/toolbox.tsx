import { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Ta<PERSON>Trigger 
} from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useMedia } from "@/hooks/use-media";
import { 
  Type, 
  Image, 
  Play, 
  Square, 
  Circle, 
  Calendar,
  Plus,
  File,
  Video,
  Link,
  Layers,
  Search,
  X,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface ToolboxProps {
  teamId: string;
  onAddElement: (type: string, content?: any) => void;
  selectedElement?: any;
  onAddToPlaylist?: (item: any) => void;
}

const Toolbox = ({ teamId, onAddElement, selectedElement, onAddToPlaylist }: ToolboxProps) => {
  const { mediaItems, isLoading } = useMedia(teamId);
  const [activeTab, setActiveTab] = useState("elements");
  const [searchQuery, setSearchQuery] = useState("");
  const [cachedMediaItems, setCachedMediaItems] = useState<any[]>([]);

  useEffect(() => {
    if (mediaItems.length > 0) {
      // Only set cached items if there's any media retrieved
      setCachedMediaItems(mediaItems);
    }
  }, [mediaItems]); // Listen to `mediaItems` changes
  
  const getMediaItems = () => {
    if (isLoading) {
      return (
        <div className="py-8 text-center text-muted-foreground">
          Loading media...
        </div>
      );
    }
    if (cachedMediaItems.length === 0) {
      return <div>No media items found</div>;
    }
    if (!mediaItems || mediaItems.length === 0) {
      return (
        <div className="py-8 text-center text-muted-foreground">
          <File className="mx-auto h-8 w-8 mb-2 opacity-50" />
          <p>No media items found</p>
          <p className="text-xs mt-1">Upload media files first</p>
        </div>
      );
    }
    
    // Filter media items based on search query
    const filteredItems = mediaItems.filter(item => {
      const name = item?.name || 'Media Item';
      return name.toLowerCase().includes(searchQuery.toLowerCase());
    });
    
    if (filteredItems.length === 0) {
      return (
        <div className="py-8 text-center text-muted-foreground">
          <File className="mx-auto h-8 w-8 mb-2 opacity-50" />
          <p>No matching media found</p>
          <p className="text-xs mt-1">Try a different search term</p>
        </div>
      );
    }
    
    return (
      <div className="grid grid-cols-2 gap-2 max-w-full overflow-hidden">
        {filteredItems.map((item) => {
          // Handle property name inconsistencies with optional chaining
          const mediaType = item?.fileType || item?.file_type || '';
          const isImage = mediaType && mediaType.startsWith('image/');
          const isVideo = mediaType && mediaType.startsWith('video/');
          // Handle both camelCase and snake_case property names
          const thumbnailUrl = item?.thumbnailUrl || (item as any)?.thumbnail_url;
          const name = item?.name || 'Media Item';
          
          let mediaPreview;
          if (thumbnailUrl) {
            mediaPreview = (
              <img
                src={thumbnailUrl}
                alt={name}
                className="w-full h-full object-cover"
              />
            );
          } else if (isVideo) {
            mediaPreview = (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <Play className="h-8 w-8 text-muted-foreground" />
              </div>
            );
          } else if (isImage) {
            mediaPreview = (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <Image className="h-8 w-8 text-muted-foreground" />
              </div>
            );
          } else {
            mediaPreview = (
              <div className="w-full h-full bg-muted flex items-center justify-center">
                <File className="h-8 w-8 text-muted-foreground" />
              </div>
            );
          }
          
          return (
            <div
              key={item.id}
              className={cn(
                "border rounded cursor-pointer overflow-hidden h-24 relative group w-full",
                !isImage && !isVideo && "opacity-50 pointer-events-none"
              )}
              onClick={() => handleAddMediaElement(item)}
            >
              {mediaPreview}
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity">
                <Plus className="h-8 w-8 text-white" />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const handleAddTextElement = () => {
    onAddElement('text', {
      text: 'Sample Text',
      style: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '32px',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'center',
        verticalAlign: 'middle',
        backgroundColor: 'transparent',
      }
    });
  };

  const handleAddShapeElement = () => {
    onAddElement('shape', {
      shape: 'rectangle', // Default shape, can be changed via properties panel
      fill: '#f3f4f6',
      stroke: '#000000',
      strokeWidth: 0,
    });
  };

  const handleAddDateElement = () => {
    onAddElement('date', {
      locale: 'en-US',
      options: {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      },
      style: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '32px',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'center',
        verticalAlign: 'middle',
        backgroundColor: 'transparent',
      }
    });
  };

  const handleAddApiElement = () => {
    onAddElement('api', {
      dataField: '',
      placeholderUrl: '',
      subtype: 'api.text',  // Default data type is text
      style: {           // Default text style
        fontFamily: 'Inter, sans-serif',
        fontSize: '32px',
        fontWeight: 'normal',
        color: '#000000',
        textAlign: 'center',
        verticalAlign: 'middle',
        backgroundColor: 'transparent',
      }
    });
  };

  const handleAddMultimediaElement = () => {
    onAddElement('multimedia', {
      playlist: [],
      currentIndex: 0,
      isPlaying: false
    });
  };

  const handleAddPlaylistElement = () => {
    onAddElement('playlist', {
      playlist: [],      // Array for media playlist items
      currentIndex: 0,   // Current playing index
      isPlaying: false,  // Playback state
    });
  };

  const handleAddMediaElement = (item: any) => {
    // Handle both possible property name formats and provide defaults
    const mediaType = item?.fileType || item?.file_type || '';
    const fileUrl = item?.fileUrl || item?.file_url || '';
    const name = item?.name || 'Media Item';

    if (!item || !fileUrl) {
      console.error('Invalid media item or missing URL:', item);
      return;
    }

    // If a playlist element is selected and onAddToPlaylist is provided, add to playlist
    if (selectedElement?.type === 'playlist' && onAddToPlaylist) {
      // Get the thumbnail URL
      const thumbnailUrl = item?.thumbnailUrl || item?.thumbnail_url;

      onAddToPlaylist({
        id: item.id,
        url: fileUrl,
        fileType: mediaType, // Use consistent property name
        name: name,
        thumbnailUrl: thumbnailUrl,
        duration: mediaType && mediaType.startsWith('image/') ? 5 : 0, // Default 5 seconds for images
      });
      return;
    }

    // Otherwise, continue with adding as a separate element
    if (mediaType && mediaType.startsWith('image/')) {
      onAddElement('image', {
        src: fileUrl,
        alt: name,
      });
    } else if (mediaType && mediaType.startsWith('video/')) {
      onAddElement('video', {
        src: fileUrl,
        type: mediaType,
      });
    }
  };

  // Set to elements tab if no playlist element is selected
  useEffect(() => {
    if (!selectedElement || selectedElement.type !== 'playlist') {
      setActiveTab("elements");
    }
  }, [selectedElement]);
  
  return (
    <div className="w-72 max-w-72 border-r bg-background h-full flex flex-col overflow-hidden">
      <div className="p-4 border-b">
        <h3 className="font-medium">Design Toolbox</h3>
      </div>
      
      <Tabs defaultValue="elements" className="flex-1 flex flex-col" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={cn(
          "grid mx-4 mt-2",
          selectedElement?.type === 'playlist' ? "grid-cols-2" : "grid-cols-1"
        )}>
          <TabsTrigger value="elements">Elements</TabsTrigger>
          {selectedElement?.type === 'playlist' && (
            <TabsTrigger value="media">Media</TabsTrigger>
          )}
        </TabsList>
        
        <ScrollArea className="flex-1 max-w-full">
          <TabsContent value="elements" className="p-4 space-y-2 w-full max-w-full">
            <div>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleAddTextElement}
              >
                <Type className="mr-2 h-4 w-4" />
                Text
              </Button>
            </div>
            
            <div>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => handleAddShapeElement()}
              >
                <svg viewBox="0 0 24 24" className="mr-2 h-4 w-4" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="7" height="7" />
                  <circle cx="17" cy="6.5" r="3.5" />
                  <polygon points="3 21 7 14 11 21" />
                  <path d="M17.5 14 L22 21 L13 21 Z" />
                </svg>
                Shape
              </Button>
            </div>
            
            <div>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleAddDateElement}
              >
                <Calendar className="mr-2 h-4 w-4" />
                Date/Time
              </Button>
            </div>
            
            <div>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleAddApiElement}
              >
                <Link className="mr-2 h-4 w-4" />
                API Element
              </Button>
            </div>
            
            <div>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={handleAddPlaylistElement}
              >
                <Play className="mr-2 h-4 w-4" />
                Multimedia
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="media" className="p-4 space-y-4 max-w-full">
            <div className="flex flex-col space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Media Library</h4>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Search media..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-8 w-full"
                />
                {searchQuery ? (
                  <button 
                    onClick={() => setSearchQuery("")}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </button>
                ) : (
                  <Search className="absolute right-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                )}
              </div>
            </div>
            
            <div className="max-w-full overflow-hidden">
              {getMediaItems()}
            </div>
          </TabsContent>
        </ScrollArea>
      </Tabs>
    </div>
  );
};

export { Toolbox };