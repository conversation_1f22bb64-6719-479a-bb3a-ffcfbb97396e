# Supabase Configuration
# You need to replace these with your actual Supabase project credentials
# Get these from your Supabase project dashboard: https://app.supabase.com/

# Your Supabase project URL
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Optional: Database URL for direct database access (if using Dr<PERSON>zle)
DATABASE_URL=postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres
