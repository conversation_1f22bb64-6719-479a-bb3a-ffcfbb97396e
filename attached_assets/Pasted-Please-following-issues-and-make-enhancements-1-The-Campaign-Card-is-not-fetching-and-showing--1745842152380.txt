Please following issues and make enhancements:

1.	The Campaign Card is not fetching and showing “Screens”, “Media” and “Slide” counts for each of the campaign. Check the attached screenshot attached.
2.	The UI to create a new campaign and update existing one are different which is not a good user experience. Use the existing Edit Campaign window to add new campaign as well. You will have to keep the track of the campaign_id or action state for Add/Edit mode. 
3.	Make the existing Edit Campaign window bit bigger. Currently the Screen dropdown list as well as Media/Slides dropdown list overlaps the “Save Changes” button and I think sometimes it propages the click event to save button. So please handle such situation either by making the window bigger or making other adjustments.
4.	When Save/Update Campaign button is clicked, the save function should follow steps below to insert/update records in the supabase database tables;
a.	If the action mode is “Add” or campaign_id is null, insert new campaign in the “campaigns” supabase table, wait for the response to get campaing_id of the newly created record.
b.	If the action mode is “Update” or campaign_id is NOT NULL, update the existing record in the “campaigns” supabase table and wait for the response.
c.	Once the “campaign” record is added/updated;
i.	Using the “campaign_id”, delete existing records from the “campaign_medias” supabase table and wait for the response.
ii.	Using the “campaign_id”, delete existing records from the “campaign_screens” supabase table and wait for the response.
d.	Once the above record deletion responses are received;
i.	Using the “campaign_id” and selected content list from the “Media/Slides” tab, insert records in the “campaign_medias” supabase table and wait for the response.
ii.	Using the “campaign_id” and selected screens list from the “Screens” tab, insert records in the “campaign_screens” supabase table and wait for the response.
e.	Once the above records insertion response is received, close the Window and refresh the campaign list.
f.	The Save function must strictly follow the above workflow. Please show wait indicator for the entire save process till the window is closed and campaign list is refreshed.
