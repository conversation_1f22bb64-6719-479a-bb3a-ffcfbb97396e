Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 859daa19-dc14-41d8-bdb6-e57a7b659dca
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 859daa19-dc14-41d8-bdb6-e57a7b659dca
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 API data index reset to 0 at start of preview
Console.js:61 Element 859daa19-dc14-41d8-bdb6-e57a7b659dca has API mapping: true 
{productname: true}
Console.js:61 Element 859daa19-dc14-41d8-bdb6-e57a7b659dca has data field: true productname
Console.js:61 Found 1 API elements for data cycling
Console.js:61 Using top-level array with 23 records
Console.js:61 Updating element 859daa19-dc14-41d8-bdb6-e57a7b659dca with data record at index 0/22 (global index: 0)
Console.js:61 Updating text element 859daa19-dc14-41d8-bdb6-e57a7b659dca with field "productname" at index 0 (global: 0)
Console.js:61 Field value: "Jarritos Tutti Fruttiiiiii"
Console.js:61 Updated text element 859daa19-dc14-41d8-bdb6-e57a7b659dca with "Jarritos Tutti Fruttiiiiii" (index: 0)
Console.js:61 Setting up API data cycling interval with 5s duration
Console.js:61 API data cycling interval set with ID: 337
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 859daa19-dc14-41d8-bdb6-e57a7b659dca
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Preview active: Looking for video elements to play
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 859daa19-dc14-41d8-bdb6-e57a7b659dca
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Preview no longer active, stopping API data cycling interval