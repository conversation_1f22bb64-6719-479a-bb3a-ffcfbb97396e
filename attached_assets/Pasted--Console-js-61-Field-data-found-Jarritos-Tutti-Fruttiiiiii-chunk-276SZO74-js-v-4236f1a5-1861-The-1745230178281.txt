﻿
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Preview active: Looking for video elements to play
<PERSON>sole.js:61 Preview active: Found 0 video elements to play
<PERSON>sole.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 63f64506-2ecf-4680-9b95-2dd7a553cdb9
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiiiii
Console.js:61 Preview no longer active - stopping cycling interval
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 CLEANUP: Clearing all preview timers and event handlers
Console.js:61 CLEANUP: No active API cycling interval to clear
Console.js:61 CLEANUP: All preview resources have been released
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 63f64506-2ecf-4680-9b95-2dd7a553cdb9
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 CLEANUP: Clearing all preview timers and event handlers
Console.js:61 CLEANUP: No active API cycling interval to clear
Console.js:61 CLEANUP: All preview resources have been released
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 63f64506-2ecf-4680-9b95-2dd7a553cdb9
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 63f64506-2ecf-4680-9b95-2dd7a553cdb9
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 63f64506-2ecf-4680-9b95-2dd7a553cdb9
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii