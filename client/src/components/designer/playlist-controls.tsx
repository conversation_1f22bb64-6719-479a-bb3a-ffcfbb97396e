import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CardContent, Card } from "@/components/ui/card";


import { 
  Video, 
  Image, 
  ChevronUp, 
  ChevronDown, 
  X, 
  Clock,
  Plus
} from "lucide-react";
import { cn } from "@/lib/utils";

interface PlaylistControlsProps {
  teamId: string;
  content: any;
  updateContent: (updates: any) => void;
}

export function PlaylistControls({ teamId, content, updateContent }: PlaylistControlsProps) {
  // Initialize playlist if it doesn't exist
  const playlist = content.playlist || [];
  
  // When a media item is selected from the Media tab in the toolbox
  // it will call this handler function through the designer page
  const addToPlaylist = (mediaItem: any) => {
    if (!mediaItem || !mediaItem.id) return;
    
    // Check if item already exists in playlist
    const existingItem = playlist.find((item: any) => item.mediaId === mediaItem.id);
    if (existingItem) return;
    
    // Create a new playlist item from the media item data
    const newItem = {
      id: uuidv4(),
      mediaId: mediaItem.id,
      name: mediaItem.name || 'Media Item',
      fileUrl: mediaItem.url,
      thumbnailUrl: mediaItem.thumbnailUrl || mediaItem.thumbnail_url,
      fileType: mediaItem.type || mediaItem.fileType,
      duration: mediaItem.duration || 5, // Default 5 seconds for images, passed from toolbox
      sortOrder: playlist.length
    };
    
    // Update the playlist
    const updatedPlaylist = [...playlist, newItem];
    updateContent({ playlist: updatedPlaylist });
  };
  
  // Remove media item from playlist
  const removeFromPlaylist = (itemId: string) => {
    const updatedPlaylist = playlist.filter((item: any) => item.id !== itemId);
    
    // Re-index sort order
    updatedPlaylist.forEach((item: any, index: number) => {
      item.sortOrder = index;
    });
    
    updateContent({ playlist: updatedPlaylist });
  };
  
  // Change duration for an image
  const updateItemDuration = (itemId: string, duration: number) => {
    const updatedPlaylist = playlist.map((item: any) => {
      if (item.id === itemId) {
        return { ...item, duration };
      }
      return item;
    });
    
    updateContent({ playlist: updatedPlaylist });
  };
  
  // Move item up in the playlist
  const moveItemUp = (itemId: string) => {
    const itemIndex = playlist.findIndex((item: any) => item.id === itemId);
    if (itemIndex <= 0) return; // Already at the top
    
    const updatedPlaylist = [...playlist];
    
    // Swap with the item above
    const temp = updatedPlaylist[itemIndex];
    updatedPlaylist[itemIndex] = updatedPlaylist[itemIndex - 1];
    updatedPlaylist[itemIndex - 1] = temp;
    
    // Update sort order
    updatedPlaylist.forEach((item: any, index: number) => {
      item.sortOrder = index;
    });
    
    updateContent({ playlist: updatedPlaylist });
  };
  
  // Move item down in the playlist
  const moveItemDown = (itemId: string) => {
    const itemIndex = playlist.findIndex((item: any) => item.id === itemId);
    if (itemIndex === -1 || itemIndex >= playlist.length - 1) return; // Already at the bottom
    
    const updatedPlaylist = [...playlist];
    
    // Swap with the item below
    const temp = updatedPlaylist[itemIndex];
    updatedPlaylist[itemIndex] = updatedPlaylist[itemIndex + 1];
    updatedPlaylist[itemIndex + 1] = temp;
    
    // Update sort order
    updatedPlaylist.forEach((item: any, index: number) => {
      item.sortOrder = index;
    });
    
    updateContent({ playlist: updatedPlaylist });
  };
  
  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow flex flex-col space-y-2 overflow-hidden">
        <div className="flex justify-between items-center">
          <Label>Current Playlist</Label>
          <span className="text-xs text-muted-foreground">{playlist.length} items</span>
        </div>
        
        {playlist.length === 0 ? (
          <div className="text-center p-4 bg-muted/30 rounded-md">
            <p className="text-sm text-muted-foreground">No media items in playlist</p>
          </div>
        ) : (
          <ScrollArea className="flex-grow border rounded-md">
            <div className="p-2 space-y-2">
              {playlist.map((item: any, index: number) => {
                // Handle case where fileType might be undefined
                const fileType = item.fileType || '';
                const isImage = fileType.startsWith('image/');
                
                return (
                  <Card key={item.id} className="overflow-hidden">
                    <div className="flex items-center p-2">
                      <div className="h-12 w-12 bg-muted rounded overflow-hidden flex-shrink-0">
                        {item.thumbnailUrl ? (
                          <img 
                            src={item.thumbnailUrl} 
                            alt={item.name} 
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              // The image failed to load
                              // Hide the broken image
                              e.currentTarget.style.display = 'none';
                              // Safely add fallback icon only if parent element exists
                              const parent = e.currentTarget.parentElement;
                              if (parent) {
                                parent.innerHTML = isImage 
                                  ? '<div class="h-full w-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-muted-foreground"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg></div>'
                                  : '<div class="h-full w-full flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-6 w-6 text-muted-foreground"><polygon points="23 7 16 12 23 17 23 7"/><rect width="15" height="14" x="1" y="5" rx="2" ry="2"/></svg></div>';
                              }
                            }}
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center">
                            {isImage ? (
                              <Image className="h-6 w-6 text-muted-foreground" />
                            ) : (
                              <Video className="h-6 w-6 text-muted-foreground" />
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 ml-2 overflow-hidden mr-2">
                        <p className="text-sm font-medium truncate">{item.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {isImage ? 'Image' : 'Video'} - #{index + 1}
                        </p>
                      </div>
                      
                      <div className="flex flex-col space-y-1">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-6 w-6"
                          onClick={() => moveItemUp(item.id)}
                          disabled={index === 0}
                        >
                          <ChevronUp className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-6 w-6"
                          onClick={() => moveItemDown(item.id)}
                          disabled={index === playlist.length - 1}
                        >
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6 text-destructive hover:text-destructive"
                        onClick={() => removeFromPlaylist(item.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {isImage && (
                      <div className="px-2 pb-2">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div className="flex-1">
                            <Label className="text-xs">Duration (seconds)</Label>
                            <div className="flex gap-2 items-center">
                              <Input
                                type="number"
                                value={item.duration || 5}
                                min={1}
                                max={60}
                                className="h-7 text-sm"
                                onChange={(e) => updateItemDuration(item.id, parseInt(e.target.value) || 5)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </div>
      
      <div className="text-center py-2 text-muted-foreground text-xs mt-auto">
        Use the Media tab in the toolbox to add items to this playlist.
      </div>
    </div>
  );
}