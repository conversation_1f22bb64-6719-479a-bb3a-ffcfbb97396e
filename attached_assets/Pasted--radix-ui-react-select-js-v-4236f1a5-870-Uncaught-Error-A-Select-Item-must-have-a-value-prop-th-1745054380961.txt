@radix-ui_react-select.js?v=4236f1a5:870 Uncaught Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at @radix-ui_react-sele…s?v=4236f1a5:870:13
    at renderWithHooks (chunk-276SZO74.js?v=4236f1a5:11548:26)
    at updateForwardRef (chunk-276SZO74.js?v=4236f1a5:14325:28)
    at beginWork (chunk-276SZO74.js?v=4236f1a5:15946:22)
    at HTMLUnknownElement.callCallback2 (chunk-276SZO74.js?v=4236f1a5:3674:22)
    at Object.invokeGuardedCallbackDev (chunk-276SZO74.js?v=4236f1a5:3699:24)
    at invokeGuardedCallback (chunk-276SZO74.js?v=4236f1a5:3733:39)
    at beginWork$1 (chunk-276SZO74.js?v=4236f1a5:19765:15)
    at performUnitOfWork (chunk-276SZO74.js?v=4236f1a5:19198:20)
    at workLoopSync (chunk-276SZO74.js?v=4236f1a5:19137:13)
Console.js:61 The above error occurred in the <SelectItem> component:

    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@radix-ui_react-select.js?v=4236f1a5:843:7
    at _c11 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/select.tsx:207:13)
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:52:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:33:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-LP3SNE3I.js?v=4236f1a5:98:15
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…kspace/node_modules/.vite/deps/@radix-ui_react-select.js?v=4236f1a5:754:13
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:52:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:33:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-LP3SNE3I.js?v=4236f1a5:98:15
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-XSD2Y4RK.js?v=4236f1a5:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…kspace/node_modules/.vite/deps/@radix-ui_react-select.js?v=4236f1a5:298:58
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:52:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-D3CTYCI6.js?v=4236f1a5:33:11
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…nner/workspace/node_modules/.vite/deps/chunk-JSKZOJSG.js?v=4236f1a5:260:22
    at SelectPortal
    at _c7 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/select.tsx:122:12)
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-XSD2Y4RK.js?v=4236f1a5:38:15)
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-LP3SNE3I.js?v=4236f1a5:28:15)
    at CollectionProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-LP3SNE3I.js?v=4236f1a5:89:13)
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-XSD2Y4RK.js?v=4236f1a5:38:15)
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…ner/workspace/node_modules/.vite/deps/chunk-ATB2HJOV.js?v=4236f1a5:1915:15)
    at Popper (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…ner/workspace/node_modules/.vite/deps/chunk-ATB2HJOV.js?v=4236f1a5:1972:11)
    at Select (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-select.js?v=4236f1a5:86:5)
    at div
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at Presence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-F2PRPNCO.js?v=4236f1a5:24:11)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=4236f1a5:175:13
    at _c5 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-XSD2Y4RK.js?v=4236f1a5:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=4236f1a5:53:7
    at div
    at ElementProperties (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/element-properties.tsx:57:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745053914498:61:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745053914498&v=wdhTk96HrJ5tJHh-Z6cKh:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4236f1a5:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4236f1a5:247:17)
    at App
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=4236f1a5:2805:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.

Console.js:61 Error caught by ErrorBoundary: Error: A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.
    at @radix-ui_react-sele…s?v=4236f1a5:870:13
    at renderWithHooks (chunk-276SZO74.js?v=4236f1a5:11548:26)
    at updateForwardRef (chunk-276SZO74.js?v=4236f1a5:14325:28)
    at beginWork (chunk-276SZO74.js?v=4236f1a5:15946:22)
    at beginWork$1 (chunk-276SZO74.js?v=4236f1a5:19753:22)
    at performUnitOfWork (chunk-276SZO74.js?v=4236f1a5:19198:20)
    at workLoopSync (chunk-276SZO74.js?v=4236f1a5:19137:13)
    at renderRootSync (chunk-276SZO74.js?v=4236f1a5:19116:15)
    at recoverFromConcurrentError (chunk-276SZO74.js?v=4236f1a5:18736:28)
    at performSyncWorkOnRoot (chunk-276SZO74.js?v=4236f1a5:18879:28) 
{componentStack: '\n    at https://29810b14-7e88-452d-9d46-13ec035240…/deps/@tanstack_react-query.js?v=4236f1a5:2805:3)'}