import React, { useState, useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useBilling } from "@/hooks/use-billing";
import { format } from "date-fns";
import { Search, RefreshCw, FileText } from "lucide-react";

interface ScreenSubscriptionsProps {
  teamId: string;
  teamName: string;
}

export function ScreenSubscriptions({ teamId, teamName }: ScreenSubscriptionsProps) {
  const {
    screenSubscriptions,
    subscriptionsLoading,
    calculateNextBillingDate,
    generateInvoiceId
  } = useBilling(teamId);

  const [searchTerm, setSearchTerm] = useState("");

  // Filter subscriptions based on search term
  const filteredSubscriptions = useMemo(() => {
    if (!searchTerm.trim()) return screenSubscriptions;

    const searchLower = searchTerm.toLowerCase();
    return screenSubscriptions.filter(subscription => {
      const screenName = (subscription.screenName || '').toLowerCase();
      const location = (subscription.screenLocation || '').toLowerCase();
      const screenCode = (subscription.screenCode || '').toLowerCase();
      const status = (subscription.subscriptionStatus || '').toLowerCase();
      const billingCycle = (subscription.billingCycle || '').toLowerCase();
      const invoiceId = generateInvoiceId(teamName, subscription.invoiceId).toLowerCase();

      return (
        screenName.includes(searchLower) ||
        location.includes(searchLower) ||
        screenCode.includes(searchLower) ||
        status.includes(searchLower) ||
        billingCycle.includes(searchLower) ||
        invoiceId.includes(searchLower)
      );
    });
  }, [screenSubscriptions, searchTerm, teamName, generateInvoiceId]);

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search subscriptions by screen name, location, code, status, or invoice..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Screen Name</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Screen Code</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Billing Cycle</TableHead>
              <TableHead>Invoice ID</TableHead>
              <TableHead>Next Billing Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {subscriptionsLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                  Loading screen subscriptions...
                </TableCell>
              </TableRow>
            ) : filteredSubscriptions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  {searchTerm.trim() ? `No subscriptions found matching "${searchTerm}"` : "No screen subscriptions found"}
                </TableCell>
              </TableRow>
            ) : (
              filteredSubscriptions.map((subscription, index) => {
              const nextBillingDate = calculateNextBillingDate(
                subscription.invoiceDate,
                subscription.billingCycle || ""
              );

              return (
                <TableRow key={`${subscription.screenCode}-${index}`}>
                  <TableCell className="font-medium">
                    {subscription.screenName}
                  </TableCell>
                  <TableCell>
                    {subscription.screenLocation || "N/A"}
                  </TableCell>
                  <TableCell>
                    <code className="bg-muted px-2 py-1 rounded text-sm">
                      {subscription.screenCode}
                    </code>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        subscription.subscriptionStatus === "active"
                          ? "default"
                          : subscription.subscriptionStatus === "trial"
                          ? "secondary"
                          : "outline"
                      }
                    >
                      {subscription.subscriptionStatus}
                    </Badge>
                  </TableCell>
                  <TableCell className="capitalize">
                    {subscription.billingCycle || "N/A"}
                  </TableCell>
                  <TableCell>
                    {generateInvoiceId(teamName, subscription.invoiceId)}
                  </TableCell>
                  <TableCell>
                    {nextBillingDate ? format(nextBillingDate, "MMM dd, yyyy") : "N/A"}
                  </TableCell>
                </TableRow>
              );
              })
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
