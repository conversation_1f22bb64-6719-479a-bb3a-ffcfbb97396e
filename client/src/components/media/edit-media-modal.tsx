import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { TagInput } from "@/components/media/tag-input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

// Helper function to handle API responses
async function handleApiResponse<T>(response: any): Promise<T> {
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText);
  }
  return await response.json();
}

interface EditMediaModalProps {
  open: boolean;
  onClose: () => void;
  mediaId: string;
  mediaName: string;
  teamId: string;
  initialTags: string[];
}

export function EditMediaModal({
  open,
  onClose,
  mediaId,
  mediaName,
  teamId,
  initialTags,
}: EditMediaModalProps) {
  const [tags, setTags] = useState<string[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Initialize tags when the modal opens
  useEffect(() => {
    if (open) {
      setTags(initialTags || []);
    }
  }, [open, initialTags]);

  const saveTags = async () => {
    if (!mediaId) return;

    setIsSaving(true);
    try {
      // Get current tags for this media
      const currentTagsResponse = await apiRequest('GET', `/api/media/${mediaId}/tags`);
      const currentTags = await handleApiResponse<Array<{id: string, name: string}>>(currentTagsResponse);

      const currentTagNames = currentTags.map(tag => tag.name);
      const currentTagIds = new Map(currentTags.map(tag => [tag.name, tag.id]));

      // Determine which tags to add (in the new list but not in the current list)
      const tagsToAdd = tags.filter(tag => !currentTagNames.includes(tag));

      // Determine which tags to remove (in the current list but not in the new list)
      const tagsToRemove = currentTags.filter(tag => !tags.includes(tag.name));

      console.log("Current tags:", currentTagNames);
      console.log("New tags:", tags);
      console.log("Tags to add:", tagsToAdd);
      console.log("Tags to remove:", tagsToRemove.map(t => t.name));

      // Process tag additions
      if (tagsToAdd.length > 0) {
        // Resolve IDs for new tags (fetch existing or create new)
        const tagIds = await resolveTagIds(tagsToAdd);

        // Add the new tags
        const addPromises = tagIds.map(async (tagId) => {
          const response = await apiRequest('POST', `/api/media/${mediaId}/tags/${tagId}`);
          return handleApiResponse(response);
        });

        await Promise.all(addPromises);
        console.log("Tags added successfully.");
      }

      // Process tag removals
      if (tagsToRemove.length > 0) {
        const removePromises = tagsToRemove.map(async (tag) => {
          const response = await apiRequest('DELETE', `/api/media/${mediaId}/tags/${tag.id}`);
          return response;
        });

        await Promise.all(removePromises);
        console.log("Tags removed successfully.");
      }

      // Invalidate queries to refresh data using the new optimized query keys
      queryClient.invalidateQueries({queryKey: [`/api/media/${mediaId}/tags`]});
      queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/media-with-tags`]});
      queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/tags`]});

      toast({
        title: "Tags updated",
        description: "Media tags have been updated successfully.",
      });

      onClose();
    } catch (error) {
      console.error("Error updating tags:", error);
      toast({
        title: "Error updating tags",
        description: error instanceof Error ? error.message : "An unknown error occurred.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Helper function to resolve tag IDs or create new tags
  const resolveTagIds = async (tagNames: string[]): Promise<string[]> => {
    try {
      // First, check if tags already exist
      console.log('Checking if tags exist:', tagNames);

      const existingTagsResponse = await apiRequest('GET', `/api/teams/${teamId}/tags`);

      const existingTags = await handleApiResponse<Array<{id: string, name: string}>>(existingTagsResponse);
      console.log('Processed tags response:', existingTags);

      // Map of tag names to IDs for existing tags
      const tagMap = new Map<string, string>();
      existingTags.forEach(tag => {
        tagMap.set(tag.name, tag.id);
      });

      // Resolve tags - use existing ID or create new tag
      const resolveTagPromises = tagNames.map(async (tagName) => {
        if (tagMap.has(tagName)) {
          console.log('Found existing tag:', tagName, 'with ID:', tagMap.get(tagName));
          return tagMap.get(tagName)!;
        } else {
          console.log('Creating new tag:', tagName);

          // Create a new tag
          const newTagData = {
            teamId: teamId,
            name: tagName,
            color: generateRandomColor(),
          };
          const newTagResponse = await apiRequest('POST', '/api/tags', newTagData);

          const newTag = await handleApiResponse<{id: string}>(newTagResponse);
          console.log('Created new tag:', tagName, 'with ID:', newTag.id);

          // Invalidate tag queries to ensure UI is updated
          queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/tags`]});

          return newTag.id;
        }
      });

      return await Promise.all(resolveTagPromises);
    } catch (error) {
      console.error('Error resolving tags:', error);
      throw error;
    }
  };

  // Helper function to generate a random color for new tags
  const generateRandomColor = () => {
    const colors = [
      "#ef4444", "#f97316", "#f59e0b", "#eab308", "#84cc16",
      "#22c55e", "#10b981", "#14b8a6", "#06b6d4", "#0ea5e9",
      "#3b82f6", "#6366f1", "#8b5cf6", "#a855f7", "#d946ef",
      "#ec4899", "#f43f5e"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Media Tags</DialogTitle>
          <DialogDescription>
            Manage tags associated with this media item
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <h3 className="text-sm font-medium mb-1">{mediaName}</h3>
          <p className="text-xs text-gray-500 mb-4">
            Add or remove tags to help organize and find this media
          </p>

          <div className="space-y-4">
            <div>
              <label htmlFor="tags" className="block text-sm font-medium mb-2">
                Tags
              </label>
              <TagInput
                value={tags}
                onChange={setTags}
                teamId={teamId}
              />
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSaving}>
            Cancel
          </Button>
          <Button onClick={saveTags} disabled={isSaving}>
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}