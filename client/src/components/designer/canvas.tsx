import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import Gauge from "./gauge-speedometer-new";

import { 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  Minimize,
  MousePointer,
  Layers,
  Image,
  Video,
  Play,
  File,
  Link,
  Gauge as GaugeIcon
} from "lucide-react";

interface CanvasProps {
  width: number;
  height: number;
  elements: CanvasElement[];
  onSelectElement: (id: string | null) => void;
  onUpdateElement: (id: string, updates: Partial<CanvasElement>) => void;
  onDeleteElement: (id: string) => void;
  selectedElementId: string | null;
  backgroundColor?: string;
  isPreviewMode?: boolean;
  currentPreviewElements?: any[];
  cycleToNextRecord: () => void;
}

export interface CanvasElement {
  id: string;
  type: 'text' | 'image' | 'video' | 'shape' | 'date' | 'multimedia' | 'playlist' | 'api';
  x: number;
  y: number;
  width: number;
  height: number;
  content: any;
  style: any;
  teamId?: string;
  zIndex?: number; // Element layer order
  animation?: {
    type: 'fade-in' | 'slide-in' | 'zoom-in' | 'bounce' | 'rotate' | 'flip-x' | 'flip-y' | 'none';
    direction?: 'left' | 'right' | 'top' | 'bottom';
    duration: number; // in milliseconds
    delay: number; // in milliseconds
    easing?: string; // CSS easing function
    _previewKey?: number; // Used to force animation re-renders
  };
}

export function Canvas({
  width,
  height,
  elements,
  onSelectElement,
  onUpdateElement,
  onDeleteElement,
  selectedElementId,
  backgroundColor = "#ffffff",
  isPreviewMode = false,
  currentPreviewElements = [],
  cycleToNextRecord
}: CanvasProps) {
  const [zoom, setZoom] = useState(0.5); // Default zoom set to 50%
  const [draggingElement, setDraggingElement] = useState<string | null>(null);
  const [resizingElement, setResizingElement] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  // Add a local state to track API data index for re-renders
  const [localApiDataIndex, setLocalApiDataIndex] = useState<number>(0);
  const lastApiDataIndexRef = useRef<number>(0);

  // Effect to handle video playback when preview mode changes
  useEffect(() => {
    if (isPreviewMode && canvasRef.current) {
      // Find all videos in the canvas
      const videoElements = canvasRef.current.querySelectorAll('video');
      
      // Play all videos
      videoElements.forEach(video => {
        // Make sure video is ready to play
        video.muted = true;
        
        // Force play the video
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error("Error playing video:", error);
          });
        }
      });
    }
  }, [isPreviewMode]);
  
  // Add an interval to check API data index changes and force re-render when it changes
  /*useEffect(() => {
    if (!isPreviewMode) return;
    
    // Reset local index on preview start
    setLocalApiDataIndex(0);
    lastApiDataIndexRef.current = 0;
    
    const checkApiDataIndexInterval = setInterval(() => {
      const apiDataIndex = window.__API_DATA_INDEX__ || 0;
      
      // Check if the API data index has changed
      if (apiDataIndex !== lastApiDataIndexRef.current) {
        console.log(`Canvas detected API data index change: ${lastApiDataIndexRef.current} -> ${apiDataIndex}`);
        
        // Update the last known index
        lastApiDataIndexRef.current = apiDataIndex;
        
        // Update local state to force a re-render of the whole canvas
        setLocalApiDataIndex(apiDataIndex);
      }
    }, 200); // Check more frequently - 5 times per second
    
    return () => {
      clearInterval(checkApiDataIndexInterval);
    };
  }, [isPreviewMode]);
*/
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.1));
  };

  const handleZoomReset = () => {
    setZoom(0.5); // Reset to 50% zoom
  };

  const handleElementMouseDown = (e: React.MouseEvent, elementId: string) => {
    // Stop event propagation to prevent canvas from handling it
    e.stopPropagation();
    
    // Select the element
    onSelectElement(elementId);
    
    // Start dragging the element
    setDraggingElement(elementId);
    
    const element = elements.find(el => el.id === elementId);
    if (element) {
      const rect = (e.target as HTMLDivElement).getBoundingClientRect();
      
      // Calculate drag offset relative to element position
      // This accounts for clicks anywhere in the element, not just the top-left corner
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // Prevent any default browser behavior that might interfere
      e.preventDefault();
    }
  };

  const handleElementResize = (e: React.MouseEvent, elementId: string) => {
    e.stopPropagation();
    e.preventDefault();
    
    setResizingElement(elementId);
  };

  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (draggingElement) {
      setIsDragging(true);
      
      const canvas = canvasRef.current;
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        
        // Adjust coordinates according to zoom factor
        const x = (e.clientX - rect.left) / zoom - dragOffset.x / zoom;
        const y = (e.clientY - rect.top) / zoom - dragOffset.y / zoom;
        
        // No boundary enforcement - elements can be dragged outside canvas
        onUpdateElement(draggingElement, { x, y });
      }
    } else if (resizingElement) {
      const element = elements.find(el => el.id === resizingElement);
      if (element && canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect();
        const canvasX = (e.clientX - rect.left) / zoom;
        const canvasY = (e.clientY - rect.top) / zoom;
        
        // Only enforce minimum size but allow resize beyond canvas bounds
        const newWidth = Math.max(20, canvasX - element.x);
        const newHeight = Math.max(20, canvasY - element.y);
        
        onUpdateElement(resizingElement, { width: newWidth, height: newHeight });
      }
    }
  };

  const handleCanvasMouseUp = (e: React.MouseEvent) => {
    // Don't deselect element on mouse up - we only want to end dragging/resizing
    // The element selection should persist until user explicitly clicks elsewhere
    setDraggingElement(null);
    setResizingElement(null);
    setTimeout(() => setIsDragging(false), 0);
    
    // Prevent event from propagating which could trigger deselection
    e.stopPropagation();
  };

  // Smart path resolver - handles automatic array index application
  const getValueAtPathWithIndex = (data: any, path: string, rowIndex: number): any => {
    if (!path || !data) return null;

    try {
      const keys = path.split('.');
      let current = data;

      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];

        if (Array.isArray(current)) {
          // If we're not at the last key yet and the next level is array-like
          current = current.map(item => item[key]).filter(item => item !== undefined).flat();
        } else if (typeof current === 'object' && current !== null) {
          current = current[key];
        } else {
          return undefined;
        }
      }

      // At this point, current can still be an array, especially if the final property was a list
      if (Array.isArray(current)) {
        return current[rowIndex] !== undefined ? current[rowIndex] : undefined;
      }

      return current;

    } catch (error) {
      console.error(`Error accessing path "${path}" with rowIndex ${rowIndex}:`, error);
      return null;
    }
  };

  const renderElement = (element: CanvasElement) => {
    const isSelected = element.id === selectedElementId;
    
    // Get the current API data index or default to 0 (for template URL processing)
    const globalIndex = typeof window.__API_DATA_INDEX__ === 'number' ? window.__API_DATA_INDEX__ : 0;
    const currentIndex = isPreviewMode ? globalIndex : 0;
    
    const elementStyle = {
      left: `${element.x}px`,
      top: `${element.y}px`,
      width: `${element.width}px`,
      height: `${element.height}px`,
      zIndex: element.zIndex || 0, // Apply zIndex property, default to 0 if not set
      ...element.style
    };

    let content;
    switch (element.type) {
      case 'text': {
        const style = element.content.style || {};
        const displayStyle = {
          ...style,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: style.verticalAlign === 'top' ? 'flex-start' : 
                      style.verticalAlign === 'bottom' ? 'flex-end' : 
                      'center',
          justifyContent: style.textAlign === 'left' ? 'flex-start' : 
                          style.textAlign === 'right' ? 'flex-end' : 
                          'center'
        };
        content = <div style={displayStyle}>{element.content.text || 'Sample Text'}</div>;
        break;
      }
      case 'image':
        content = <img src={element.content.src} alt={element.content.alt || ""} className="w-full h-full object-cover" />;
        break;
      case 'video':
        content = (
          <video 
            className="w-full h-full" 
            controls={!isPreviewMode}
            autoPlay={isPreviewMode}
            muted={isPreviewMode}
            playsInline
            preload="auto"
            src={element.content.src}
          ></video>
        );
        break;
      case 'api': {
        // Get subtype from content
        const subtype = element.content?.subtype || 'api.text';
        
        // Get content or provide defaults
        const { dataField, placeholderUrl, style } = element.content || {};
        
        // If there's no subtype or dataField defined yet, show the API placeholder
        if (!element.content || (!element.content.subtype && !element.content.dataField)) {
          content = (
            <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
              <Link className="h-10 w-10 mb-2 text-muted-foreground" />
              <div className="text-center text-sm text-muted-foreground">
                <p>API Element</p>
                <p className="text-xs mt-1">Configure API data field</p>
              </div>
            </div>
          );
          break;
        }
        
        // Get field data from API response if available
        let fieldData = null;
        if (dataField && window.__API_DATA__) {
          try {
            console.log(`Canvas dataIndex value:`, window.__API_DATA_INDEX__);
            // Get the data in a simple way using our new smart path resolver
            fieldData = getValueAtPathWithIndex(window.__API_DATA__, dataField, window.__API_DATA_INDEX__);

          } catch (error) {
            console.error('Error accessing API data:', error);
          }
        }
        
        // Render based on subtype
        switch (subtype) {
          case 'api.text': {
            const textStyle = style || {};
            const displayStyle = {
              ...textStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              backgroundColor: textStyle.backgroundColor || 'transparent',
              textDecoration: textStyle.textDecoration || 'none',
              fontWeight: textStyle.fontWeight || 'normal',
              fontStyle: textStyle.fontStyle || 'normal',
              alignItems: textStyle.verticalAlign === 'top' ? 'flex-start' : 
                        textStyle.verticalAlign === 'bottom' ? 'flex-end' : 
                        'center',
              justifyContent: textStyle.textAlign === 'left' ? 'flex-start' : 
                            textStyle.textAlign === 'right' ? 'flex-end' : 
                            'center'
            };
            
            // If we have field data or are in preview mode, display the data/placeholder text
            if (fieldData !== null || isPreviewMode) {
              content = (
                <div style={displayStyle} className="overflow-hidden">
                  {fieldData !== null ? String(fieldData) : (element.content.placeholder || 'API Data')}
                </div>
              );
            } else {
              // If no field data and not in preview mode, show a nicer placeholder with Link icon
              content = (
                <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20" style={displayStyle}>
                  <Link className="h-6 w-6 mb-1 text-muted-foreground" />
                  <div className="text-center text-sm text-muted-foreground">
                    <p>API Text</p>
                  </div>
                </div>
              );
            }
            break;
          }
          
          case 'api.image': {
            let processedPlaceholderUrl = placeholderUrl;
            
            // Process template URL if it contains variables
            if (placeholderUrl && placeholderUrl.includes('{') && placeholderUrl.includes('}')) {
              // Replace all {fieldName} patterns with actual values from API data
              processedPlaceholderUrl = placeholderUrl.replace(/\{([^}]+)\}/g, (match: string, fieldPath: string) => {
                // Get the value for this field path using the global getValueAtPathWithIndex function
                const fieldValue = getValueAtPathWithIndex(window.__API_DATA__, fieldPath.trim(), currentIndex);
                return fieldValue !== null && fieldValue !== undefined ? String(fieldValue) : match;
              });
              
              //console.log(`Processed template URL: ${placeholderUrl} → ${processedPlaceholderUrl}`);
            }
            
            if (fieldData !== null || (processedPlaceholderUrl && isPreviewMode)) {
              // If we have data or a placeholder URL in preview mode, show the image
              let imageSrc = '';
              
              // If the dataField contains a URL, use it directly
              if (fieldData !== null && (String(fieldData).startsWith('http://') || String(fieldData).startsWith('https://'))) {
                imageSrc = String(fieldData);
              }
              // Otherwise, if we have a processed placeholder URL, use that
              else if (processedPlaceholderUrl && processedPlaceholderUrl.includes('://')) {
                imageSrc = processedPlaceholderUrl;
              }
              // As a fallback, use the field data value as is
              else if (fieldData !== null) {
                imageSrc = String(fieldData);
              }
              
              content = (
                <img 
                  src={imageSrc} 
                  alt={element.content.alt || "API Image"} 
                  className="w-full h-full object-fill" 
                />
              );
            } else {
              // If no data and not in preview mode, or no placeholder URL, show a placeholder
              content = (
                <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
                  <Image className="h-8 w-8 mb-1 text-muted-foreground" />
                  <Link className="h-6 w-6 mb-1 text-muted-foreground" />
                  <div className="text-center text-sm text-muted-foreground">
                    <p>API Image</p>
                  </div>
                </div>
              );
            }
            break;
          }
          
          case 'api.video': {
            let processedPlaceholderUrl = placeholderUrl;
            
            // Process template URL if it contains variables
            if (placeholderUrl && placeholderUrl.includes('{') && placeholderUrl.includes('}')) {
              // Replace all {fieldName} patterns with actual values from API data
              processedPlaceholderUrl = placeholderUrl.replace(/\{([^}]+)\}/g, (match: string, fieldPath: string) => {
                // Get the value for this field path using the global getValueAtPathWithIndex function
                const fieldValue = getValueAtPathWithIndex(window.__API_DATA__, fieldPath.trim(), currentIndex);
                return fieldValue !== null && fieldValue !== undefined ? String(fieldValue) : match;
              });
              
              //console.log(`Processed video template URL: ${placeholderUrl} → ${processedPlaceholderUrl}`);
            }
            
            if (fieldData !== null || (processedPlaceholderUrl && isPreviewMode)) {
              // If we have data or a placeholder URL in preview mode, show the video
              let videoSrc = '';
              
              // If the dataField contains a URL, use it directly
              if (fieldData !== null && (String(fieldData).startsWith('http://') || String(fieldData).startsWith('https://'))) {
                videoSrc = String(fieldData);
              }
              // Otherwise, if we have a processed placeholder URL, use that
              else if (processedPlaceholderUrl && processedPlaceholderUrl.includes('://')) {
                videoSrc = processedPlaceholderUrl;
              }
              // As a fallback, use the field data value as is
              else if (fieldData !== null) {
                videoSrc = String(fieldData);
              }
              
              content = (
                <video 
                  className="w-full h-full object-fill" 
                  controls={!isPreviewMode}
                  autoPlay={isPreviewMode}
                  muted={isPreviewMode}
                  playsInline
                  preload="auto"
                  src={videoSrc}
                  onEnded={cycleToNextRecord}
                ></video>
              );
            } else {
              // If no data and not in preview mode, or no placeholder URL, show a placeholder
              content = (
                <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
                  <Video className="h-8 w-8 mb-1 text-muted-foreground" />
                  <Link className="h-6 w-6 mb-1 text-muted-foreground" />
                  <div className="text-center text-sm text-muted-foreground">
                    <p>API Video</p>
                  </div>
                </div>
              );
            }
            break;
          }
          
          case 'api.gauge': {
            // Get gauge settings from element content or use defaults
            const gaugeSettings = element.content || {};
            
            // Convert field data to number for gauge
            let gaugeValue = 0;
            
            if (fieldData !== null) {
              const numericValue = Number(fieldData);
              gaugeValue = isNaN(numericValue) ? 0 : numericValue;
            }
            
            // Collect all gauge properties
            const {
              minValue = 0,
              maxValue = 100,
              segments = 4,
              needleColor = "#4682B4",
              ringWidth = 60,
              textColor = "#666",
              labelFontSize = 14,
              valueTextFontSize = 48,
              valueTextFontWeight = "bold",
              paddingHorizontal = 15,
              paddingVertical = 15,
              segmentColors = ['#FF5F6D', '#FFC371', '#89EC5B', '#00BFFF'],
              segmentStops = [],
              customSegmentLabels: providedSegmentLabels = [],
              fluidWidth = true,
              forceRender = true
            } = gaugeSettings;
            
            // Ensure we have customSegmentLabels for each segment
            // This is critical when data mapping is first selected and no segment labels have been set
            const customSegmentLabels = [];
            
            // Generate default segment labels if none are provided
            for (let i = 0; i < segments; i++) {
              // Calculate segment boundaries for this segment
              const segmentStart = minValue + i * ((maxValue - minValue) / segments);
              const segmentEnd = minValue + (i + 1) * ((maxValue - minValue) / segments);
              
              // Use provided labels if they exist, otherwise create default ones
              if (providedSegmentLabels[i] && providedSegmentLabels[i].text) {
                customSegmentLabels[i] = {...providedSegmentLabels[i]};
              } else {
                // For the first segment when data mapping is selected, we want to show "25"
                // as shown in the reference image for the first segment
                let labelText = "";
                
                // Use segment end value for the label (0-25, 25-50, etc.)
                // Last segment shows the max value
                if (i === segments - 1) {
                  labelText = maxValue.toString();
                } else {
                  labelText = segmentEnd.toString();
                }
                
                // Generate default label for this segment
                customSegmentLabels[i] = {
                  text: labelText,
                  position: 'middle', 
                  color: textColor
                };
              }
            }
            
            // Calculate which segment the value falls into and use the segment's current value text
            let segmentIndex = -1;
            
            // Determine segment boundaries - use custom stops if available, or calculate equal segments
            let segmentBoundaries = [minValue];
            
            if (segmentStops && segmentStops.length === segments - 1) {
              // Use provided custom segment stops
              segmentBoundaries = [minValue, ...segmentStops, maxValue];
            } else {
              // Calculate equally spaced segments
              const segmentStep = (maxValue - minValue) / segments;
              for (let i = 1; i <= segments; i++) {
                segmentBoundaries.push(minValue + i * segmentStep);
              }
            }
            
            // Find which segment the current value falls into using boundaries
            for (let i = 0; i < segmentBoundaries.length - 1; i++) {
              if (gaugeValue >= segmentBoundaries[i] && gaugeValue <= segmentBoundaries[i+1]) {
                segmentIndex = i;
                break;
              }
            }
            
            // If value is at maximum, ensure it falls in the last segment
            if (segmentIndex === -1 && gaugeValue >= maxValue) {
              segmentIndex = segments - 1;
            }
            
            // Get the current value text from the appropriate segment
            let currentValueText = '';
            if (segmentIndex !== -1 && customSegmentLabels[segmentIndex] && customSegmentLabels[segmentIndex].currentValueText) {
              currentValueText = customSegmentLabels[segmentIndex].currentValueText;
            }
            
            // Replace {{value}} placeholders with the actual value
            if (currentValueText) {
              currentValueText = currentValueText.replace(/{{value}}/g, gaugeValue.toString());
            }
            
            // Use valueLabel from gaugeSettings to match what's shown in the properties panel
            // When Data Mapping is selected, this will automatically show the API value
            const valueLabel = gaugeSettings.valueLabel || 'Value';
            
            if (fieldData !== null || isPreviewMode) {
              content = (
                <Gauge 
                  value={gaugeValue}
                  minValue={minValue}
                  maxValue={maxValue}
                  segments={segments}
                  needleColor={needleColor}
                  ringWidth={ringWidth}
                  textColor={textColor}
                  labelFontSize={labelFontSize}
                  valueTextFontSize={valueTextFontSize}
                  valueTextFontWeight={valueTextFontWeight as 'normal' | 'bold' | 'italic'}
                  paddingHorizontal={paddingHorizontal}
                  paddingVertical={paddingVertical}
                  width={element.width}
                  height={element.height}
                  dimensionUnit="px"
                  segmentColors={segmentColors}
                  segmentStops={segmentStops}
                  customSegmentLabels={customSegmentLabels}
                  currentValueText={currentValueText}
                  valueLabel={valueLabel}
                  fluidWidth={fluidWidth}
                  forceRender={forceRender}
                />
              );
            } else {
              // If no data and not in preview mode, show a placeholder
              content = (
                <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
                  <GaugeIcon className="h-8 w-8 mb-1 text-muted-foreground" />
                  <Link className="h-6 w-6 mb-1 text-muted-foreground" />
                  <div className="text-center text-sm text-muted-foreground">
                    <p>API Gauge</p>
                  </div>
                </div>
              );
            }
            break;
          }
          
          default: {
            // Default to text display
            content = (
              <div style={{width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                {fieldData !== null ? String(fieldData) : (element.content.placeholder || 'API Data')}
              </div>
            );
          }
        }
        break;
      }
      case 'playlist':
      case 'multimedia': {
        // Multimedia elements can have two types:
        // 1. Simple media with type, src, etc.
        // 2. Playlist-enabled media with a playlist array and currentIndex
        
        // Check if this element has a playlist
        const playlist = element.content?.playlist || [];
        const hasPlaylist = Array.isArray(playlist) && playlist.length > 0;
        
        if (hasPlaylist) {
          // This is a playlist-based multimedia element
          const currentIndex = element.content?.currentIndex || 0;
          
          if (playlist.length === 0) {
            // Empty playlist - just show the placeholder
            content = (
              <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
                <Image className="h-10 w-10 mb-2 text-muted-foreground" />
                <div className="text-center text-sm text-muted-foreground">
                  <p>Multimedia Playlist</p>
                  <p className="text-xs mt-1">No media items added</p>
                </div>
              </div>
            );
          } else {
            // We have items in the playlist
            const currentItem = playlist[currentIndex % playlist.length];
            
            if (isPreviewMode) {
              // In preview mode, show the actual content
              // Extract fileType and fileUrl from the playlist item structure
              // Handle both legacy and new playlist item structures
              const isImage = currentItem.fileType?.startsWith('image/') || currentItem.type === 'image';
              const isVideo = currentItem.fileType?.startsWith('video/') || currentItem.type === 'video';
              const mediaSrc = currentItem.fileUrl || currentItem.src || '';
              
              if (isImage) {
                content = (
                  <img 
                    src={mediaSrc} 
                    alt={currentItem.name || currentItem.alt || "Playlist Image"} 
                    className="w-full h-full object-fill" 
                  />
                );
              } else if (isVideo) {
                content = (
                  <video 
                    className="w-full h-full object-fill" 
                    controls={!isPreviewMode}
                    autoPlay={isPreviewMode}
                    muted={isPreviewMode}
                    playsInline
                    preload="auto"
                    src={mediaSrc}
                    onEnded={(event) => {
                      //console.log(`Direct onEnded event triggered for playlist element ${element.id}`);
                      // When video ends, update to next item in playlist
                      if (isPreviewMode) {
                        // Only do this in preview mode
                        const nextIndex = (currentIndex + 1) % playlist.length;
                        //console.log(`Advancing playlist element ${element.id} to index ${nextIndex}`);
                        onUpdateElement(element.id, {
                          content: {
                            ...element.content,
                            currentIndex: nextIndex
                          }
                        });
                      }
                    }}
                  ></video>
                );
              } else {
                // Fallback - this shouldn't happen since multimedia only supports images/videos
                content = (
                  <div className="w-full h-full flex items-center justify-center bg-muted/20">
                    <Image className="h-16 w-16 text-muted-foreground" />
                  </div>
                );
              }
            } else {
              // Always show the first item of the playlist in the element
              // even when in edit mode
              const firstItem = playlist[0]; // Get the first item of the playlist
              
              // Extract fileType and fileUrl from the playlist item structure
              // Handle both legacy and new playlist item structures
              const isImage = firstItem.fileType?.startsWith('image/') || firstItem.type === 'image';
              const isVideo = firstItem.fileType?.startsWith('video/') || firstItem.type === 'video';
              const mediaSrc = firstItem.fileUrl || firstItem.src || '';
              
              if (isImage) {
                content = (
                  <img 
                    src={mediaSrc} 
                    alt={firstItem.name || firstItem.alt || "Playlist Image"} 
                    className="w-full h-full object-fill" 
                  />
                );
              } else if (isVideo) {
                content = (
                  <video 
                    className="w-full h-full object-fill" 
                    controls={false}
                    playsInline
                    preload="auto"
                    muted
                    src={mediaSrc}
                  ></video>
                );
              } else {
                // Fallback - this shouldn't happen since multimedia only supports images/videos
                content = (
                  <div className="w-full h-full flex items-center justify-center bg-muted/20">
                    <Image className="h-16 w-16 text-muted-foreground" />
                  </div>
                );
              }
            }
          }
        } else {
          // This is a simple single-media element
          const { type, src, alt, style, text } = element.content || {};
          
          // Render based on the multimedia content type
          if (!type) {
            // If no type is specified, show the default multimedia placeholder
            content = (
              <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
                <Image className="h-10 w-10 mb-2 text-muted-foreground" />
                <div className="text-center text-sm text-muted-foreground">
                  <p>Multimedia Element</p>
                  <p className="text-xs mt-1">Select media to display</p>
                </div>
              </div>
            );
          } else {
            switch (type) {
              case 'image': {
                content = (
                  <img 
                    src={src || ''} 
                    alt={alt || "Image"} 
                    className="w-full h-full object-fill" 
                  />
                );
                break;
              }
              
              case 'video': {
                content = (
                  <video 
                    className="w-full h-full object-fill" 
                    controls={!isPreviewMode}
                    autoPlay={isPreviewMode}
                    muted={isPreviewMode}
                    playsInline
                    preload="auto"
                    src={src || ''}
                  ></video>
                );
                break;
              }
              
              default: {
                // Fallback - shouldn't happen since multimedia only supports images/videos
                content = (
                  <div className="w-full h-full flex items-center justify-center bg-muted/20">
                    <Image className="h-10 w-10 text-muted-foreground" />
                  </div>
                );
                break;
              }
            }
          }
        }
        break;
      }
      case 'shape': {
        const commonStyle = {
          backgroundColor: element.content.fill,
          border: element.content.strokeWidth ? `${element.content.strokeWidth}px solid ${element.content.stroke || '#000000'}` : 'none',
          width: '100%',
          height: '100%'
        };
        
        switch (element.content.shape) {
          case 'rectangle':
            content = <div style={commonStyle}></div>;
            break;
            
          case 'circle':
            content = <div style={{...commonStyle, borderRadius: '50%'}}></div>;
            break;
            
          case 'triangle':
            content = (
              <div style={{position: 'relative', width: '100%', height: '100%'}}>
                <div style={{
                  position: 'absolute',
                  width: '100%',
                  height: '100%',
                  clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                  backgroundColor: element.content.fill,
                  border: element.content.strokeWidth ? `${element.content.strokeWidth}px solid ${element.content.stroke || '#000000'}` : 'none',
                }}></div>
              </div>
            );
            break;
            
          case 'pentagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)',
              }}></div>
            );
            break;
            
          case 'hexagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
              }}></div>
            );
            break;
            
          case 'octagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)',
              }}></div>
            );
            break;
            
          case 'oval':
            content = <div style={{...commonStyle, borderRadius: '50% / 50%'}}></div>;
            break;
            
          case 'semicircle':
            content = (
              <div style={{
                ...commonStyle,
                height: '50%',
                marginTop: '50%',
                borderRadius: '100% 100% 0 0',
              }}></div>
            );
            break;
            
          case 'star':
            content = (
              <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                <svg
                  viewBox="0 0 24 24"
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'block',
                  }}
                >
                  <path
                    d="M12,2L16.2,8.5L23.4,9.1L17.8,14.3L19.1,21.5L12,18.1L4.9,21.5L6.2,14.3L0.6,9.1L7.8,8.5L12,2z"
                    fill={element.content.fill || '#f3f4f6'}
                    stroke={element.content.strokeWidth ? (element.content.stroke || '#000000') : 'none'}
                    strokeWidth={element.content.strokeWidth || 0}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            );
            break;
            
          case 'heart':
            content = (
              <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                <svg
                  viewBox="0 0 24 24"
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'block',
                  }}
                >
                  <path
                    d="M12,21.4L10.6,20.1C5.4,15.4 2,12.3 2,8.5C2,5.4 4.4,3 7.5,3C9.2,3 10.9,3.8 12,5.1C13.1,3.8 14.8,3 16.5,3C19.6,3 22,5.4 22,8.5C22,12.3 18.6,15.4 13.4,20.1L12,21.4Z"
                    fill={element.content.fill || '#f3f4f6'}
                    stroke={element.content.strokeWidth ? (element.content.stroke || '#000000') : 'none'}
                    strokeWidth={element.content.strokeWidth || 0}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            );
            break;
            
          default:
            content = <div style={commonStyle}></div>;
        }
        break;
      }
      case 'date': {
        const { locale = 'en-US', options, timezone } = element.content || {};
        const now = new Date();
        
        // Parse options if they exist as a string, otherwise use default options
        let dateOptions: Intl.DateTimeFormatOptions = {};
        if (options) {
          try {
            // If options is a string (JSON), parse it
            if (typeof options === 'string') {
              dateOptions = JSON.parse(options);
            } else {
              // If options is already an object, use it directly
              dateOptions = options;
            }
          } catch (e) {
            console.error('Error parsing date options:', e);
            // Fallback to default options
            dateOptions = { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            };
          }
        }
        
        // Add timezone if specified
        if (timezone) {
          (dateOptions as Intl.DateTimeFormatOptions).timeZone = timezone;
        }
        
        // Format the date using the specified locale and options
        let formattedDate;
        try {
          const dateFormat = new Intl.DateTimeFormat(locale, dateOptions);
          formattedDate = dateFormat.format(now);
        } catch (e) {
          console.error('Error formatting date:', e);
          // Fallback to basic date string
          formattedDate = now.toLocaleString();
        }
        
        // Format the text for display similar to text elements
        const style = element.content.style || {};
        const displayStyle = {
          ...style,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: style.verticalAlign === 'top' ? 'flex-start' : 
                      style.verticalAlign === 'bottom' ? 'flex-end' : 
                      'center',
          justifyContent: style.textAlign === 'left' ? 'flex-start' : 
                          style.textAlign === 'right' ? 'flex-end' : 
                          'center'
        };
        
        // Render the date text
        content = <div style={displayStyle}>{formattedDate}</div>;
        break;
      }
      /* Second multimedia case removed - consolidated with first instance */
      default:
        content = <div>Unknown element type: {element.type}</div>;
    }

    // Handle animation effects if they exist
    // Check either preview mode is active OR if the element has a _previewKey
    // The _previewKey is added when the "Preview Animation" button is clicked
    const shouldAnimateElement = isPreviewMode || element.animation?._previewKey !== undefined;
    const animationProps = element.animation && shouldAnimateElement
      ? getAnimationProps(element.animation)
      : undefined;
    
    // Set up animation with framer-motion
    const AnimatedContainer = AnimatePresence ? (
      <div key={element.id}>
        <AnimatePresence>
          {animationProps ? (
            <motion.div
              className="absolute"
              style={elementStyle}
              initial={animationProps.initial}
              animate={animationProps.animate}
              transition={animationProps.transition}
              exit={animationProps.exit || {}}
              key={`${element.id}-${element.animation?._previewKey || 0}`}
            >
              {content}
            </motion.div>
          ) : (
            <div
              className={cn("absolute", 
                isSelected && !isPreviewMode ? "outline outline-2 outline-primary" : "",
                isDragging && draggingElement === element.id ? "cursor-grabbing" : "",
                !isPreviewMode ? "hover:outline hover:outline-1 hover:outline-primary-light" : ""
              )}
              style={elementStyle}
              onMouseDown={(e) => {
                if (!isPreviewMode) {
                  handleElementMouseDown(e, element.id);
                }
              }}
            >
              {content}
              
              {/* Show resize handle when element is selected and not in preview mode */}
              {isSelected && !isPreviewMode && (
                <div 
                  className="absolute bottom-0 right-0 cursor-se-resize"
                  onMouseDown={(e) => handleElementResize(e, element.id)}
                >
                  {/* L-shaped resize handle */}
                  <div className="absolute bottom-0 right-0 w-4 h-1 bg-primary" />
                  <div className="absolute bottom-0 right-0 w-1 h-4 bg-primary" />
                </div>
              )}
            </div>
          )}
        </AnimatePresence>
      </div>
    ) : (
      // Fallback if AnimatePresence isn't available
      <div
        className={cn("absolute", 
          isSelected && !isPreviewMode ? "outline outline-2 outline-primary" : "",
          isDragging && draggingElement === element.id ? "cursor-grabbing" : "",
          !isPreviewMode ? "hover:outline hover:outline-1 hover:outline-primary-light" : ""
        )}
        style={elementStyle}
        onMouseDown={(e) => {
          if (!isPreviewMode) {
            handleElementMouseDown(e, element.id);
          }
        }}
      >
        {content}
        
        {/* Show resize handle when element is selected and not in preview mode */}
        {isSelected && !isPreviewMode && (
          <div 
            className="absolute bottom-0 right-0 cursor-se-resize"
            onMouseDown={(e) => handleElementResize(e, element.id)}
          >
            {/* L-shaped resize handle */}
            <div className="absolute bottom-0 right-0 w-4 h-1 bg-primary" />
            <div className="absolute bottom-0 right-0 w-1 h-4 bg-primary" />
          </div>
        )}
      </div>
    );

    return AnimatedContainer;
  };

  // Utility function to get animation properties from configuration
  const getAnimationProps = (animation: CanvasElement["animation"]) => {
    if (!animation) return undefined;
    
    const { type, direction, duration, delay, easing } = animation;
    
    // Default values
    const defaultEasing = easing || "cubic-bezier(0.4, 0, 0.2, 1)";
    
    let initial = {};
    let animate = {};
    let exit = {};
    
    switch (type) {
      case 'fade-in':
        initial = { opacity: 0 };
        animate = { opacity: 1 };
        exit = { opacity: 0 };
        break;
        
      case 'slide-in': {
        // Default direction is from bottom
        const directionOffset = direction === 'left' ? { x: -100, y: 0 } :
                                direction === 'right' ? { x: 100, y: 0 } :
                                direction === 'top' ? { x: 0, y: -100 } :
                                { x: 0, y: 100 }; // bottom or default
                                
        initial = { ...directionOffset, opacity: 0 };
        animate = { x: 0, y: 0, opacity: 1 };
        exit = { ...directionOffset, opacity: 0 };
        break;
      }
      
      case 'zoom-in':
        initial = { scale: 0.5, opacity: 0 };
        animate = { scale: 1, opacity: 1 };
        exit = { scale: 0.5, opacity: 0 };
        break;
        
      case 'bounce':
        initial = { scale: 0.9, opacity: 0 };
        animate = { scale: 1, opacity: 1 };
        exit = { scale: 0.9, opacity: 0 };
        break;
        
      case 'rotate':
        initial = { rotate: -15, opacity: 0 };
        animate = { rotate: 0, opacity: 1 };
        exit = { rotate: 15, opacity: 0 };
        break;
        
      case 'flip-x':
        initial = { rotateX: 90, opacity: 0 };
        animate = { rotateX: 0, opacity: 1 };
        exit = { rotateX: -90, opacity: 0 };
        break;
        
      case 'flip-y':
        initial = { rotateY: 90, opacity: 0 };
        animate = { rotateY: 0, opacity: 1 };
        exit = { rotateY: -90, opacity: 0 };
        break;
        
      default: // 'none' or any other value
        // No animation
        initial = { opacity: 1 };
        animate = { opacity: 1 };
        exit = { opacity: 1 };
    }
    
    // Create final animation configuration
    return {
      initial,
      animate,
      exit,
      transition: {
        duration: duration / 1000, // Convert ms to seconds
        delay: delay / 1000, // Convert ms to seconds
        ease: defaultEasing,
        // For bounce animation, add some bounce physics
        ...(type === 'bounce' ? {
          type: "spring",
          stiffness: 300,
          damping: 10
        } : {})
      }
    };
  };

  const handleCanvasClick = (e: React.MouseEvent) => {
    // Only handle click if not dragging or resizing
    if (!isDragging && draggingElement === null && resizingElement === null) {
      // Only deselect if user clicked directly on the canvas background (not on an element)
      if (e.target === e.currentTarget || e.target === canvasRef.current) {
        onSelectElement(null);
      }
    }
    
    // Stop event from propagating to parent elements
    e.stopPropagation();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Only show zoom controls in edit mode */}
      {!isPreviewMode && (
        <div className="p-2 bg-background border-b flex gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handleZoomOut}
            title="Zoom Out"
          >
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleZoomReset}
            title="Reset Zoom"
          >
            <Maximize className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleZoomIn}
            title="Zoom In"
          >
            <ZoomIn className="h-4 w-4" />
          </Button>
          <div className="ml-2 text-xs font-mono flex items-center">
            {Math.round(zoom * 100)}%
          </div>
        </div>
      )}
      <div className="flex-grow relative overflow-auto">
        <div className="absolute inset-0 flex items-center justify-center">
          {/* Canvas wrapper maintains aspect ratio */}
          <div className="relative" style={{ 
            width: `${width * zoom}px`, 
            height: `${height * zoom}px`,
            maxWidth: '100%',
            maxHeight: '100%'
          }}>
            <div 
              className={cn(
                "relative transform origin-center transition-transform", 
                isPreviewMode ? "" : "shadow-lg" // Only show shadow in edit mode
              )}
              style={{ 
                width: `${width}px`, 
                height: `${height}px`,
                transform: `scale(${zoom})`,
                transformOrigin: 'top left',
                backgroundColor,
              }}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
              onMouseLeave={handleCanvasMouseUp}
              onClick={handleCanvasClick}
              ref={canvasRef}
            >
              <div className="absolute inset-0">
                {elements.map((element) => renderElement(element))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// TypeScript global namespace is defined elsewhere
// Don't declare window properties here to avoid duplicates