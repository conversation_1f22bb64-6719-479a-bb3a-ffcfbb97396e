import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

type Campaign = {
  id: string;
  teamId: string;
  name: string;
  description?: string;
  startDate: Date | null;
  endDate: Date | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
};

type CampaignScreen = {
  campaignId: string;
  screenId: string;
  createdAt: Date;
  campaign?: Campaign;
};

export function useScreenCampaigns(screenId: string, teamId: string) {
  const queryClient = useQueryClient();
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // Check if we have valid parameters
  const isValidParams: boolean = Boolean(screenId && screenId !== '' && teamId && teamId !== '');
  
  // Get campaigns for this screen
  const {
    data: screenCampaigns,
    isLoading: isLoadingScreenCampaigns,
    refetch: refetchScreenCampaigns
  } = useQuery<CampaignScreen[]>({
    queryKey: [`/api/screens/${screenId}/campaigns`],
    queryFn: async () => {
      try {
        // Ensure we have a valid screenId
        if (!isValidParams) {
          console.warn('Invalid screenId provided to useScreenCampaigns');
          return [];
        }
        
        const response = await fetch(`/api/screens/${screenId}/campaigns`);
        if (!response.ok) {
          throw new Error('Failed to fetch screen campaigns');
        }
        return response.json();
      } catch (error) {
        console.error('Error fetching screen campaigns:', error);
        return [];
      }
    },
    // Disable the query if no valid screenId is provided
    enabled: isValidParams,
  });
  
  // Get all campaigns for the team
  const {
    data: allCampaigns,
    isLoading: isLoadingAllCampaigns,
  } = useQuery<Campaign[]>({
    queryKey: [`/api/teams/${teamId}/campaigns`],
    queryFn: async () => {
      try {
        // Ensure we have a valid teamId
        if (!isValidParams) {
          console.warn('Invalid teamId provided to useScreenCampaigns');
          return [];
        }
        
        const response = await fetch(`/api/teams/${teamId}/campaigns`);
        if (!response.ok) {
          throw new Error('Failed to fetch team campaigns');
        }
        return response.json();
      } catch (error) {
        console.error('Error fetching team campaigns:', error);
        return [];
      }
    },
    // Disable the query if no valid teamId is provided
    enabled: isValidParams,
  });
  
  // Get available campaigns (the ones not already assigned to this screen)
  const availableCampaigns = (allCampaigns || []).filter(campaign => {
    // Only include active campaigns
    if (campaign.status !== 'active') return false;
    
    // Filter out campaigns already assigned to this screen
    return !(screenCampaigns || []).some(sc => sc.campaignId === campaign.id);
  });
  
  // Add campaign to screen
  const addCampaignToScreen = useMutation({
    mutationFn: async ({ campaignId }: { campaignId: string }) => {
      return apiRequest('POST', `/api/campaign-screens`, {
        campaignId,
        screenId,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/screens/${screenId}/campaigns`] });
      toast({ 
        title: "Campaign added", 
        description: "The campaign has been added to the screen" 
      });
    },
    onError: (error: any) => {
      toast({ 
        title: "Error adding campaign", 
        description: error.message || "An error occurred while adding the campaign to the screen",
        variant: "destructive"
      });
    },
  });
  
  // Remove campaign from screen
  const removeCampaignFromScreen = useMutation({
    mutationFn: async ({ campaignId }: { campaignId: string }) => {
      return apiRequest('DELETE', `/api/campaign-screens/${campaignId}/${screenId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/screens/${screenId}/campaigns`] });
      toast({ 
        title: "Campaign removed", 
        description: "The campaign has been removed from the screen" 
      });
    },
    onError: (error: any) => {
      toast({ 
        title: "Error removing campaign", 
        description: error.message || "An error occurred while removing the campaign from the screen",
        variant: "destructive"
      });
    },
  });
  
  // Set hasInitialized when data is loaded
  useEffect(() => {
    if (!isLoadingScreenCampaigns && !isLoadingAllCampaigns) {
      setHasInitialized(true);
    }
  }, [isLoadingScreenCampaigns, isLoadingAllCampaigns]);
  
  const isLoading = isLoadingScreenCampaigns || isLoadingAllCampaigns;
  const isEmpty = hasInitialized && (!screenCampaigns || screenCampaigns.length === 0);
  
  return {
    screenCampaigns,
    availableCampaigns,
    isLoading,
    isEmpty,
    hasInitialized,
    addCampaignToScreen,
    removeCampaignFromScreen,
    refetchScreenCampaigns,
  };
}