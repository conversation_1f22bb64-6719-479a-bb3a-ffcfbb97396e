import { createClient } from '@supabase/supabase-js';
import { createBrowserClient, createServerClient } from '@supabase/ssr';

// Find these in your Supabase project settings
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-supabase-url.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

// Only log in development
if (import.meta.env.DEV) {
  console.log('Supabase URL:', supabaseUrl);
}
// Singleton browser client to avoid multiple instances
let browserClient: ReturnType<typeof createBrowserClient> | null = null;

// Create browser client with extended session duration
export const createSupabaseBrowserClient = () => {
  if (browserClient) {
    return browserClient;
  }

  browserClient = createBrowserClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      flowType: 'pkce',
      detectSessionInUrl: true,
      storageKey: 'supabase-auth',
    }
  });

  return browserClient;
};

// Legacy client for direct API access with extended session
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    storageKey: 'supabase-auth',
    // Increase session timeout to 12 hours for development ease
    localStorage: {
      getItem: (key) => {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            const parsed = JSON.parse(data);
            if (parsed?.expires_at) {
              // Extend expiration time to 12 hours from now
              parsed.expires_at = Math.floor(Date.now() / 1000) + 12 * 60 * 60;
              localStorage.setItem(key, JSON.stringify(parsed));
            }
          } catch (e) {
            console.error('Error parsing auth data', e);
          }
        }
        return data;
      },
      setItem: (key, value) => localStorage.setItem(key, value),
      removeItem: (key) => localStorage.removeItem(key),
    }
  }
});

// Make supabase globally available for direct access in components
if (typeof window !== 'undefined') {
  (window as any).supabase = supabase;
}

// Storage bucket helpers
const MEDIA_BUCKET = 'medialibrary';

export const getMediaStoragePath = (teamId: string, fileName: string) =>
  `${teamId}/${fileName}`;

export const getMediaDownloadUrl = (path: string) =>
  `${supabaseUrl}/storage/v1/object/public/${MEDIA_BUCKET}/${path}`;

// Direct password change function that works around session issues
export const changePasswordDirectly = async (email: string, currentPassword: string, newPassword: string) => {
  try {
    // Step 1: Create a fresh client for this operation
    const tempClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Step 2: Sign in with the current credentials to verify current password
    const { error: signInError } = await tempClient.auth.signInWithPassword({
      email,
      password: currentPassword,
    });

    if (signInError) {
      return {
        success: false,
        error: "Current password is incorrect"
      };
    }

    // Step 3: Get the new session
    const { data: sessionData } = await tempClient.auth.getSession();

    if (!sessionData.session) {
      return {
        success: false,
        error: "Failed to authenticate with current password"
      };
    }

    // Step 4: Update the password using the fresh session
    const { error: updateError } = await tempClient.auth.updateUser({
      password: newPassword
    });

    if (updateError) {
      return {
        success: false,
        error: updateError.message
      };
    }

    // Step 5: Sign out of the temporary session
    await tempClient.auth.signOut();

    // Step 6: Sign out of the main client too (will log out the user)
    await supabase.auth.signOut();

    return {
      success: true,
      shouldLogout: true // Indicate that the user should be logged out
    };
  } catch (error) {
    console.error("Password change error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred"
    };
  }
};
