import React from 'react';
import { useLocation } from 'wouter';

interface SafeLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * SafeLink component that properly handles URL navigation without causing invalid href errors
 */
const SafeLink: React.FC<SafeLinkProps> = ({ href, children, className, onClick }) => {
  const [_, navigate] = useLocation();
  
  // <PERSON>le clicks to avoid the Replit wrapper issues
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    
    // Remove leading slashes to avoid double-slash URLs
    const cleanPath = href.replace(/^\/+/, '');
    
    // If there's a custom click handler, call it
    if (onClick) {
      onClick();
    } else {
      // Otherwise navigate to the cleaned path
      navigate(cleanPath);
    }
  };
  
  return (
    <a 
      href="#" 
      className={className}
      onClick={handleClick}
    >
      {children}
    </a>
  );
};

export default SafeLink;