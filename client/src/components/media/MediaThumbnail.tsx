import React from 'react';
import { 
  FileImage, 
  FileVideo, 
  FileAudio, 
  FileText, 
  FileType,
  File as FileIcon,
  Presentation
} from 'lucide-react';

export interface MediaThumbnailProps {
  fileUrl: string;
  fileType: string;
  thumbnailUrl?: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  onClick?: () => void;
}

export function MediaThumbnail({
  fileUrl, 
  fileType, 
  thumbnailUrl, 
  alt = 'Media thumbnail',
  className = '', 
  width = 200,
  height = 150,
  onClick
}: MediaThumbnailProps) {
  const hasValidThumbnail = thumbnailUrl && !thumbnailUrl.includes('undefined');
  const isImage = fileType.startsWith('image/');
  const isVideo = fileType.startsWith('video/');
  const isAudio = fileType.startsWith('audio/');
  const isPdf = fileType === 'application/pdf';
  const isPresentation = fileType.includes('presentation') || 
    fileType.includes('powerpoint') || 
    fileUrl.endsWith('.ppt') || 
    fileUrl.endsWith('.pptx');
  const isDocument = fileType.includes('document') || 
    fileType.includes('word') || 
    fileUrl.endsWith('.doc') || 
    fileUrl.endsWith('.docx');

  // Default wrapper classes
  const wrapperClasses = `
    bg-muted/40 rounded-md overflow-hidden flex items-center justify-center relative
    ${className}
    ${onClick ? 'cursor-pointer hover:opacity-90 transition-opacity' : ''}
  `;
  
  // Icon wrapper classes
  const iconWrapperClasses = `
    w-full h-full flex items-center justify-center
  `;
  
  // Determine which component to render based on file type
  if (isImage && (hasValidThumbnail || fileType === 'image/svg+xml')) {
    // For images with thumbnails or SVGs
    return (
      <div 
        className={wrapperClasses}
        style={{ width, height }}
        onClick={onClick}
      >
        <img 
          src={thumbnailUrl || fileUrl} 
          alt={alt}
          className="w-full h-full object-contain"
        />
      </div>
    );
  }
  
  if (isVideo && hasValidThumbnail) {
    // For videos with thumbnails
    return (
      <div 
        className={wrapperClasses}
        style={{ width, height }}
        onClick={onClick}
      >
        <img 
          src={thumbnailUrl} 
          alt={alt} 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-background/70 rounded-full p-2">
            <FileVideo className="h-8 w-8 text-primary" />
          </div>
        </div>
      </div>
    );
  }
  
  // For files without thumbnails, display appropriate icons
  return (
    <div 
      className={wrapperClasses}
      style={{ width, height }}
      onClick={onClick}
    >
      <div className={iconWrapperClasses}>
        {isImage && (
          <FileImage className="h-12 w-12 text-primary" />
        )}
        
        {isVideo && (
          <FileVideo className="h-12 w-12 text-primary" />
        )}
        
        {isAudio && (
          <FileAudio className="h-12 w-12 text-primary" />
        )}
        
        {isPdf && (
          <FileType className="h-12 w-12 text-primary" />
        )}
        
        {isPresentation && (
          <Presentation className="h-12 w-12 text-primary" />
        )}
        
        {isDocument && (
          <FileText className="h-12 w-12 text-primary" />
        )}
        
        {!isImage && !isVideo && !isAudio && !isPdf && !isPresentation && !isDocument && (
          <FileIcon className="h-12 w-12 text-primary" />
        )}
      </div>
    </div>
  );
}