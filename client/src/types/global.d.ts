// Global type definitions for the application

declare global {
  interface Window {
    // Global API data
    __API_DATA__: Record<string, any> | undefined;
    
    // Global API data fields for dropdown selections
    __API_DATA_FIELDS__: string[];
    
    // Current API data index (for cycling through data)
    __API_DATA_INDEX__: number;
    
    // Mapping of display paths to real paths with indices
    __API_PATH_MAPPINGS__?: Record<string, string>;
    
    // Helper function to get real data path from display path
    __GET_REAL_PATH__?: (displayPath: string) => string;
  }
}

export {};