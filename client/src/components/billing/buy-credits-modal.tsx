import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useBilling } from "@/hooks/use-billing";
import type { Pricing } from "@shared/schema";

const buyCreditsSchema = z.object({
  billingCycle: z.string().min(1, "Please select a billing cycle"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
});

type BuyCreditsFormValues = z.infer<typeof buyCreditsSchema>;

interface BuyCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
  teamName: string;
}

export function BuyCreditsModal({ isOpen, onClose, teamId, teamName }: BuyCreditsModalProps) {
  const { pricing, createInvoice, isCreatingInvoice } = useBilling(teamId);
  const [selectedPricing, setSelectedPricing] = useState<Pricing | null>(null);
  const [totalAmount, setTotalAmount] = useState(0);

  const form = useForm<BuyCreditsFormValues>({
    resolver: zodResolver(buyCreditsSchema),
    defaultValues: {
      billingCycle: "",
      quantity: 1,
    },
  });

  const watchedValues = form.watch();

  // Update selected pricing and total amount when form values change
  useEffect(() => {
    const selectedBillingCycle = watchedValues.billingCycle;
    const quantity = watchedValues.quantity || 1;

    if (selectedBillingCycle && pricing.length > 0) {
      const pricingOption = pricing.find(p => p.billingCycleValue === selectedBillingCycle);
      if (pricingOption) {
        setSelectedPricing(pricingOption);
        // Price is already in dollars, so just multiply by quantity
        const totalInDollars = (pricingOption.price || 0) * quantity;
        setTotalAmount(totalInDollars);
      }
    } else {
      setSelectedPricing(null);
      setTotalAmount(0);
    }
  }, [watchedValues.billingCycle, watchedValues.quantity, pricing]);

  const onSubmit = async (data: BuyCreditsFormValues) => {
    if (!selectedPricing) return;

    // Generate random 10 character alphanumeric external subscription ID
    const externalSubscriptionId = Math.random().toString(36).substring(2, 12).toUpperCase();

    createInvoice({
      teamId,
      billingCycle: data.billingCycle,
      qty: data.quantity,
      totalAmount: totalAmount,
      externalSubscriptionId,
    });

    // Reset form and close modal
    form.reset();
    onClose();
  };

  const handleClose = () => {
    form.reset();
    setSelectedPricing(null);
    setTotalAmount(0);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Buy Subscription Credits</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="billingCycle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Billing Cycle</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select billing cycle" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {pricing.map((option) => (
                        <SelectItem key={option.id} value={option.billingCycleValue || ""}>
                          {option.billingCycleText} - ${(option.price || 0).toFixed(2)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {selectedPricing && (
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total Amount:</span>
                  <span className="text-lg font-bold">
                    ${totalAmount.toFixed(2)}
                  </span>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {watchedValues.quantity} × ${(selectedPricing.price || 0).toFixed(2)} ({selectedPricing.billingCycleText})
                </div>
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreatingInvoice || !selectedPricing}
              >
                {isCreatingInvoice ? "Processing..." : "Proceed to Payment"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
