import React from 'react';
import { useLocation } from 'wouter';

interface NextSafeLinkProps {
  to: string;
  href?: string; // Added to support existing sidebar component
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  isActive?: boolean;
}

/**
 * A component to handle proper navigation in the Replit environment
 * by avoiding the next/router invalid href errors with double slashes
 */
const NextSafeLink: React.FC<NextSafeLinkProps> = ({ 
  to, 
  href, // Support original href attribute
  children, 
  className, 
  onClick,
  isActive 
}) => {
  const [_, navigate] = useLocation();
  
  // Use to prop, or fallback to href prop if to is not provided
  const destination = to || href || '';
  
  // Clean the path to avoid double slashes
  const cleanTo = destination.replace(/^\/+/, '');
  
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    
    // Call onClick callback if provided
    if (onClick) {
      onClick();
    }
    
    // Navigate to the cleaned path
    navigate(cleanTo);
  };
  
  return (
    <a 
      href={`/${cleanTo}`} 
      className={className} 
      onClick={handleClick}
    >
      {children}
    </a>
  );
};

export default NextSafeLink;