import { useState, useEffect } from 'react';
import { HexColorPicker, HexColorInput, RgbaColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

// Convert rgba to css rgba string
const rgbaToString = (rgba: { r: number; g: number; b: number; a: number }) => {
  return `rgba(${rgba.r}, ${rgba.g}, ${rgba.b}, ${rgba.a})`;
};

// Convert hex color to rgba object
const hexToRgba = (hex: string, alpha = 1) => {
  hex = hex.replace('#', '');
  
  // Convert 3-digit hex to 6-digit
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('');
  }
  
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return { r, g, b, a: alpha };
};

// Convert rgba object to hex
const rgbaToHex = (rgba: { r: number; g: number; b: number; a: number }) => {
  const toHex = (num: number) => {
    const hex = Math.round(num).toString(16);
    return hex.length === 1 ? `0${hex}` : hex;
  };
  
  return `#${toHex(rgba.r)}${toHex(rgba.g)}${toHex(rgba.b)}`;
};

// Parse CSS color string to rgba
const parseColor = (color: string) => {
  if (color.startsWith('#')) {
    return hexToRgba(color);
  } else if (color.startsWith('rgba')) {
    const matches = color.match(/rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/);
    if (matches) {
      return {
        r: parseInt(matches[1]),
        g: parseInt(matches[2]),
        b: parseInt(matches[3]),
        a: parseFloat(matches[4])
      };
    }
  } else if (color.startsWith('rgb')) {
    const matches = color.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);
    if (matches) {
      return {
        r: parseInt(matches[1]),
        g: parseInt(matches[2]),
        b: parseInt(matches[3]),
        a: 1
      };
    }
  }
  
  // Default white
  return { r: 255, g: 255, b: 255, a: 1 };
};

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

export function ColorPicker({ value, onChange, label }: ColorPickerProps) {
  const [open, setOpen] = useState(false);
  const [color, setColor] = useState<string>(value || '#ffffff');
  const [rgbaColor, setRgbaColor] = useState(parseColor(value || '#ffffff'));
  // Always default to rgba tab to make transparency options more accessible
  const [activeTab, setActiveTab] = useState<string>('rgba');
  
  useEffect(() => {
    // Update internal state when external value changes
    setColor(value || '#ffffff');
    setRgbaColor(parseColor(value || '#ffffff'));
  }, [value]);
  
  const handleHexChange = (newColor: string) => {
    setColor(newColor);
    setRgbaColor(hexToRgba(newColor, rgbaColor.a));
    
    if (activeTab === 'hex') {
      onChange(newColor);
    } else {
      onChange(rgbaToString({ ...hexToRgba(newColor), a: rgbaColor.a }));
    }
  };
  
  const handleRgbaChange = (newColor: { r: number; g: number; b: number; a: number }) => {
    setRgbaColor(newColor);
    setColor(rgbaToHex(newColor));
    onChange(rgbaToString(newColor));
  };
  
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (value === 'hex') {
      onChange(color);
    } else {
      onChange(rgbaToString(rgbaColor));
    }
  };
  
  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <div className="flex items-center gap-2">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-10 h-10 p-0 relative overflow-hidden"
              style={{
                border: '1px solid #ccc'
              }}
            >
              {/* Checkerboard background to show transparency */}
              <div 
                className="absolute inset-0 bg-[#e0e0e0]" 
                style={{
                  backgroundImage: 'linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%), linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%)',
                  backgroundSize: '10px 10px',
                  backgroundPosition: '0 0, 5px 5px'
                }}
              />
              {/* Color overlay with transparency */}
              <div 
                className="absolute inset-0"
                style={{
                  backgroundColor: activeTab === 'hex' ? color : rgbaToString(rgbaColor),
                }}
              />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-4" align="start">
            <Tabs defaultValue="hex" value={activeTab} onValueChange={handleTabChange}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="hex">Hex</TabsTrigger>
                <TabsTrigger value="rgba">RGBA</TabsTrigger>
              </TabsList>
              <TabsContent value="hex" className="space-y-4">
                <HexColorPicker 
                  color={color} 
                  onChange={handleHexChange} 
                  className="mt-4"
                />
                <div className="flex items-center gap-2 mt-4">
                  <span className="text-sm">#</span>
                  <HexColorInput
                    color={color}
                    onChange={handleHexChange}
                    prefixed={false}
                    className="flex-1 h-9 px-3 py-2 text-sm border rounded-md"
                  />
                </div>
                
                {/* Add transparency note */}
                <div className="pt-2 text-xs text-muted-foreground">
                  <span>Switch to RGBA tab to adjust transparency</span>
                </div>
              </TabsContent>
              <TabsContent value="rgba" className="space-y-4">
                <RgbaColorPicker
                  color={rgbaColor}
                  onChange={handleRgbaChange}
                  className="mt-4"
                />
                <div className="space-y-4 mt-4">
                  <div>
                    <div className="flex items-center justify-between">
                      <Label className="text-xs">Alpha/Transparency</Label>
                      <span className="text-xs font-medium">{Math.round(rgbaColor.a * 100)}%</span>
                    </div>
                    <div className="relative mt-1">
                      {/* Checkerboard background to show transparency effect */}
                      <div 
                        className="absolute inset-0 rounded-md h-9 pointer-events-none bg-[#e0e0e0]" 
                        style={{
                          backgroundImage: 'linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%), linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%)',
                          backgroundSize: '10px 10px',
                          backgroundPosition: '0 0, 5px 5px'
                        }}
                      />
                      {/* Color gradient overlay to show transparency effect */}
                      <div 
                        className="absolute inset-0 rounded-md h-9 pointer-events-none" 
                        style={{
                          backgroundImage: `linear-gradient(to right, rgba(${rgbaColor.r}, ${rgbaColor.g}, ${rgbaColor.b}, 0), rgba(${rgbaColor.r}, ${rgbaColor.g}, ${rgbaColor.b}, 1))`
                        }}
                      />
                      <Input 
                        type="range" 
                        min="0" 
                        max="1" 
                        step="0.01"
                        value={rgbaColor.a}
                        onChange={(e) => handleRgbaChange({ ...rgbaColor, a: parseFloat(e.target.value) })}
                        className="h-9 relative z-10 opacity-75 hover:opacity-100 transition-opacity"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <Label className="text-xs">Red</Label>
                      <Input 
                        type="number" 
                        min="0" 
                        max="255" 
                        value={rgbaColor.r}
                        onChange={(e) => handleRgbaChange({ ...rgbaColor, r: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Green</Label>
                      <Input 
                        type="number" 
                        min="0" 
                        max="255" 
                        value={rgbaColor.g}
                        onChange={(e) => handleRgbaChange({ ...rgbaColor, g: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                    <div>
                      <Label className="text-xs">Blue</Label>
                      <Input 
                        type="number" 
                        min="0" 
                        max="255" 
                        value={rgbaColor.b}
                        onChange={(e) => handleRgbaChange({ ...rgbaColor, b: parseInt(e.target.value) || 0 })}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </PopoverContent>
        </Popover>
        <div className="flex-1 relative">
          {/* Only show checkerboard for rgba mode */}
          {activeTab === 'rgba' && (
            <div 
              className="absolute inset-0 rounded-md pointer-events-none bg-[#e0e0e0]" 
              style={{
                backgroundImage: 'linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%), linear-gradient(45deg, #a0a0a0 25%, transparent 25%, transparent 75%, #a0a0a0 75%)',
                backgroundSize: '10px 10px',
                backgroundPosition: '0 0, 5px 5px',
                zIndex: -1
              }}
            />
          )}
          <Input
            type="text"
            value={activeTab === 'hex' ? color : rgbaToString(rgbaColor)}
            onChange={(e) => {
              const newValue = e.target.value;
              if (activeTab === 'hex') {
                setColor(newValue);
                if (/^#[0-9A-Fa-f]{6}$/.test(newValue)) {
                  handleHexChange(newValue);
                }
              } else {
                if (newValue.startsWith('rgba')) {
                  try {
                    const parsed = parseColor(newValue);
                    setRgbaColor(parsed);
                    handleRgbaChange(parsed);
                  } catch (e) {
                    // Invalid format, ignore
                  }
                }
              }
            }}
            className="flex-1 relative bg-transparent"
            placeholder={activeTab === 'hex' ? "#ffffff" : "rgba(255, 255, 255, 1)"}
            style={{
              backgroundColor: activeTab === 'rgba' ? rgbaToString(rgbaColor) : undefined
            }}
          />
        </div>
      </div>
    </div>
  );
}