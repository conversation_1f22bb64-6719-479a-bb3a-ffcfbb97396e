Yes slide designer is now loading. One issue i noticed with the canvas size is that even though size is 1920 X 1080, the actual scaled size was 465 x 484 which doesn't look 16:9 resolution so the scaled canvas should maintain aspect ratio based on the actual slide size. Please proceed to implement complete features of the slides functionality such as;

- Ability to change slide size, background, API url, api url preview duration  properties
- Ability to add elements and media files on the canvas
- Add more shape types such as triangle, pentagon, hexagon, octagon, oval, semicircle, star with rounded corner, heart etc
- Add one more element type namely "Multimedia" to display an image or video using external hyper link. More details explained below in the API URL requirements below.
- The concept about having API URL and Preview duration in the Slide Properties is as follows;
  - Ability to fetch external data source such as weather feed, fire index data, rss feed or product catalogue, menu information etc
  - Read the external data source structure and map data field with element such as Text or Multimedia. For the Multimedia element, user can define a Placeholder URL for example, in the weather feed data, just weather icon name is provided and it has to be retrieved using valid external url so in the 'Placeholder URL', the user can enter url such as 'htttps://externalurl.com/assets/{API_Data_Field_Name}.png. So when the user preview, the icon name data can be fetched from 'API Data_Field_Name' column value and replaced in the placeholder url.
- Ability to set element properties such as;
  - position
  - size
  - background color (including alpha)
  - font color
  - font family
  - font size
  - font style
  - font weight
  - Text Decoration
  - horizontal alignment
  - verticle alignment
  - Text content
  - For date time, ability to show Date, Time or Date & Time both. 
  - If API URL data structure is available then ability to map API data field with the Text or Multimedia element
  - As soon as the above element properties are modified, the element should be updated
- Any other features you may think would be useful to dynamically display on the digital signage display