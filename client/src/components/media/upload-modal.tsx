import { useState, useRef, ChangeEvent } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { supabase, getMediaStoragePath } from "@/lib/supabase";
import { useToast } from "@/hooks/use-toast";

// Helper function to handle Response objects and JSON parsing
async function handleApiResponse<T>(response: any): Promise<T> {
  try {
    if (response instanceof Response) {
      console.log("Response is a Response object, parsing JSON...");
      if (response.headers.get('content-type')?.includes('application/json')) {
        const jsonResult = await response.json();
        console.log("Parsed response:", jsonResult);
        return jsonResult as T;
      } else {
        console.log("Response has no JSON content, status:", response.status);
        return { success: response.ok, status: response.status } as unknown as T;
      }
    }

    return response as T;
  } catch (error) {
    console.error("Error handling API response:", error);
    throw new Error('Error processing API response');
  }
}

// Helper function to generate random colors for tags
const generateRandomColor = (): string => {
  const colors = [
    "#ef4444", // red
    "#f97316", // orange
    "#f59e0b", // amber
    "#eab308", // yellow
    "#84cc16", // lime
    "#22c55e", // green
    "#10b981", // emerald
    "#14b8a6", // teal
    "#06b6d4", // cyan
    "#0ea5e9", // sky
    "#3b82f6", // blue
    "#6366f1", // indigo
    "#8b5cf6", // violet
    "#a855f7", // purple
    "#d946ef", // fuchsia
    "#ec4899", // pink
    "#f43f5e", // rose
  ];
  return colors[Math.floor(Math.random() * colors.length)];
};

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { TagInput } from "@/components/media/tag-input";
import { UploadCloud, X, File } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { formatBytes } from "@/lib/utils";

interface UploadModalProps {
  open: boolean;
  onClose: () => void;
  teamId: string;
  onSuccess: () => void;
}

// Define our preview type which can be a string URL or an object with dataUrl
type PreviewType = string | {dataUrl: string, blob?: Blob, fileName?: string};

// Define our file with preview interface
interface FileWithPreview {
  id: string;
  name: string;
  size: number;
  type: string;
  lastModified: number;
  preview: PreviewType;
  uploadProgress: number;
  // Store the original file for upload
  file?: File;
}

// Helper function to extract the dataUrl from a preview
function getPreviewUrl(preview: PreviewType | undefined): string {
  if (!preview) return '';
  if (typeof preview === 'string') return preview;
  return preview.dataUrl;
}

// Helper function for uploading files with progress tracking
const uploadFileWithProgress = async (
  file: File,
  storagePath: string,
  onProgress: (progress: number) => void
): Promise<{ data: any; error: any }> => {
  return new Promise((resolve, reject) => {
    // Initialize progress to a small value to show activity started
    onProgress(1);

    // Create XHR request to upload the file
    const xhr = new XMLHttpRequest();

    // Set up the FormData object
    const formData = new FormData();
    formData.append('file', file);

    // Create a custom URL for the Supabase storage API
    // Using the public upload method via createUploadSignedUrl is not feasible for progress tracking
    // So we're simulating progress with timed intervals

    // Start a "fake" progress update that moves progressively faster
    // This simulates the upload process while the actual upload happens in the background
    let simulatedProgress = 1;
    const progressInterval = setInterval(() => {
      if (simulatedProgress < 90) {
        // Accelerate the progress as it increases
        const increment = simulatedProgress < 30 ? 2 :
                        simulatedProgress < 60 ? 3 : 4;

        simulatedProgress += increment;
        onProgress(simulatedProgress);
      }
    }, 200);

    // Perform the actual upload using Supabase
    const uploadFile = async () => {
      try {
        // Upload the file to Supabase storage
        const { data, error } = await supabase.storage
          .from('medialibrary')
          .upload(storagePath, file, {
            upsert: true,
          });

        clearInterval(progressInterval);

        if (error) {
          onProgress(0); // Reset progress on error
          reject({ data: null, error });
        } else {
          // Set to 100% on success
          onProgress(100);
          resolve({ data, error: null });
        }
      } catch (error) {
        clearInterval(progressInterval);
        onProgress(0); // Reset progress on error
        reject({ data: null, error });
      }
    };

    // Start the upload
    uploadFile();
  });
};

export function UploadModal({ open, onClose, teamId, onSuccess }: UploadModalProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Helper function to generate a thumbnail from a video file
  const generateVideoThumbnail = async (file: File, videoObjectUrl: string): Promise<PreviewType> => {
    return new Promise((resolve) => {
      try {
        console.log("Starting video thumbnail generation...");

        // Step 1: Create a fallback thumbnail first (black with play button)
        const createFallbackThumbnail = () => {
          const canvas = document.createElement('canvas');
          canvas.width = 320;
          canvas.height = 180;
          const ctx = canvas.getContext('2d');

          if (ctx) {
            // Fill with black background
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add play button icon
            ctx.fillStyle = '#ffffff';
            ctx.font = '48px sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('▶', canvas.width / 2, canvas.height / 2);

            // Create data URL directly
            const fallbackDataUrl = canvas.toDataURL('image/jpeg', 0.9);
            console.log("Created fallback thumbnail data URL");
            resolve(fallbackDataUrl);
          } else {
            console.error("Could not get canvas context for fallback");
            resolve(videoObjectUrl);
          }
        };

        // Step 2: Try to generate a better thumbnail from the video
        // Create video element to get a frame
        const video = document.createElement('video');

        // Set attributes for cross-browser compatibility
        video.crossOrigin = 'anonymous';
        video.muted = true;
        video.playsInline = true;
        video.preload = 'metadata';

        // Set source to the video file
        video.src = videoObjectUrl;

        // Function to capture frame from video
        const captureVideoFrame = () => {
          try {
            console.log("Attempting to capture video frame...");

            // Create a canvas to draw the video frame
            const canvas = document.createElement('canvas');

            // Set dimensions from video or fallback to defaults
            let width = video.videoWidth || 320;
            let height = video.videoHeight || 180;

            // Ensure minimum width of 180px
            if (width < 180) {
              const ratio = 180 / width;
              width = 180;
              height = Math.round(height * ratio);
            }

            canvas.width = width;
            canvas.height = height;

            // Get the canvas context
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              console.error("Failed to get canvas context");
              createFallbackThumbnail();
              return;
            }

            // First clear the canvas with a black background
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, width, height);

            // Then draw the video frame
            ctx.drawImage(video, 0, 0, width, height);

            // Check if the canvas is not empty (all black pixels)
            const imageData = ctx.getImageData(0, 0, width, height);
            const data = imageData.data;
            let hasContent = false;

            // Sample some pixels to check if there's actual content
            for (let i = 0; i < data.length; i += 40) { // Sample every 10th pixel
              // If any RGB value is non-zero, we have content
              if (data[i] > 5 || data[i + 1] > 5 || data[i + 2] > 5) {
                hasContent = true;
                break;
              }
            }

            if (!hasContent) {
              console.warn("Canvas appears to be empty or black, using fallback");
              // Draw a play icon to indicate it's a video
              ctx.fillStyle = '#ffffff';
              ctx.font = '48px sans-serif';
              ctx.textAlign = 'center';
              ctx.textBaseline = 'middle';
              ctx.fillText('▶', width / 2, height / 2);
            }

            // Convert to data URL with high quality
            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.85);
            console.log("Generated video frame thumbnail");

            // Clean up video element
            video.pause();
            video.removeAttribute('src');
            video.load();

            // Just return the data URL - no need for blob storage anymore
            resolve(thumbnailDataUrl);
          } catch (error) {
            console.error("Error capturing video frame:", error);
            createFallbackThumbnail();
          }
        };

        // Event handlers for video element

        // When metadata is loaded, seek to 1 second mark
        video.onloadedmetadata = () => {
          console.log("Video metadata loaded", video.videoWidth, "x", video.videoHeight);
          try {
            // Try to seek to 1 second into the video for better thumbnail
            video.currentTime = 1.0;
          } catch (err) {
            console.warn("Error seeking video:", err);
            // Try to capture current frame anyway
            setTimeout(captureVideoFrame, 100);
          }
        };

        // When seeking is complete, capture the frame
        video.onseeked = () => {
          console.log("Video seeked to position:", video.currentTime);
          captureVideoFrame();
        };

        // Fallback for seeking errors
        video.onerror = (error) => {
          console.error("Video element error:", error);
          createFallbackThumbnail();
        };

        // Set a timeout in case nothing happens
        const timeoutId = setTimeout(() => {
          console.warn("Thumbnail generation timed out");
          createFallbackThumbnail();
        }, 3000);

        // Cancel timeout if video can play
        video.oncanplay = () => {
          clearTimeout(timeoutId);
        };

        // Start loading the video
        video.load();

      } catch (error) {
        console.error("Error in generateVideoThumbnail:", error);
        resolve(videoObjectUrl);
      }
    });
  };

  const handleFileInputChange = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const filesArray = Array.from(e.target.files);
      const newFilesPromises = filesArray.map(async file => {
        // Log file details for debugging
        console.log("Processing file:", file.name, file.type, file.size);

        // Create initial preview
        let preview: PreviewType = URL.createObjectURL(file);

        // Generate thumbnail for videos
        if (file.type.startsWith('video/')) {
          try {
            console.log("Generating video thumbnail...");
            // Always create a fallback immediately
            // (This is handled inside generateVideoThumbnail now)

            // Try to generate a better thumbnail
            preview = await generateVideoThumbnail(file, URL.createObjectURL(file));
          } catch (error) {
            console.error("Error generating video preview:", error);
            // The preview will remain as object URL fallback
          }
        }

        return {
          id: crypto.randomUUID(),
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          preview,
          uploadProgress: 0,
          file
        };
      });

      const newFiles = await Promise.all(newFilesPromises);
      setFiles(prevFiles => [...prevFiles, ...newFiles]);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const filesArray = Array.from(e.dataTransfer.files);
      const newFilesPromises = filesArray.map(async file => {
        // Log file details for debugging
        console.log("Processing dropped file:", file.name, file.type, file.size);

        // Create initial preview
        let preview: PreviewType = URL.createObjectURL(file);

        // Generate thumbnail for videos
        if (file.type.startsWith('video/')) {
          try {
            console.log("Generating video thumbnail for dropped file...");
            // Try to generate a thumbnail
            preview = await generateVideoThumbnail(file, URL.createObjectURL(file));
          } catch (error) {
            console.error("Error generating video preview:", error);
            // The preview will remain as object URL fallback
          }
        }

        return {
          id: crypto.randomUUID(),
          name: file.name,
          size: file.size,
          type: file.type,
          lastModified: file.lastModified,
          preview,
          uploadProgress: 0,
          file
        };
      });

      const newFiles = await Promise.all(newFilesPromises);
      setFiles(prevFiles => [...prevFiles, ...newFiles]);
    }
  };

  const removeFile = (id: string) => {
    setFiles(prevFiles => {
      const fileToRemove = prevFiles.find(file => file.id === id);
      if (fileToRemove?.preview && typeof fileToRemove.preview === 'string') {
        // Only revoke if it's a URL object (not a data URL which starts with data:)
        if (!fileToRemove.preview.startsWith('data:')) {
          URL.revokeObjectURL(fileToRemove.preview);
        }
      }
      return prevFiles.filter(file => file.id !== id);
    });
  };

  const uploadFiles = async () => {
    if (files.length === 0) {
      toast({
        title: "No files selected",
        description: "Please select at least one file to upload.",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      const uploadPromises = files.map(async (file) => {
        const storagePath = getMediaStoragePath(teamId, file.name || `unnamed-file-${file.id}`);

        // Upload file to Supabase storage
        if (!file.file) {
          throw new Error(`Missing file data for ${file.name || 'unnamed file'}`);
        }

        // Log the file object to verify it has proper data before upload
        console.log('File object to upload:', {
          id: file.id,
          name: file.file.name,
          size: file.file.size,
          type: file.file.type,
        });

        // Custom upload with progress tracking using XMLHttpRequest
        const { data: storageData, error: storageError } = await uploadFileWithProgress(
          file.file,
          storagePath,
          (progress) => {
            // Update file progress
            setFiles(prevFiles =>
              prevFiles.map(f =>
                f.id === file.id ? { ...f, uploadProgress: progress } : f
              )
            );
          }
        );

        if (storageError) {
          throw new Error(`Error uploading file: ${storageError.message}`);
        }

        // Generate thumbnail for image/video
        let thumbnailUrl = undefined;
        let width = undefined;
        let height = undefined;
        let duration = undefined;

        // Use the file type from the original file object
        const fileTypeForCheck = file.file?.type || file.type || '';  // Ensure type is not undefined

        if (fileTypeForCheck.startsWith('image/')) {
          const img = new Image();
          img.src = getPreviewUrl(file.preview);
          await new Promise<void>((resolve) => {
            img.onload = () => {
              width = img.width;
              height = img.height;
              resolve();
            };
            // Add error handler in case image loading fails
            img.onerror = () => {
              console.error('Error loading image for metadata extraction');
              resolve();
            };
          });

          // Generate a proper thumbnail for images too as a data URL
          try {
            // Create a canvas to create a resized thumbnail
            const canvas = document.createElement('canvas');
            // Set a standard thumbnail size
            const maxDimension = 320;
            let thumbWidth = width || 640;
            let thumbHeight = height || 480;

            // Ensure minimum width of 180px
            if (thumbWidth < 180) {
              const ratio = 180 / thumbWidth;
              thumbWidth = 180;
              thumbHeight = Math.round(thumbHeight * ratio);
            }

            // Calculate aspect ratio
            if (thumbWidth > thumbHeight) {
              if (thumbWidth > maxDimension) {
                thumbHeight = Math.round(thumbHeight * (maxDimension / thumbWidth));
                thumbWidth = maxDimension;
              }
            } else {
              if (thumbHeight > maxDimension) {
                thumbWidth = Math.round(thumbWidth * (maxDimension / thumbHeight));
                thumbHeight = maxDimension;
              }
            }

            canvas.width = thumbWidth;
            canvas.height = thumbHeight;

            // Draw the image to canvas with resize
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              console.error('Could not get canvas context');
              // Fallback to using the original image
              thumbnailUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;
              return;
            }

            // Draw image onto canvas
            ctx.drawImage(img, 0, 0, thumbWidth, thumbHeight);

            // Convert canvas directly to data URL with good quality
            thumbnailUrl = canvas.toDataURL('image/jpeg', 0.85);
            console.log('Generated image thumbnail data URL');
          } catch (error) {
            console.error('Error generating image thumbnail:', error);
            // Fallback to using the original image
            thumbnailUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;
          }
        }
        else if (fileTypeForCheck.startsWith('video/')) {
          console.log('Processing video file thumbnail...');

          // Set video dimensions and duration from file metadata if possible
          try {
            const videoFile = file.file;
            if (videoFile) {
              // Create a temporary object URL to load video metadata
              const tempObjectUrl = URL.createObjectURL(videoFile);
              const tempVideo = document.createElement('video');
              tempVideo.muted = true;
              tempVideo.preload = 'metadata';
              tempVideo.src = tempObjectUrl;

              await new Promise<void>((resolve) => {
                tempVideo.onloadedmetadata = () => {
                  width = tempVideo.videoWidth;
                  height = tempVideo.videoHeight;
                  // Convert float duration to integer to match database schema
                  duration = Math.round(tempVideo.duration);
                  resolve();
                };
                tempVideo.onerror = () => {
                  console.warn('Could not extract video metadata from file');
                  resolve();
                };
                setTimeout(resolve, 3000); // Timeout for metadata loading
              });

              // Clean up
              URL.revokeObjectURL(tempObjectUrl);
              tempVideo.src = '';
              tempVideo.load();
            }
          } catch (err) {
            console.warn('Error extracting video metadata:', err);
          }

          // Check if we already have a pre-generated thumbnail from the preview step
          if (file.preview) {
            console.log('Using pre-generated thumbnail from preview step');

            if (typeof file.preview === 'object' && file.preview.dataUrl) {
              // We already have a data URL from the preview generation
              thumbnailUrl = file.preview.dataUrl;
              console.log('Using pre-generated thumbnail data URL');
            } else if (typeof file.preview === 'string') {
              // We have a string URL or data URL
              thumbnailUrl = file.preview;
              console.log('Using pre-generated thumbnail string URL');
            } else {
              console.warn('Invalid preview format, using default file URL');
              thumbnailUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;
            }
          } else {
            console.warn('No pre-generated thumbnail available, using default video file URL');
            thumbnailUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;
          }
        }
        else {
          // For other file types, use the original URL
          thumbnailUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;
        }

        // Get the public URL
        const fileUrl = supabase.storage.from('medialibrary').getPublicUrl(storagePath).data.publicUrl;

        // Create the media item in the database
        const mediaItem = {
          teamId: teamId,
          name: file.name,
          description: null,
          fileUrl: fileUrl,
          thumbnailUrl: thumbnailUrl || fileUrl, // Fallback to fileUrl if no thumbnail
          fileType: fileTypeForCheck,
          fileSize: file.size,
          width: width || undefined,
          height: height || undefined,
          duration: duration || undefined,
        };

        console.log('Creating media item:', mediaItem);

        const mediaItemResponse = await apiRequest('POST', '/api/media', mediaItem);

        const parsedResponse = await handleApiResponse(mediaItemResponse) as {id: string};
        console.log('Created media item:', parsedResponse);

        // Associate tags with the media item if there are any
        if (tags.length > 0) {
          console.log('Adding', tags.length, 'tags to media item:', parsedResponse.id);

          try {
            // Resolve tag IDs or create new tags
            const tagIds = await resolveTagIds(tags, teamId);
            console.log('Tag IDs resolved:', tagIds);

            // Associate each tag with the media item
            if (tagIds.length > 0) {
              console.log('Associating', tagIds.length, 'tags with media item', parsedResponse.id);

              const tagAssociationPromises = tagIds.map(async (tagId) => {
                try {
                  // Tag association API endpoint
                  const tagAssociationUrl = `/api/media/${parsedResponse.id}/tags/${tagId}`;
                  console.log('Associating tag', tagId, 'with media', parsedResponse.id, '- URL:', tagAssociationUrl);

                  const tagAssociationResponse = await apiRequest('POST', tagAssociationUrl);

                  const parsedTagAssociation = await handleApiResponse(tagAssociationResponse);
                  console.log('Processed tag association response for tagId', tagId, ':', parsedTagAssociation);

                  return parsedTagAssociation;
                } catch (tagError) {
                  console.error('Error associating tag', tagId, 'with media', parsedResponse.id, ':', tagError);
                  return null;
                }
              });

              // Wait for all tag associations to complete
              const tagAssociationResults = await Promise.all(tagAssociationPromises);
              console.log('Tag association results:', tagAssociationResults);
            }
          } catch (tagsError) {
            console.error('Error processing tags:', tagsError);
          }
        }

        return parsedResponse;
      });

      try {
        const results = await Promise.all(uploadPromises);
        console.log('All uploads completed:', results);

        // Invalidate and refetch the media list immediately using the new optimized query keys
        console.log('Invalidating and refetching media cache for teamId:', teamId);

        // Invalidate the optimized media-with-tags query
        queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/media-with-tags`]});

        // Also invalidate tags in case new tags were created
        queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/tags`]});

        // Explicitly refetch to ensure the data is updated immediately
        await queryClient.refetchQueries({queryKey: [`/api/teams/${teamId}/media-with-tags`]});

        // Call onSuccess callback (which will close the modal)
        onSuccess();

        // Reset state
        setFiles([]);
        setTags([]);
      } catch (uploadError) {
        console.error('Error during file uploads:', uploadError);

        toast({
          title: "Upload failed",
          description: `Error uploading files: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error in uploadFiles:', error);

      toast({
        title: "Upload failed",
        description: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  // Helper function to resolve tag IDs or create new tags
  const resolveTagIds = async (tagNames: string[], teamId: string): Promise<string[]> => {
    try {
      // First, check if tags already exist
      console.log('Checking if tags exist:', tagNames);

      const existingTagsResponse = await apiRequest('GET', `/api/teams/${teamId}/tags`);

      const existingTags = await handleApiResponse(existingTagsResponse) as Array<{id: string, name: string}>;
      console.log('Processed tags response:', existingTags);

      // Map of tag names to IDs for existing tags
      const tagMap = new Map<string, string>();
      existingTags.forEach(tag => {
        tagMap.set(tag.name, tag.id);
      });

      // Resolve tags - use existing ID or create new tag
      const resolveTagPromises = tagNames.map(async (tagName) => {
        if (tagMap.has(tagName)) {
          console.log('Found existing tag:', tagName, 'with ID:', tagMap.get(tagName));
          return tagMap.get(tagName)!;
        } else {
          console.log('Creating new tag:', tagName);

          // Create a new tag
          const newTagData = {
            teamId: teamId,
            name: tagName,
            color: generateRandomColor(),
          };
          const newTagResponse = await apiRequest('POST', '/api/tags', newTagData);

          const newTag = await handleApiResponse(newTagResponse) as {id: string};
          console.log('Created new tag:', tagName, 'with ID:', newTag.id);

          // Invalidate tag queries to ensure UI is updated
          console.log('Invalidating tags cache for teamId:', teamId);
          queryClient.invalidateQueries({queryKey: [`/api/teams/${teamId}/tags`]});

          return newTag.id;
        }
      });

      return await Promise.all(resolveTagPromises);
    } catch (error) {
      console.error('Error resolving tags:', error);
      throw error;
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Upload Media</DialogTitle>
        </DialogHeader>

        <div
          className="border-2 border-dashed rounded-md p-6 mt-4 text-center cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-2">
            <p className="text-sm text-gray-500">
              Drag and drop files here, or click to select files
            </p>
            <p className="text-xs text-gray-400 mt-1">
              Supports images and videos
            </p>
          </div>
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            onChange={handleFileInputChange}
            multiple
            accept="image/*,video/*"
          />
        </div>

        {files.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium mb-2">Selected files</h3>
            <div className="space-y-2">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded border"
                >
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 flex-shrink-0 overflow-hidden rounded bg-gray-100">
                      {file.type.startsWith('image/') || file.type.startsWith('video/') ? (
                        <img
                          src={getPreviewUrl(file.preview)}
                          alt={file.name}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center bg-gray-200">
                          <File className="h-5 w-5 text-gray-500" />
                        </div>
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatBytes(file.size)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {file.uploadProgress > 0 && file.uploadProgress < 100 ? (
                      <Progress value={file.uploadProgress} className="w-20" />
                    ) : null}
                    <button
                      type="button"
                      onClick={() => removeFile(file.id)}
                      className="p-1 rounded-full text-gray-400 hover:text-gray-500"
                      disabled={isUploading}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Tags</h3>
          <TagInput value={tags} onChange={setTags} teamId={teamId} />
          <p className="text-xs text-gray-500 mt-1">
            Tags help you organize and find media easily
          </p>
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>
          <Button
            onClick={uploadFiles}
            disabled={files.length === 0 || isUploading}
            className="ml-2"
          >
            {isUploading ? 'Uploading...' : 'Upload Files'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}