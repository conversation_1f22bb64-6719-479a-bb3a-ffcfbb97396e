import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog";
import { CircularProgress } from "@/components/ui/circular-progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useScreens } from "@/hooks/use-screens";
import { Monitor, Smartphone, HardDrive, Cpu, MemoryStick, Download, CreditCard } from "lucide-react";
import { format } from "date-fns";

interface ScreenDetailsModalProps {
  screenId: string | null;
  teamId: string;
  isOpen: boolean;
  onClose: () => void;
}

interface HealthData {
  app_version?: string;
  build_number?: string;
  package_name?: string;
  platform?: string;
  timestamp?: string;

  // Memory fields
  memory_total?: number;
  memory_free?: number;
  memory_available?: number;
  memory_used?: number;
  memory_usage_percentage?: string;

  // CPU fields (Desktop)
  cpu_usage_percentage?: string;

  // Storage fields (Android)
  internal_storage_total?: string;
  internal_storage_used?: string;
  internal_storage_available?: string;
  internal_storage_usage_percentage?: string;
  external_storage_total?: string;
  external_storage_used?: string;
  external_storage_available?: string;
  external_storage_usage_percentage?: string;

  // Disk fields (Desktop) - dynamic keys
  [key: string]: any;
}

export function ScreenDetailsModal({
  screenId,
  teamId,
  isOpen,
  onClose,
}: ScreenDetailsModalProps) {
  const { useScreen } = useScreens(teamId);
  const screenData = useScreen(screenId);

  if (!screenData || !screenId) {
    return null;
  }

  const screen = screenData.data;
  const health: HealthData = screen?.health || {};

  // Parse file download progress
  const fileDownloadCount = screen?.maxFileDownloadCount || 0;
  const totalFileDownload = screen?.maxTotalFileDownload || 0;
  const downloadPercentage = totalFileDownload > 0 ? Math.round((fileDownloadCount / totalFileDownload) * 100) : 0;

  // Helper function to format memory values
  const formatMemory = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    } else if (bytes >= 1024 * 1024) {
      return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    } else if (bytes >= 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`;
    }
    return `${bytes} B`;
  };

  // Helper function to format disk size
  const formatDiskSize = (size: number): string => {
    if (size >= 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
    } else if (size >= 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    } else if (size >= 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    }
    return `${size} B`;
  };

  // Parse disk information for Desktop/Linux
  const getDiskInfo = () => {
    const disks: Array<{
      filesystem: string;
      total: number;
      used: number;
      available: number;
      usage_percentage: string;
    }> = [];

    Object.keys(health).forEach(key => {
      if (key.includes('_filesystem')) {
        const prefix = key.replace('_filesystem', '');
        const filesystem = health[key];
        const total = health[`${prefix}_total`];
        const used = health[`${prefix}_used`];
        const available = health[`${prefix}_available`];
        const usage_percentage = health[`${prefix}_usage_percentage`];

        if (filesystem && total !== undefined) {
          disks.push({
            filesystem,
            total,
            used,
            available,
            usage_percentage
          });
        }
      }
    });

    return disks;
  };

  const renderHealthDetails = () => {
    const isAndroid = health.platform === 'Android';
    const isDesktop = health.platform === 'Linux' || health.platform === 'Windows' || health.platform === 'Darwin';

    return (
      <div className="space-y-6">
        {/* App Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              {isAndroid ? <Smartphone className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
              Application Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">App Version:</span>
                <span className="ml-2 font-medium">{health.app_version || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Build Number:</span>
                <span className="ml-2 font-medium">{health.build_number || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Package Name:</span>
                <span className="ml-2 font-medium">{health.package_name || 'N/A'}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Platform:</span>
                <span className="ml-2">
                  <Badge variant="outline">{health.platform || 'Unknown'}</Badge>
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Memory Information */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MemoryStick className="h-4 w-4" />
              Memory Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 gap-2 text-sm">
              {health.memory_total && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Total:</span>
                  <span className="font-medium">{formatMemory(health.memory_total)}</span>
                </div>
              )}
              {health.memory_free && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Free:</span>
                  <span className="font-medium">{formatMemory(health.memory_free)}</span>
                </div>
              )}
              {health.memory_available && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Available:</span>
                  <span className="font-medium">{formatMemory(health.memory_available)}</span>
                </div>
              )}
              {health.memory_used && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Used:</span>
                  <span className="font-medium">{formatMemory(health.memory_used)}</span>
                </div>
              )}
              {health.memory_usage_percentage && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Memory Usage:</span>
                  <span className="font-medium">{health.memory_usage_percentage}%</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* CPU Information (Desktop only) */}
        {isDesktop && health.cpu_usage_percentage && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Cpu className="h-4 w-4" />
                CPU Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">CPU Usage:</span>
                <span className="font-medium">{health.cpu_usage_percentage}%</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Storage Information (Android) */}
        {isAndroid && (health.internal_storage_total || health.external_storage_total) && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                Storage Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {health.internal_storage_total && (
                <div>
                  <h4 className="font-medium text-sm mb-2">Internal Storage</h4>
                  <div className="grid grid-cols-1 gap-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total:</span>
                      <span className="font-medium">{health.internal_storage_total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Used:</span>
                      <span className="font-medium">{health.internal_storage_used}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Available:</span>
                      <span className="font-medium">{health.internal_storage_available}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Usage:</span>
                      <span className="font-medium">{health.internal_storage_usage_percentage}%</span>
                    </div>
                  </div>
                </div>
              )}

              {health.external_storage_total && (
                <div>
                  <h4 className="font-medium text-sm mb-2">External Storage</h4>
                  <div className="grid grid-cols-1 gap-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total:</span>
                      <span className="font-medium">{health.external_storage_total}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Used:</span>
                      <span className="font-medium">{health.external_storage_used}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Available:</span>
                      <span className="font-medium">{health.external_storage_available}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Usage:</span>
                      <span className="font-medium">{health.external_storage_usage_percentage}%</span>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Disk Information (Desktop) */}
        {isDesktop && getDiskInfo().length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <HardDrive className="h-4 w-4" />
                Disk Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {getDiskInfo().map((disk, index) => (
                <div key={index}>
                  <h4 className="font-medium text-sm mb-2">{disk.filesystem}</h4>
                  <div className="grid grid-cols-1 gap-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total:</span>
                      <span className="font-medium">{formatDiskSize(disk.total)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Used:</span>
                      <span className="font-medium">{formatDiskSize(disk.used)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Available:</span>
                      <span className="font-medium">{formatDiskSize(disk.available)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Usage:</span>
                      <span className="font-medium">{disk.usage_percentage}%</span>
                    </div>
                  </div>
                  {index < getDiskInfo().length - 1 && <Separator className="mt-3" />}
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderSubscriptionDetails = () => {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Subscription Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-1 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Status:</span>
              <span className="ml-2">
                <Badge
                  variant={
                    screen?.subscriptionStatus === "active"
                      ? "default"
                      : screen?.subscriptionStatus === "trial"
                      ? "secondary"
                      : "outline"
                  }
                >
                  {screen?.subscriptionStatus || 'Unregistered'}
                </Badge>
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Billing Cycle:</span>
              <span className="ml-2 font-medium capitalize">
                {screen?.billingCycle || 'N/A'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">
                {screen?.subscriptionStatus === "active" ? "Subscription Ends At:" : "Trial Ends At:"}
              </span>
              <span className="ml-2 font-medium">
                {screen?.trialEndsAt
                  ? format(new Date(screen.trialEndsAt), "MMM dd, yyyy")
                  : 'N/A'
                }
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">LemonSqueezy ID:</span>
              <span className="ml-2 font-medium">
                {screen?.lemonsqueezySubscriptionId || 'N/A'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>
            {screen?.name} ({screen?.code})
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-4 overflow-y-auto max-h-[75vh]">
          {/* Left Panel - Health Information */}
          <div className="space-y-6">
            {renderHealthDetails()}
          </div>

          {/* Right Panel - File Download Progress & Subscription */}
          <div className="space-y-6">
            {/* File Download Progress Section */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  File Download Progress
                </CardTitle>
              </CardHeader>
              <CardContent className="flex flex-col items-center space-y-4">
                <CircularProgress
                  value={fileDownloadCount}
                  max={totalFileDownload}
                  size={150}
                  strokeWidth={12}
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {fileDownloadCount}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      of {totalFileDownload}
                    </div>
                  </div>
                </CircularProgress>

                <div className="text-center">
                  <div className="text-lg font-semibold">
                    {downloadPercentage}% Complete
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {fileDownloadCount} of {totalFileDownload} files downloaded
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscription Information Section */}
            {renderSubscriptionDetails()}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
