a.	profiles table schema
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "email",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "first_name",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "last_name",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "avatar_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

b.	teams table schema:
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "logo_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

c.	team_members table schema:
[
  {
    "column_name": "profile_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "role",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

“role” can be either “owner” or “member”

d.	media_items table schema;
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "file_url",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "thumbnail_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "file_type",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "file_size",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "width",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "height",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "duration",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

e.	screens table schema;
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "location",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "code",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "last_ping_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "start_time",
    "data_type": "time without time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "end_time",
    "data_type": "time without time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "update_frequency",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "site_email",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

f.	campaigns table schema;
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "start_date",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "end_date",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "status",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

g.	campaign_medias table schema;
[
  {
    "column_name": "campaign_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "media_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "display_order",
    "data_type": "integer",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

h.	campaign_screens table schema;
[
  {
    "column_name": "campaign_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "screen_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

i.	slides table schema;
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "content",
    "data_type": "jsonb",
    "is_nullable": "NO"
  },
  {
    "column_name": "thumbnail_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "slide_width",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "slide_height",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "slide_background_color",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "api_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "api_data_preview_duration",
    "data_type": "integer",
    "is_nullable": "YES"
  }
]

j.	tags table schema;
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "color",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

k.	media_tags table schema;
[
  {
    "column_name": "media_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "tag_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

l.	screen_tags table schema
[
  {
    "column_name": "screen_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "tag_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]
