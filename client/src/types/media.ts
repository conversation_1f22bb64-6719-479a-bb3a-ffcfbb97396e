export interface MediaItem {
  id: string;
  teamId: string | null;    // db: team_id
  name: string;
  description: string | null;
  fileUrl: string;          // db: file_url
  thumbnailUrl: string | null; // db: thumbnail_url
  fileType: string;         // db: file_type (primary)
  file_type?: string;       // For compatibility with API responses
  type?: string;            // For backward compatibility
  width: number | null;
  height: number | null;
  duration: number | null;
  fileSize: number | null;  // db: file_size
  createdAt: Date | null;   // db: created_at
  updatedAt: Date | null;   // db: updated_at
  tags?: string[];          // Not in DB - populated by join with media_tags table
}

export interface Tag {
  id: string;
  teamId: string | null;    // db: team_id
  name: string;
  color: string | null;
  createdAt: Date | null;   // db: created_at
  updatedAt: Date | null;   // db: updated_at
}

export interface MediaTag {
  mediaId: string;         // db: media_id
  tagId: string;           // db: tag_id
  createdAt: Date | null;  // db: created_at
}