import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Slide, InsertSlide } from "@shared/schema";

export function useSlides(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get all slides for the team
  const {
    data: slides,
    isLoading,
    error,
  } = useQuery<Slide[]>({
    queryKey: [`/api/teams/${teamId}/slides`],
    enabled: !!teamId,
  });

  // Get a single slide
  const useSlide = (slideId: string | null) => {
    return useQuery<Slide>({
      queryKey: [`/api/slides/${slideId}`],
      enabled: !!slideId,
    });
  };

  // Create a new slide
  const createSlide = async (slide: InsertSlide) => {
    try {
      const newSlide = await apiRequest<Slide>("/api/slides", {
        method: "POST",
        data: slide,
      });
      
      await queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/slides`] });
      toast({
        title: "Success",
        description: "Slide created successfully",
      });
      
      return newSlide;
    } catch (error) {
      console.error("Failed to create slide:", error);
      toast({
        title: "Error",
        description: "Failed to create slide",
        variant: "destructive",
      });
    }
  };

  // Update a slide
  const updateSlide = async ({ id, data }: { id: string; data: Partial<InsertSlide> }) => {
    try {
      const updatedSlide = await apiRequest<Slide>(`/api/slides/${id}`, {
        method: "PATCH",
        data,
      });
      
      await queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/slides`] });
      await queryClient.invalidateQueries({ queryKey: [`/api/slides/${id}`] });
      
      toast({
        title: "Success",
        description: "Slide updated successfully",
      });
      
      return updatedSlide;
    } catch (error) {
      console.error("Failed to update slide:", error);
      toast({
        title: "Error",
        description: "Failed to update slide",
        variant: "destructive",
      });
    }
  };

  // Delete a slide
  const deleteSlide = async (id: string) => {
    try {
      await apiRequest(`/api/slides/${id}`, {
        method: "DELETE",
      });
      
      await queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/slides`] });
      
      toast({
        title: "Success",
        description: "Slide deleted successfully",
      });
    } catch (error) {
      console.error("Failed to delete slide:", error);
      toast({
        title: "Error",
        description: "Failed to delete slide",
        variant: "destructive",
      });
    }
  };

  return {
    slides,
    isLoading,
    error,
    useSlide,
    createSlide,
    updateSlide,
    deleteSlide,
  };
}