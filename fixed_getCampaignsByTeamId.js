// Fixed version of the getCampaignsByTeamId function

async getCampaignsByTeamId(teamId: string): Promise<Campaign[]> {
  console.log(`[getCampaignsByTeamId] Fetching campaigns with counts for team ID: ${teamId}`);
  
  // First try the RPC function if available
  try {
    const { data, error } = await supabase
      .rpc('get_campaigns_with_counts', { team_id_param: teamId });
      
    if (!error && data && data.length > 0) {
      console.log(`[getCampaignsByTeamId] Successfully used RPC function. Found ${data.length} campaigns.`);
      
      return data.map(campaign => ({
        id: campaign.id,
        teamId: campaign.team_id,
        name: campaign.name,
        description: campaign.description,
        startDate: campaign.start_date ? new Date(campaign.start_date) : undefined,
        endDate: campaign.end_date ? new Date(campaign.end_date) : undefined,
        status: campaign.status,
        createdAt: new Date(campaign.created_at),
        updatedAt: new Date(campaign.updated_at),
        screenCount: campaign.screen_count || 0,
        mediaCount: campaign.media_count || 0,
        slideCount: campaign.slide_count || 0
      }));
    } else {
      console.log(`[getCampaignsByTeamId] RPC function not available or returned no data. Using fallback method.`);
    }
  } catch (rpcError) {
    console.error(`[getCampaignsByTeamId] Error using RPC function:`, rpcError);
  }
  
  // Fallback method: Get data with individual queries
  // 1. Get all campaigns for the team
  const { data: campaigns, error } = await supabase
    .from('campaigns')
    .select('*')
    .eq('team_id', teamId);
  
  if (error || !campaigns) {
    console.error("[getCampaignsByTeamId] Error fetching campaigns:", error);
    return [];
  }
  
  console.log(`[getCampaignsByTeamId] Found ${campaigns.length} campaigns for team ${teamId}`);
  
  // If no campaigns, return empty array
  if (campaigns.length === 0) {
    return [];
  }
  
  // 2. Extract all campaign IDs for efficient lookup
  const campaignIds = campaigns.map(c => c.id);
  
  // 3. For each campaign, fetch its screen count
  const screenCountMap = new Map();
  for (const campaignId of campaignIds) {
    try {
      const { data, error } = await supabase
        .from('campaign_screens')
        .select('*')
        .eq('campaign_id', campaignId);
      
      if (!error && data) {
        screenCountMap.set(campaignId, data.length);
      }
    } catch (err) {
      console.error(`Error fetching screen count for campaign ${campaignId}:`, err);
    }
  }
  
  // 4. For each campaign, fetch its media count (campaign_type = 0)
  const mediaCountMap = new Map();
  for (const campaignId of campaignIds) {
    try {
      const { data, error } = await supabase
        .from('campaign_medias')
        .select('*')
        .eq('campaign_id', campaignId)
        .eq('campaign_type', 0);
      
      if (!error && data) {
        mediaCountMap.set(campaignId, data.length);
      }
    } catch (err) {
      console.error(`Error fetching media count for campaign ${campaignId}:`, err);
    }
  }
  
  // 5. For each campaign, fetch its slide count (campaign_type = 1)
  const slideCountMap = new Map();
  for (const campaignId of campaignIds) {
    try {
      const { data, error } = await supabase
        .from('campaign_medias')
        .select('*')
        .eq('campaign_id', campaignId)
        .eq('campaign_type', 1);
      
      if (!error && data) {
        slideCountMap.set(campaignId, data.length);
      }
    } catch (err) {
      console.error(`Error fetching slide count for campaign ${campaignId}:`, err);
    }
  }
  
  // For debugging
  console.log(`[getCampaignsByTeamId] Count maps:`, {
    screens: Object.fromEntries(screenCountMap),
    media: Object.fromEntries(mediaCountMap),
    slides: Object.fromEntries(slideCountMap)
  });

  // 6. Combine all data into the final result
  return campaigns.map(campaign => ({
    id: campaign.id,
    teamId: campaign.team_id,
    name: campaign.name,
    description: campaign.description,
    startDate: campaign.start_date ? new Date(campaign.start_date) : undefined,
    endDate: campaign.end_date ? new Date(campaign.end_date) : undefined,
    status: campaign.status,
    createdAt: new Date(campaign.created_at),
    updatedAt: new Date(campaign.updated_at),
    screenCount: screenCountMap.get(campaign.id) || 0,
    mediaCount: mediaCountMap.get(campaign.id) || 0,
    slideCount: slideCountMap.get(campaign.id) || 0
  }));
}