import { useRouter, Router, useLocation } from 'wouter';
import { useState, useRef, useLayoutEffect } from 'react';

/**
 * Clean URLs to avoid double-slash issues in the Replit environment.
 * This cleans up URLs to avoid the Invalid href error in next/router.
 */
export const createCleanUrlHook = () => {
  return () => {
    const [location, navigate] = useLocation();
    
    const cleanNavigate = (to: string) => {
      // Remove any leading slashes and then add exactly one
      const cleanPath = to.replace(/^\/+/, '');
      navigate(cleanPath);
    };
    
    return [location, cleanNavigate];
  };
};

// Create a custom hook that ensures clean URLs without double slashes
export const useCleanLocation = createCleanUrlHook();