Auth state changed: INITIAL_SESSION
Console.js:61 Auth state changed: SIGNED_IN
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 TeamId changed, resetting initialization state
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
3
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
Array(3)
Console.js:61 TeamId changed, resetting initialization state
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: false, isDataChanging: false, prevDataLength: null, currentDataLength: 0
6
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
2

 Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies.
2
Console.js:61 Media hook state - mediaQuery.isLoading: true, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 0
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: null, currentDataLength: 10
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: true, hasInitialized: true, isDataChanging: false, prevDataLength: 10, currentDataLength: 10
Console.js:61 Media hook state - mediaQuery.isLoading: false, isTagsLoading: false, hasInitialized: true, isDataChanging: false, prevDataLength: 10, currentDataLength: 10
﻿
