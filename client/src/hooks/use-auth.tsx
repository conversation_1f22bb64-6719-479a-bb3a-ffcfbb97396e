import { useState, useEffect, createContext, useContext } from "react";
import { useLocation } from "wouter";
import { supabase, changePasswordDirectly } from "@/lib/supabase";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  team?: any;  // Optional team property for user's team data
  role?: string; // Optional role property for user's role
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string, company: string) => Promise<void>;
  signOut: () => Promise<void>;
  updatePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (password: string) => Promise<void>;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signIn: async () => {},
  signUp: async () => {},
  signOut: async () => {},
  updatePassword: async () => {},
  forgotPassword: async () => {},
  resetPassword: async () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [_, navigate] = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    const initAuth = async () => {
      try {
        console.log('🔄 Initializing authentication...');

        // Debug: Check what's in localStorage
        const authData = localStorage.getItem('supabase-auth');
        console.log('🔍 Auth data in localStorage:', authData ? 'Found' : 'Not found');
        if (authData) {
          try {
            const parsed = JSON.parse(authData);
            if (parsed.expires_at) {
              console.log('🔍 Session expires at:', new Date(parsed.expires_at * 1000));
              console.log('🔍 Current time:', new Date());
              console.log('🔍 Session valid:', new Date(parsed.expires_at * 1000) > new Date());
            }
          } catch (e) {
            console.warn('⚠️ Could not parse auth data:', e);
          }
        }

        // Get the current session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('❌ Session error:', error);
          setLoading(false);
          return;
        }

        if (session?.user) {
          console.log('✅ Session found, fetching user profile...');

          // First, get basic profile data
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single();

          if (profileError) {
            console.error('❌ Profile fetch error:', profileError);
            // Even if profile fetch fails, we can still set basic user info from session
            setUser({
              id: session.user.id,
              email: session.user.email ?? '',
              firstName: undefined,
              lastName: undefined,
              avatarUrl: undefined,
            });
            setLoading(false);
            return;
          }

          // Try to get team info separately (non-blocking)
          let teamData = null;
          let userRole = null;

          try {
            const { data: teamMemberData } = await supabase
              .from('team_members')
              .select(`
                role,
                teams (
                  id,
                  name
                )
              `)
              .eq('profile_id', session.user.id)
              .limit(1)
              .single();

            if (teamMemberData) {
              teamData = teamMemberData.teams;
              userRole = teamMemberData.role;
            }
          } catch (teamError) {
            console.warn('⚠️ Could not fetch team data:', teamError);
            // This is non-critical, continue without team data
          }

          setUser({
            id: session.user.id,
            email: session.user.email ?? '',
            firstName: profileData?.first_name,
            lastName: profileData?.last_name,
            avatarUrl: profileData?.avatar_url,
            team: teamData,
            role: userRole
          });

          console.log('✅ User authenticated and profile loaded');
        } else {
          console.log('ℹ️ No active session found');
        }
      } catch (error) {
        console.error('❌ Error initializing auth:', error);
        // Don't leave user in loading state on error
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Add a timeout to prevent infinite loading (only for initial auth)
    const timeoutId = setTimeout(() => {
      console.warn('⚠️ Auth initialization timeout - this is normal if no session exists');
      if (loading) {
        setLoading(false);
      }
    }, 5000); // 5 second timeout

    initAuth().finally(() => {
      clearTimeout(timeoutId);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔔 Auth state changed:', event);

        if (event === 'SIGNED_IN' && session) {
          console.log('✅ Processing SIGNED_IN event...');

          try {
            console.log('🔍 Fetching user profile for ID:', session.user.id);

            // Add timeout to profile fetch to prevent hanging
            const profilePromise = supabase
              .from('profiles')
              .select('*')
              .eq('id', session.user.id)
              .single();

            const timeoutPromise = new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Profile fetch timeout')), 5000)
            );

            const { data: profileData, error: profileError } = await Promise.race([
              profilePromise,
              timeoutPromise
            ]) as any;

            if (profileError || profileError?.message === 'Profile fetch timeout') {
              console.error('❌ Profile fetch error on state change:', profileError);
              // Set basic user info even if profile fetch fails
              console.log('🔄 Setting basic user info due to profile error');
              setUser({
                id: session.user.id,
                email: session.user.email ?? '',
                firstName: session.user.user_metadata?.first_name,
                lastName: session.user.user_metadata?.last_name,
                avatarUrl: undefined,
              });
              console.log('✅ Basic user info set, setting loading to false');
              setLoading(false);
              return;
            }

            console.log('✅ Profile data fetched successfully:', profileData);

            // Try to get team info
            console.log('🔍 Fetching team data...');
            let teamData = null;
            let userRole = null;

            try {
              const { data: teamMemberData } = await supabase
                .from('team_members')
                .select(`
                  role,
                  teams (
                    id,
                    name
                  )
                `)
                .eq('profile_id', session.user.id)
                .limit(1)
                .single();

              if (teamMemberData) {
                teamData = teamMemberData.teams;
                userRole = teamMemberData.role;
                console.log('✅ Team data fetched:', teamData);
              } else {
                console.log('ℹ️ No team data found for user');
              }
            } catch (teamError) {
              console.warn('⚠️ Could not fetch team data in auth change:', teamError);
            }

            console.log('🔄 Setting complete user profile...');
            setUser({
              id: session.user.id,
              email: session.user.email ?? '',
              firstName: profileData?.first_name,
              lastName: profileData?.last_name,
              avatarUrl: profileData?.avatar_url,
              team: teamData,
              role: userRole
            });

            console.log('✅ User profile set from auth state change');
            console.log('🔄 Setting loading to false...');
            setLoading(false);
            console.log('✅ Loading set to false - auth process complete');
          } catch (error) {
            console.error('❌ Error in SIGNED_IN handler:', error);
            setLoading(false);
          }
        } else if (event === 'SIGNED_OUT') {
          console.log('🚪 Processing SIGNED_OUT event...');
          setUser(null);
          setLoading(false);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      console.log('🔑 Starting sign in process...');

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error('❌ Sign in error:', error);
        throw error;
      }

      if (data.user && data.session) {
        console.log('✅ Sign in successful, user authenticated');

        // Don't manually set user here - let the auth state change handler do it
        // This prevents race conditions
        console.log('🔄 Navigating to dashboard...');
        navigate('dashboard');

        // Don't set loading to false here - let the auth state change handler do it
        // This prevents the button from getting stuck
        return;
      }
    } catch (error: any) {
      console.error('Sign in error:', error);
      setLoading(false); // Only set loading false on error
      toast({
        title: "Sign in failed",
        description: error.message || "There was a problem signing in.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const signUp = async (
    email: string,
    password: string,
    firstName: string,
    lastName: string,
    company: string
  ) => {
    try {
      setLoading(true);

      // Sign up the user
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName
          }
        }
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Create a new team
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .insert([
            { name: company }
          ])
          .select();

        if (teamError) {
          throw teamError;
        }

        // Create a profile for the user
        const { error: profileError } = await supabase
          .from('profiles')
          .upsert([
            {
              id: data.user.id,
              email: data.user.email,
              first_name: firstName,
              last_name: lastName
            }
          ]);

        if (profileError) {
          throw profileError;
        }

        // Add the user to the team
        const { error: memberError } = await supabase
          .from('team_members')
          .insert([
            {
              profile_id: data.user.id,
              team_id: teamData[0].id,
              role: 'owner'
            }
          ]);

        if (memberError) {
          throw memberError;
        }

        toast({
          title: "Account created!",
          description: "Please check your email to confirm your account.",
        });

        navigate('login');
      }
    } catch (error: any) {
      console.error('Sign up error:', error);
      toast({
        title: "Sign up failed",
        description: error.message || "There was a problem creating your account.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      console.log('🚪 Starting sign out process...');

      // Check if we have a session first
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Current session:', session ? 'exists' : 'none');

      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('❌ Sign out error:', error);
        // Even if signOut fails, clear local state
        setUser(null);
        navigate('login');
        throw error;
      }

      console.log('✅ Sign out successful');
      setUser(null);
      // Clean URL path to prevent double slashes
      navigate('login');
      toast({
        title: "Signed out",
        description: "You have been signed out successfully.",
      });
    } catch (error: any) {
      console.error('Sign out error:', error);

      // Force local logout even if remote logout fails
      setUser(null);
      navigate('login');

      toast({
        title: "Signed out",
        description: "You have been signed out (local session cleared).",
      });
    }
  };

  const updatePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setLoading(true);

      // Check if we have user email
      if (!user?.email) {
        throw new Error("User email not found");
      }

      // Use the direct password change method
      const result = await changePasswordDirectly(
        user.email,
        currentPassword,
        newPassword
      );

      if (!result.success) {
        throw new Error(result.error || "Password update failed");
      }

      // Show success message
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully. Please log in again with your new password.",
      });

      // The user is already signed out by the changePasswordDirectly function
      // Just clear the user from the context
      setUser(null);

      // Redirect to login page
      navigate('login');

      return;
    } catch (error: any) {
      console.error('Password update error:', error);
      toast({
        title: "Password update failed",
        description: error.message || "There was a problem updating your password.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      console.log('🔑 Starting password reset request for:', email);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        console.error('❌ Password reset request error:', error);
        throw error;
      }

      console.log('✅ Password reset email sent successfully');
      toast({
        title: "Password reset email sent",
        description: "Check your email for a link to reset your password.",
      });
    } catch (error: any) {
      console.error('Password reset request error:', error);
      toast({
        title: "Password reset failed",
        description: error.message || "There was a problem sending the reset email.",
        variant: "destructive",
      });
      throw error;
    }
  };

  const resetPassword = async (password: string) => {
    try {
      console.log('🔑 Starting password reset...');

      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        console.error('❌ Password reset error:', error);
        throw error;
      }

      console.log('✅ Password reset successful');
      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      });

      // Redirect to dashboard after successful reset
      navigate('dashboard');
    } catch (error: any) {
      console.error('Password reset error:', error);
      toast({
        title: "Password reset failed",
        description: error.message || "There was a problem resetting your password.",
        variant: "destructive",
      });
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signIn,
      signUp,
      signOut,
      updatePassword,
      forgotPassword,
      resetPassword
    }}>
      {children}
    </AuthContext.Provider>
  );
};
