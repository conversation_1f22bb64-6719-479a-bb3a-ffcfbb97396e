import { useState, KeyboardEvent, useRef, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";

interface Tag {
  id: string;
  name: string;
  color?: string;
}

interface TagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  teamId?: string;
  onCreateTag?: (name: string) => Promise<Tag>;
}

export function TagInput({
  value,
  onChange,
  placeholder = "Add tag...",
  disabled = false,
  className,
  teamId,
  onCreateTag,
}: TagInputProps) {
  const [inputValue, setInputValue] = useState("");
  const [open, setOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Fetch team tags if teamId is provided
  const { data: tags = [] } = useQuery<Tag[]>({
    queryKey: ['/api/teams', teamId || '', 'tags'],
    enabled: !!teamId,
  });

  // Filter tags based on input value, with case-insensitive matching
  const filteredTags = tags.filter((tag: Tag) => 
    tag.name.toLowerCase().includes(inputValue.toLowerCase()) && 
    !value.some(v => v.toLowerCase() === tag.name.toLowerCase())
  );

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollLeft = containerRef.current.scrollWidth;
    }
  }, [value]);

  const handleInputKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (disabled) return;
    
    // Add tag on Enter
    if (e.key === "Enter" && inputValue.trim()) {
      e.preventDefault();
      // Check if we should create a new tag
      const existingTag = tags.find((tag: Tag) => tag.name.toLowerCase() === inputValue.toLowerCase());
      
      if (existingTag) {
        // If tag exists, just add it
        addTag(existingTag.name);
      } else if (onCreateTag) {
        // If tag doesn't exist and we have a createTag function, create and add it
        const tagText = inputValue.trim();
        console.log(`Creating new tag on-the-fly: "${tagText}"`);
        
        try {
          // Clear input immediately to prevent duplicate attempts
          setInputValue("");
          
          onCreateTag(tagText)
            .then(newTag => {
              console.log(`Successfully created tag:`, newTag);
              if (newTag && newTag.name) {
                addTag(newTag.name);
              } else {
                console.error("Created tag is missing name:", newTag);
                // Fallback to using the original input
                addTag(tagText);
              }
            })
            .catch(error => {
              console.error("Error creating tag:", error);
              // Even if tag creation fails in the backend, we can still
              // add it to the input as a temporary measure
              addTag(tagText);
            });
        } catch (error) {
          console.error("Exception during tag creation:", error);
          // Fallback to using the original input
          addTag(tagText);
        }
      } else {
        // Otherwise just add the tag as a string
        addTag(inputValue.trim());
      }
    }
    
    // Handle comma as separator
    if (e.key === "," && inputValue.trim()) {
      e.preventDefault();
      addTag(inputValue.trim().replace(",", ""));
    }
    
    // Remove last tag on Backspace if input is empty
    if (e.key === "Backspace" && !inputValue && value.length > 0) {
      removeTag(value.length - 1);
    }
  };

  const addTag = (tag: string) => {
    if (!tag) return;
    
    // Prevent duplicates
    if (!value.includes(tag)) {
      onChange([...value, tag]);
    }
    
    setInputValue("");
    setOpen(false);
  };

  const removeTag = (index: number) => {
    onChange(value.filter((_, i) => i !== index));
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "flex flex-wrap gap-2 p-2 border border-input rounded-md overflow-x-auto",
        disabled ? "bg-muted" : "bg-background",
        className
      )}
      onClick={handleContainerClick}
    >
      {value.map((tag, index) => (
        <Badge
          key={`${tag}-${index}`}
          variant="secondary"
          className="flex items-center h-6 px-2 text-sm"
        >
          {tag}
          <button
            type="button"
            onClick={() => !disabled && removeTag(index)}
            className={cn(
              "ml-1 text-muted-foreground hover:text-foreground",
              disabled && "cursor-not-allowed opacity-50"
            )}
            disabled={disabled}
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      ))}
      <Popover open={open && filteredTags.length > 0} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value);
              if (e.target.value) {
                setOpen(true);
              } else {
                setOpen(false);
              }
            }}
            onKeyDown={handleInputKeyDown}
            onFocus={() => inputValue && setOpen(true)}
            placeholder={value.length === 0 ? placeholder : ""}
            className="flex-1 min-w-[120px] h-6 p-0 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
            disabled={disabled}
          />
        </PopoverTrigger>
        <PopoverContent className="p-0" align="start" sideOffset={5}>
          <Command>
            <CommandList>
              <CommandInput placeholder="Search tags..." />
              <CommandEmpty>No tags found, press enter to create</CommandEmpty>
              <CommandGroup>
                {filteredTags.map((tag: Tag) => (
                  <CommandItem
                    key={tag.id}
                    value={tag.name}
                    onSelect={() => {
                      addTag(tag.name);
                    }}
                  >
                    {tag.name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
