import React from 'react';
import { Link as WouterLink } from 'wouter';

interface AppLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * Custom Link component that properly handles relative paths within the application
 * while avoiding double-slash issues in the Replit environment
 */
const AppLink: React.FC<AppLinkProps> = ({ to, children, className, onClick }) => {
  // Ensure we only have a clean path without the domain and without leading slashes
  const formattedPath = to.replace(/^\/+/, '');
  
  // Prevent any navigation if onClick is provided, as it's likely a custom handler
  const handleClick = onClick ? (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    onClick();
  } : undefined;
  
  return (
    <a 
      href={`/${formattedPath}`} 
      className={className}
      onClick={handleClick}
    >
      {children}
    </a>
  );
};

export default AppLink;