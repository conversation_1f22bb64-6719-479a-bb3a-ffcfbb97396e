/**
 * Path utility functions to fix URL issues in the Replit environment
 */

/**
 * Normalizes a path to ensure it doesn't have leading double slashes
 * This helps prevent the Next.js router invalid href errors in Replit
 * 
 * @param path The path to normalize
 * @returns Normalized path without leading double slashes
 */
export function normalizePath(path: string): string {
  // If path is empty or undefined, return "/"
  if (!path) return "/";
  
  // If path already starts with "/", just return it as is
  if (path.startsWith("/")) {
    return path;
  }
  
  // Otherwise, add a single leading slash
  return `/${path}`;
}

/**
 * Creates a clean URL without double slashes
 * 
 * @param base The base part of the URL (can be empty)
 * @param path The path part to append
 * @returns A properly formatted URL without double slashes
 */
export function createCleanUrl(base: string, path: string): string {
  // Remove trailing slashes from base
  const cleanBase = base ? base.replace(/\/+$/, "") : "";
  
  // Remove leading slashes from path
  const cleanPath = path ? path.replace(/^\/+/, "") : "";
  
  // Join with a single slash
  if (!cleanBase) {
    return cleanPath ? `/${cleanPath}` : "/";
  }
  
  return cleanPath ? `${cleanBase}/${cleanPath}` : cleanBase;
}