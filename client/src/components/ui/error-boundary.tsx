import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallbackComponent?: ReactNode;
  onReset?: () => void;
  componentName?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });

    // You could also log the error to an error reporting service here
    // logErrorToService(error, errorInfo);
  }

  handleReset = (): void => {
    if (this.props.onReset) {
      this.props.onReset();
    }
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render(): ReactNode {
    const { hasError, error } = this.state;
    const { children, fallbackComponent, componentName } = this.props;

    if (hasError) {
      // You can render any custom fallback UI
      if (fallbackComponent) {
        return fallbackComponent;
      }

      return (
        <Card className="w-full max-w-lg mx-auto mt-8 shadow-lg">
          <CardHeader className="bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-300">
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Error in {componentName || 'Component'}
            </CardTitle>
            <CardDescription className="text-red-700 dark:text-red-300">
              An error occurred in this component
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="text-sm font-mono p-4 bg-slate-100 dark:bg-slate-900 rounded overflow-auto max-h-48">
              {error?.message || 'Unknown error'}
            </div>
            <p className="mt-4 text-muted-foreground text-sm">
              The component has been contained to prevent the entire application from crashing.
            </p>
          </CardContent>
          <CardFooter>
            <Button 
              onClick={this.handleReset} 
              variant="outline" 
              className="gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          </CardFooter>
        </Card>
      );
    }

    return children;
  }
}

export default ErrorBoundary;