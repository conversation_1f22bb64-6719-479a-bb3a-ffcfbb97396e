-- Update invoices table to add default values for timestamp fields
-- This will automatically set current timestamp when records are inserted

-- Add default value for invoice_date (current timestamp when invoice is created)
ALTER TABLE invoices
ALTER COLUMN invoice_date SET DEFAULT TIMEZONE('utc', NOW());

-- Add default value for paid_at (current timestamp for immediate payment simulation)
ALTER TABLE invoices
ALTER COLUMN paid_at SET DEFAULT TIMEZONE('utc', NOW());

-- This setup means:
-- 1. invoice_date = automatically set to current timestamp when invoice is created
-- 2. paid_at = automatically set to current timestamp (simulates immediate payment)
-- 3. All new invoices will appear as "paid" immediately
-- 4. To test "Retry Payment" functionality, manually clear paid_at field in database

-- Verify the changes
\d invoices;
