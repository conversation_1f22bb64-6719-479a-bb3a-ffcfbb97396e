import { pgTable, text, serial, timestamp, varchar, integer, boolean, jsonb, primaryKey, uuid, time, numeric } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Define a Zod schema for multimedia playlist items
export const multimediaPlaylistItemSchema = z.object({
  id: z.string().uuid(),
  mediaId: z.string().uuid(),
  duration: z.number().int().positive().default(5), // Duration in seconds for images
  sortOrder: z.number().int().min(0).default(0),
});

export const profiles = pgTable("profiles", {
  id: text("id").primaryKey().notNull(),
  email: text("email").notNull().unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  avatarUrl: text("avatar_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const teams = pgTable("teams", {
  id: uuid("id").defaultRandom().primaryKey(),
  name: text("name").notNull(),
  logoUrl: text("logo_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const teamMembers = pgTable("team_members", {
  profileId: text("profile_id").notNull().references(() => profiles.id),
  teamId: uuid("team_id").notNull().references(() => teams.id),
  role: text("role").notNull().default("member"),
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey({columns: [t.profileId, t.teamId]})
}));

export const mediaItems = pgTable("media_items", {
  id: uuid("id").defaultRandom().primaryKey(),
  teamId: uuid("team_id").references(() => teams.id),
  name: text("name").notNull(),
  description: text("description"),
  fileUrl: text("file_url").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  fileType: text("file_type").notNull(),
  fileSize: integer("file_size"),
  width: integer("width"),
  height: integer("height"),
  duration: integer("duration"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const screens = pgTable("screens", {
  id: uuid("id").defaultRandom().primaryKey(),
  teamId: uuid("team_id").references(() => teams.id),
  name: text("name").notNull(),
  location: text("location"),
  code: text("code").notNull(),
  status: text("status").default("offline"),
  lastPingAt: timestamp("last_ping_at"),
  startTime: time("start_time"),
  endTime: time("end_time"),
  updateFrequency: text("update_frequency"),
  siteEmail: text("site_email"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  isRegistered: boolean("is_registered").default(false),
  updateguid: uuid("updateguid"),
  health: jsonb("health"),
  isDeleted: boolean("is_deleted").default(false),
});

export const screenActivities = pgTable("screen_activities", {
  id: integer("id").primaryKey(), // bigint in DB, but using integer for compatibility
  screenId: uuid("screen_id").references(() => screens.id),
  logDetails: text("log_details"),
  logDatetime: timestamp("log_datetime"),
  fileDownloadCount: integer("file_download_count"),
  totalFileDownload: integer("total_file_download"),
});

export const screenRegistrations = pgTable("screen_registrations", {
  id: integer("id").primaryKey(), // bigint in DB, but using integer for compatibility
  registeredAt: timestamp("registered_at").notNull(),
  screenId: uuid("screen_id").references(() => screens.id),
  trialEndsAt: timestamp("trial_ends_at"),
  subscriptionStatus: text("subscription_status").default("trial"),
  lemonsqueezySubscriptionId: text("lemonsqueezy_subscription_id"),
  billingCycle: text("billing_cycle"),
  invoiceId: integer("invoice_id"),
  isDeleted: boolean("is_deleted").default(false),
});

export const invoices = pgTable("invoices", {
  invoiceId: integer("invoice_id").primaryKey(), // bigint in DB, but using integer for compatibility
  teamId: uuid("team_id").references(() => teams.id),
  invoiceDate: timestamp("invoice_date").defaultNow(), // Auto-set to current timestamp
  billingCycle: text("billing_cycle"),
  qty: integer("qty"),
  totalAmount: numeric("total_amount"), // Using numeric to match DB schema
  externalSubscriptionId: text("external_subscription_id"),
  paidAt: timestamp("paid_at").defaultNow(), // Auto-set to current timestamp (immediate payment simulation)
  isDeleted: boolean("is_deleted").default(false), // Now properly boolean type
});

export const pricing = pgTable("pricing", {
  id: integer("id").primaryKey(), // bigint in DB, but using integer for compatibility
  billingCycleText: text("billing_cycle_text"),
  billingCycleValue: text("billing_cycle_value"),
  price: numeric("price"), // Using numeric to match DB schema (stores dollar amounts)
});

export const campaigns = pgTable("campaigns", {
  id: uuid("id").defaultRandom().primaryKey(),
  teamId: uuid("team_id").references(() => teams.id),
  name: text("name").notNull(),
  description: text("description"),
  startDate: timestamp("start_date"),
  endDate: timestamp("end_date"),
  status: text("status").default("draft"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const campaignMedias = pgTable("campaign_medias", {
  campaignId: uuid("campaign_id").notNull().references(() => campaigns.id),
  mediaId: uuid("media_id").notNull(), // Remove reference to mediaItems
  displayOrder: integer("display_order").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  campaignType: integer("campaign_type").default(0), // 0 for media, 1 for slides
}, (t) => ({
  pk: primaryKey({columns: [t.campaignId, t.mediaId]})
}));

export const campaignScreens = pgTable("campaign_screens", {
  campaignId: uuid("campaign_id").notNull().references(() => campaigns.id),
  screenId: uuid("screen_id").notNull().references(() => screens.id),
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey({columns: [t.campaignId, t.screenId]})
}));

export const slides = pgTable("slides", {
  id: uuid("id").defaultRandom().primaryKey(),
  teamId: uuid("team_id").references(() => teams.id),
  name: text("name").notNull(),
  content: jsonb("content").notNull(),
  thumbnailUrl: text("thumbnail_url"),
  slideWidth: integer("slide_width"),
  slideHeight: integer("slide_height"),
  slideBackgroundColor: text("slide_background_color"),
  apiUrl: text("api_url"),
  apiDataPreviewDuration: integer("api_data_preview_duration"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
  isDeleted: boolean("isdeleted").default(false),
});

export const campaignSlides = pgTable("campaign_slides", {
  campaignId: uuid("campaign_id").notNull().references(() => campaigns.id),
  slideId: uuid("slide_id").notNull().references(() => slides.id),
  displayOrder: integer("display_order").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey({columns: [t.campaignId, t.slideId]})
}));

export const tags = pgTable("tags", {
  id: uuid("id").defaultRandom().primaryKey(),
  teamId: uuid("team_id").references(() => teams.id),
  name: text("name").notNull(),
  color: text("color"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const mediaTags = pgTable("media_tags", {
  mediaId: uuid("media_id").notNull().references(() => mediaItems.id),
  tagId: uuid("tag_id").notNull().references(() => tags.id),
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey({columns: [t.mediaId, t.tagId]})
}));

export const screenTags = pgTable("screen_tags", {
  screenId: uuid("screen_id").notNull().references(() => screens.id),
  tagId: uuid("tag_id").notNull().references(() => tags.id),
  createdAt: timestamp("created_at").defaultNow(),
}, (t) => ({
  pk: primaryKey({columns: [t.screenId, t.tagId]})
}));

// Insert schemas
export const insertProfileSchema = createInsertSchema(profiles).omit({ id: true });
export const insertTeamSchema = createInsertSchema(teams).omit({ id: true });
export const insertTeamMemberSchema = createInsertSchema(teamMembers);
export const insertMediaItemSchema = createInsertSchema(mediaItems).omit({ id: true });
export const insertScreenSchema = createInsertSchema(screens).omit({ id: true });
// Custom campaign schema with string-to-date conversion for date fields
export const insertCampaignSchema = createInsertSchema(campaigns)
  .omit({ id: true })
  .extend({
    startDate: z.string().datetime().transform(val => new Date(val)).or(z.date()),
    endDate: z.string().datetime().transform(val => new Date(val)).or(z.date()),
  });
export const insertCampaignMediaSchema = createInsertSchema(campaignMedias)
  .extend({
    campaignType: z.number().default(0), // 0 for media, 1 for slide
  });
export const insertCampaignSlideSchema = createInsertSchema(campaignSlides);
export const insertCampaignScreenSchema = createInsertSchema(campaignScreens);
export const insertSlideSchema = createInsertSchema(slides).omit({ id: true });
export const insertTagSchema = createInsertSchema(tags).omit({ id: true });
export const insertMediaTagSchema = createInsertSchema(mediaTags);
export const insertScreenTagSchema = createInsertSchema(screenTags);
export const insertScreenRegistrationSchema = createInsertSchema(screenRegistrations).omit({ id: true });
// Custom invoice schema - completely manual to avoid createInsertSchema issues
export const insertInvoiceSchema = z.object({
  teamId: z.string().uuid(),
  invoiceDate: z.string().optional(),
  billingCycle: z.string(),
  qty: z.number().int(),
  totalAmount: z.number(),
  externalSubscriptionId: z.string(),
  paidAt: z.string().optional(),
  isDeleted: z.boolean().optional(),
});
export const insertPricingSchema = createInsertSchema(pricing).omit({ id: true });
export const insertScreenActivitySchema = createInsertSchema(screenActivities).omit({ id: true });

// Types
export type Profile = typeof profiles.$inferSelect;
export type InsertProfile = z.infer<typeof insertProfileSchema>;

export type Team = typeof teams.$inferSelect;
export type InsertTeam = z.infer<typeof insertTeamSchema>;

export type TeamMember = typeof teamMembers.$inferSelect;
export type InsertTeamMember = z.infer<typeof insertTeamMemberSchema>;

export type MediaItem = typeof mediaItems.$inferSelect;
export type InsertMediaItem = z.infer<typeof insertMediaItemSchema>;

export type Screen = typeof screens.$inferSelect;
export type InsertScreen = z.infer<typeof insertScreenSchema>;

export type Campaign = typeof campaigns.$inferSelect & {
  screenCount?: number;
  mediaCount?: number;
  slideCount?: number;
};
export type InsertCampaign = z.infer<typeof insertCampaignSchema>;

export type CampaignMedia = typeof campaignMedias.$inferSelect;
export type InsertCampaignMedia = z.infer<typeof insertCampaignMediaSchema>;

export type CampaignScreen = typeof campaignScreens.$inferSelect;
export type InsertCampaignScreen = z.infer<typeof insertCampaignScreenSchema>;

export type CampaignSlide = typeof campaignSlides.$inferSelect;
export type InsertCampaignSlide = z.infer<typeof insertCampaignSlideSchema>;

export type Slide = typeof slides.$inferSelect;
export type InsertSlide = z.infer<typeof insertSlideSchema>;

export type Tag = typeof tags.$inferSelect;
export type InsertTag = z.infer<typeof insertTagSchema>;

export type MediaTag = typeof mediaTags.$inferSelect;
export type InsertMediaTag = z.infer<typeof insertMediaTagSchema>;

export type ScreenTag = typeof screenTags.$inferSelect;
export type InsertScreenTag = z.infer<typeof insertScreenTagSchema>;

export type ScreenActivity = typeof screenActivities.$inferSelect;
export type InsertScreenActivity = z.infer<typeof insertScreenActivitySchema>;

export type ScreenRegistration = typeof screenRegistrations.$inferSelect;
export type InsertScreenRegistration = z.infer<typeof insertScreenRegistrationSchema>;

export type Invoice = typeof invoices.$inferSelect & {
  creditUsed?: number; // Count of screen registrations using this invoice
};
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;

export type Pricing = typeof pricing.$inferSelect;
export type InsertPricing = z.infer<typeof insertPricingSchema>;