import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export interface Tag {
  id: string;
  name: string;
  color: string;
  teamId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TagWithAssociations extends Tag {
  mediaIds?: string[];
  screenIds?: string[];
}

export function useTags(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get all tags for the team - only fetch when needed
  const tagsQuery = useQuery<Tag[]>({
    queryKey: [`/api/teams/${teamId}/tags`],
    enabled: !!teamId,
    staleTime: 0, // Always fresh for real-time updates
    refetchOnWindowFocus: false,
  });

  // Create a new tag
  const createTagMutation = useMutation({
    mutationFn: async (tagData: { name: string; color: string; teamId: string }) => {
      const response = await apiRequest("POST", "/api/tags", tagData);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/tags`] });
      toast({
        title: "Success",
        description: "Tag created successfully",
      });
    },
    onError: (error) => {
      console.error("Failed to create tag:", error);
      toast({
        title: "Error",
        description: "Failed to create tag",
        variant: "destructive",
      });
    },
  });

  // Update a tag
  const updateTagMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<Tag> }) => {
      const response = await apiRequest("PATCH", `/api/tags/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/tags`] });
      toast({
        title: "Success",
        description: "Tag updated successfully",
      });
    },
    onError: (error) => {
      console.error("Failed to update tag:", error);
      toast({
        title: "Error",
        description: "Failed to update tag",
        variant: "destructive",
      });
    },
  });

  // Delete a tag
  const deleteTagMutation = useMutation({
    mutationFn: async (id: string) => {
      await apiRequest("DELETE", `/api/tags/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/tags`] });
      toast({
        title: "Success",
        description: "Tag deleted successfully",
      });
    },
    onError: (error) => {
      console.error("Failed to delete tag:", error);
      toast({
        title: "Error",
        description: "Failed to delete tag",
        variant: "destructive",
      });
    },
  });

  return {
    tags: tagsQuery.data || [],
    isLoading: tagsQuery.isLoading,
    error: tagsQuery.error,
    createTag: createTagMutation.mutateAsync,
    updateTag: updateTagMutation.mutateAsync,
    deleteTag: deleteTagMutation.mutateAsync,
    refetchTags: tagsQuery.refetch,
  };
}

// Hook for getting tags with their associations (media/screen counts) - OPTIMIZED
export function useTagsWithAssociations(teamId: string, enabled: boolean = false) {
  const queryClient = useQueryClient();

  return useQuery<TagWithAssociations[]>({
    queryKey: [`/api/teams/${teamId}/tags-with-associations`],
    queryFn: async () => {
      if (!teamId) return [];

      // Use the new optimized endpoint that fetches tags with associations in a single SQL query
      const response = await apiRequest("GET", `/api/teams/${teamId}/tags-with-associations`);
      const tagsWithAssociations = await response.json();

      if (!Array.isArray(tagsWithAssociations)) {
        return [];
      }

      return tagsWithAssociations;
    },
    enabled: !!teamId && enabled,
    staleTime: 0, // Always fresh
    refetchOnWindowFocus: false,
  });
}

// Utility function to generate random colors for new tags
export function generateRandomColor(): string {
  const colors = [
    "#ef4444", "#f97316", "#f59e0b", "#eab308", "#84cc16",
    "#22c55e", "#10b981", "#14b8a6", "#06b6d4", "#0ea5e9",
    "#3b82f6", "#6366f1", "#8b5cf6", "#a855f7", "#d946ef",
    "#ec4899", "#f43f5e"
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}
