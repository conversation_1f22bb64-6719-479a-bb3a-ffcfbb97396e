{"event_message": "PATCH | 200 | ************* | 9431461aab34bcb6 | https://oatyudgnhndxrwwlbxsj.supabase.co/rest/v1/screens?id=eq.f4feb5dc-8546-4ac6-8e07-3ae62cdb6515&select=* | node", "id": "8c7f024e-769f-437e-b83d-c1407e42560c", "metadata": [{"load_balancer_experimental_routing": null, "load_balancer_geo_aware_info": [], "load_balancer_redirect_identifier": null, "logflare_worker": [{"worker_id": "R4L6E1"}], "request": [{"cf": [{"asOrganization": "Google Cloud", "asn": 396982, "botManagement": [{"corporateProxy": false, "detectionIds": [], "ja3Hash": null, "jsDetection": [{"passed": false}], "score": 99, "staticResource": false, "verifiedBot": false}], "city": "North Charleston", "clientAcceptEncoding": "br, gzip, deflate", "clientTcpRtt": 11, "clientTrustScore": null, "colo": "ATL", "continent": "NA", "country": "US", "edgeRequestKeepAliveStatus": 1, "httpProtocol": "HTTP/1.1", "isEUCountry": null, "latitude": "32.86080", "longitude": "-79.97460", "metroCode": "519", "postalCode": "29415", "region": "South Carolina", "regionCode": "SC", "requestPriority": null, "timezone": "America/New_York", "tlsCipher": "AEAD-AES256-GCM-SHA384", "tlsClientAuth": [{"certPresented": "0", "certRevoked": "0", "certVerified": "NONE"}], "tlsClientExtensionsSha1": "GWeb1cCR2UBICwtIDbeP9YjL/PU=", "tlsClientHelloLength": "661", "tlsClientRandom": "I3qrpbFpiWDXpNz3Ecs+BMFMJTP5b5UtzrlpfC9Aw7c=", "tlsExportedAuthenticator": [{"clientFinished": "02c69e32ec3f22f2ab553b62146f8d392698e2bffa6d2003f239d09892d3ccbf6ec7b30cb64dbcc908c740f472fc7287", "clientHandshake": "5fb86e985ac21bfefd83621c8e05d0b3bb716e21f94f17461d910816f5097c204861885ea8ae261f4a3becb678108b77", "serverFinished": "9a7ccb2f8353ec2e2d76bfccde61ef30af30ea5d108c9b543703f3b29a4ffb184adcd8ea9fca66dca0e155e6944834a9", "serverHandshake": "8eebfc32e3c2e12e46ce358679521f46e16e3f21405e2af21a0f54c6af0195e509004a24987883417f2ee12353b35626"}], "tlsVersion": "TLSv1.3", "verifiedBotCategory": null}], "headers": [{"accept": "application/vnd.pgrst.object+json", "cf_cache_status": null, "cf_connecting_ip": "*************", "cf_ipcountry": "US", "cf_ray": "9431461aab34bcb6", "content_length": "153", "content_location": null, "content_range": null, "content_type": "application/json", "date": null, "host": "oatyudgnhndxrwwlbxsj.supabase.co", "prefer": "return=representation", "range": null, "referer": null, "sb_gateway_mode": null, "sb_gateway_version": null, "user_agent": "node", "x_client_info": "supabase-js-node/2.49.4", "x_forwarded_host": null, "x_forwarded_proto": "https", "x_forwarded_user_agent": null, "x_kong_proxy_latency": null, "x_kong_upstream_latency": null, "x_real_ip": "*************"}], "host": "oatyudgnhndxrwwlbxsj.supabase.co", "method": "PATCH", "path": "/rest/v1/screens", "port": null, "protocol": "https:", "sb": [{"apikey": [], "auth_user": null, "jwt": [{"apikey": [{"invalid": null, "payload": [{"algorithm": "HS256", "expires_at": 2060381000, "issuer": "supabase", "role": "anon", "signature_prefix": "FLeeCa", "subject": null}]}], "authorization": [{"invalid": null, "payload": [{"algorithm": "HS256", "expires_at": 2060381000, "issuer": "supabase", "key_id": null, "role": "anon", "session_id": null, "signature_prefix": "FLeeCa", "subject": null}]}]}]}], "search": "?id=eq.f4feb5dc-8546-4ac6-8e07-3ae62cdb6515&select=*", "url": "https://oatyudgnhndxrwwlbxsj.supabase.co/rest/v1/screens?id=eq.f4feb5dc-8546-4ac6-8e07-3ae62cdb6515&select=*"}], "response": [{"headers": [{"cf_cache_status": "DYNAMIC", "cf_ray": "9431461ab618bcb6-ATL", "content_length": null, "content_location": null, "content_range": "0-0/*", "content_type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Wed, 21 May 2025 04:19:08 GMT", "sb_gateway_mode": null, "sb_gateway_version": "1", "transfer_encoding": "chunked", "x_kong_proxy_latency": null, "x_kong_upstream_latency": null, "x_sb_error_code": null}], "origin_time": 230, "status_code": 200}]}], "timestamp": 1747801148821000}