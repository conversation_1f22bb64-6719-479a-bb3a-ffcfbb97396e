<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>AdLoopr - Digital Signage CMS</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzA1REFDMyIgZD0iTTMgM0gyMVYyMUgzVjNaTTUgNVYxOUgxOVY1SDVaTTcgN0gxN1YxN0g3VjdaTTkgOVYxNUgxNVY5SDlaIi8+PC9zdmc+" />
  </head>
  <body>
    <div id="root"></div>
    <!-- URL Fixer Script to suppress Next.js router errors in Replit environment -->
    <script>
      // Handle URL issues in Next.js router
      window.addEventListener('DOMContentLoaded', function() {
        // Track error messages to avoid duplicates
        const errorsSeen = new Set();
        
        // Override console.error to catch Next.js router errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
          // Check if the error is a Next.js router invalid href error
          if (args[0] && typeof args[0] === 'string' && args[0].includes('Invalid href')) {
            // Create a key for this error to avoid showing it repeatedly
            const errorKey = args[0];
            if (!errorsSeen.has(errorKey)) {
              errorsSeen.add(errorKey);
              // Log a simplified message once
              console.warn('URL warning suppressed: Invalid href detected. This does not affect application functionality.');
            }
            return; // Suppress the error
          }
          
          // Not a router error, pass through to original console.error
          originalConsoleError.apply(console, args);
        };
      });
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
    <!-- Replit dev banner disabled to avoid URL conflicts -->
    <!-- <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script> -->
  </body>
</html>
