-- Create the sessions table for managing user sessions
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  user_agent TEXT,
  ip_address INET,
  device_info JSONB DEFAULT '{}'::JSONB,
  is_valid BOOLEAN DEFAULT TRUE,
  refresh_token TEXT,
  token_hash TEXT
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash);

-- Create RLS policies for the sessions table
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- Users can only see their own sessions
CREATE POLICY user_sessions_select_policy 
  ON user_sessions 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Users can only insert their own sessions
CREATE POLICY user_sessions_insert_policy 
  ON user_sessions 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Users can only update their own sessions
CREATE POLICY user_sessions_update_policy 
  ON user_sessions 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Users can only delete their own sessions
CREATE POLICY user_sessions_delete_policy 
  ON user_sessions 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Create function to handle session update
CREATE OR REPLACE FUNCTION update_session_last_active()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  NEW.last_active_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update session activity timestamps
CREATE TRIGGER update_user_sessions_last_active
  BEFORE UPDATE ON user_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_session_last_active();

-- Create function to handle session cleanup (expired sessions)
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM user_sessions 
  WHERE expires_at < now();
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create scheduled function to clean up expired sessions daily
CREATE EXTENSION IF NOT EXISTS pg_cron;

SELECT cron.schedule(
  'cleanup-expired-sessions',  -- name of the schedule
  '0 0 * * *',                -- run at midnight every day (cron expression)
  $$DELETE FROM user_sessions WHERE expires_at < now()$$
);

-- Add sign_out_session function to invalidate a specific session
CREATE OR REPLACE FUNCTION sign_out_session(session_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE user_sessions
  SET is_valid = FALSE
  WHERE id = session_id AND user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add sign_out_all_sessions function to invalidate all sessions for a user
CREATE OR REPLACE FUNCTION sign_out_all_sessions()
RETURNS VOID AS $$
BEGIN
  UPDATE user_sessions
  SET is_valid = FALSE
  WHERE user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add function to create a new session when a user signs in
CREATE OR REPLACE FUNCTION create_user_session(
  p_user_agent TEXT,
  p_ip_address INET,
  p_device_info JSONB,
  p_refresh_token TEXT,
  p_expires_in INTEGER -- seconds until expiration
)
RETURNS UUID AS $$
DECLARE
  v_session_id UUID;
BEGIN
  INSERT INTO user_sessions(
    user_id,
    user_agent,
    ip_address,
    device_info,
    refresh_token,
    token_hash,
    expires_at
  ) 
  VALUES (
    auth.uid(),
    p_user_agent,
    p_ip_address,
    p_device_info,
    p_refresh_token,
    encode(digest(p_refresh_token, 'sha256'), 'hex'),
    now() + (p_expires_in || ' seconds')::interval
  )
  RETURNING id INTO v_session_id;
  
  RETURN v_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;