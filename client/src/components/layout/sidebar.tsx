import { useState, useEffect, useRef } from "react";
import { useLocation, Link } from "wouter";
import { cn } from "@/lib/utils";
import { useAuth } from "@/hooks/use-auth";
import { useIsMobile as useMobile } from "@/hooks/use-mobile";
import { useSidebar } from "@/contexts/sidebar-context";

// Import logo assets
import appLogo from "../../assets/app.png";
import logoFull from "../../assets/Final-adloopr-V2-light.png";
import logoBoldDark from "../../assets/Final-adloopr-V2-bold-dark.png";

import {
  LayoutDashboard,
  Image,
  Monitor,
  Presentation,
  Paintbrush,
  BarChart2,
  Settings,
  Tag,
  LogOut,
  ChevronLeft,
  ChevronRight,
  Menu,
} from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetDes<PERSON> } from "@/components/ui/sheet";
import { VisuallyHidden } from "@/components/ui/visually-hidden";

interface SidebarProps {
  className?: string;
}

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
  onClick?: () => void;
}

const NavItem = ({ href, icon, label, isActive, onClick }: NavItemProps) => {
  // Clean href to prevent double slashes
  const cleanHref = href.startsWith('/') ? href : `/${href}`;

  return (
    <Link
      href={cleanHref}
      className={cn(
        "flex items-center p-2 rounded-md transition-colors mb-1",
        isActive
          ? "bg-[#05DAC3] text-[#020626]"
          : "text-[#05DAC3] hover:bg-[#05DAC3] hover:text-[#020626]"
      )}
      onClick={onClick}
    >
      <span className="mr-3 text-lg">{icon}</span>
      <span>{label}</span>
    </Link>
  );
};

export function Sidebar({ className }: SidebarProps) {
  const [location] = useLocation();
  const { user, signOut } = useAuth();
  const isMobile = useMobile();
  const { isCollapsed, setIsCollapsed, toggleCollapse, isMobileOpen, setIsMobileOpen } = useSidebar();

  // Function to handle navigation clicks
  const handleNavClick = () => {
    if (isMobile && isMobileOpen) {
      console.log('Closing mobile sidebar due to navigation click');
      setIsMobileOpen(false);
    }
  };

  const sidebarContent = (
    <>
      <div className="flex items-center justify-center p-4 border-b border-[#05DAC3]/20">
        <div className="flex items-center justify-center">
          {isCollapsed ? (
            <img
              src={appLogo}
              alt="AdLoopr Logo"
              className="h-8 w-8"
            />
          ) : (
            <img
              src={logoFull}
              alt="AdLoopr"
              className="h-8"
            />
          )}
        </div>
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileOpen(false)}
            className="lg:hidden text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3] ml-auto"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}
      </div>

      <nav className="p-4 flex-1 overflow-y-auto">
        <div className="space-y-6">
          <div>
            {!isCollapsed && (
              <p className="text-[#05DAC3] text-xs uppercase font-medium mb-3">
                Main
              </p>
            )}
            <ul>
              <li>
                <NavItem
                  href="/dashboard"
                  icon={<LayoutDashboard size={18} />}
                  label={isCollapsed ? "" : "Dashboard"}
                  isActive={location === "/dashboard"}
                  onClick={handleNavClick}
                />
              </li>
              <li>
                <NavItem
                  href="/media"
                  icon={<Image size={18} />}
                  label={isCollapsed ? "" : "Media"}
                  isActive={location === "/media"}
                  onClick={handleNavClick}
                />
              </li>
              <li>
                <NavItem
                  href="/campaigns"
                  icon={<Presentation size={18} />}
                  label={isCollapsed ? "" : "Campaigns"}
                  isActive={location === "/campaigns"}
                  onClick={handleNavClick}
                />
              </li>
              <li>
                <NavItem
                  href="/designer"
                  icon={<Paintbrush size={18} />}
                  label={isCollapsed ? "" : "Slide Designer"}
                  isActive={location === "/designer"}
                  onClick={() => {
                    handleNavClick();
                    if (!isMobile && !isCollapsed) {
                      toggleCollapse();
                    }
                  }}
                />
              </li>
              <li>
                <NavItem
                  href="/screens"
                  icon={<Monitor size={18} />}
                  label={isCollapsed ? "" : "Screens"}
                  isActive={location === "/screens"}
                  onClick={handleNavClick}
                />
              </li>
              <li>
                <NavItem
                  href="/reports"
                  icon={<BarChart2 size={18} />}
                  label={isCollapsed ? "" : "Reports"}
                  isActive={location === "/reports"}
                  onClick={handleNavClick}
                />
              </li>
            </ul>
          </div>

          <div>
            {!isCollapsed && (
              <p className="text-[#05DAC3] text-xs uppercase font-medium mb-3">
                Admin
              </p>
            )}
            <ul>
              <li>
                <NavItem
                  href="/settings"
                  icon={<Settings size={18} />}
                  label={isCollapsed ? "" : "Settings"}
                  isActive={location === "/settings"}
                  onClick={handleNavClick}
                />
              </li>
              <li>
                <NavItem
                  href="/settings/tags"
                  icon={<Tag size={18} />}
                  label={isCollapsed ? "" : "Tags"}
                  isActive={location === "/settings/tags"}
                  onClick={handleNavClick}
                />
              </li>
            </ul>
          </div>
        </div>
      </nav>

      <div className="p-4 border-t border-[#05DAC3]/20 mt-auto">
        <div className="flex items-center">
          <Avatar className="h-8 w-8 border border-[#05DAC3]/30">
            <AvatarImage src={user?.avatarUrl} alt={user?.firstName} />
            <AvatarFallback className="bg-[#020626]/80 text-[#05DAC3]">
              {user?.firstName?.charAt(0)}
              {user?.lastName?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          {!isCollapsed && (
            <div className="ml-3 overflow-hidden">
              <p className="text-sm font-medium truncate text-[#05DAC3]">
                {user?.firstName} {user?.lastName}
              </p>
              <p className="text-xs text-[#05DAC3]/70 truncate">{user?.email}</p>
            </div>
          )}
          {!isCollapsed && (
            <Button
              variant="ghost"
              size="icon"
              className="ml-auto text-[#05DAC3] hover:text-[#020626] hover:bg-[#05DAC3]"
              onClick={signOut}
              title="Log out"
            >
              <LogOut className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </>
  );

  if (isMobile) {
    // On mobile, return only the Sheet overlay (takes no layout space)
    return (
      <Sheet open={isMobileOpen} onOpenChange={setIsMobileOpen}>
        <SheetContent
          side="left"
          className="p-0 w-[280px] bg-[#020626]"
        >
          <VisuallyHidden>
            <SheetHeader>
              <SheetTitle>Navigation Menu</SheetTitle>
              <SheetDescription>
                Main navigation menu for the application
              </SheetDescription>
            </SheetHeader>
          </VisuallyHidden>
          {sidebarContent}
        </SheetContent>
      </Sheet>
    );
  }

  // On desktop, return the normal sidebar
  return (
    <aside
      className={cn(
        "h-full bg-[#020626] text-[#05DAC3] z-10 transition-all duration-300",
        isCollapsed ? "w-[70px]" : "w-[280px]",
        className
      )}
    >
      <div className="flex flex-col h-full">
        {sidebarContent}
      </div>
    </aside>
  );
}
