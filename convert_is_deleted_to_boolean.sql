-- Convert is_deleted field from bit to boolean in invoices table
-- This will make it consistent with other tables and easier to work with

-- Step 1: Add a temporary boolean column
ALTER TABLE invoices ADD COLUMN is_deleted_temp BOOLEAN DEFAULT FALSE;

-- Step 2: Copy data from bit column to boolean column
-- Convert '1' to TRUE and '0' to FALSE
UPDATE invoices 
SET is_deleted_temp = CASE 
    WHEN is_deleted = B'1' THEN TRUE 
    ELSE FALSE 
END;

-- Step 3: Drop the old bit column
ALTER TABLE invoices DROP COLUMN is_deleted;

-- Step 4: Rename the temporary column to the original name
ALTER TABLE invoices RENAME COLUMN is_deleted_temp TO is_deleted;

-- Verify the changes
\d invoices;

-- Check the data
SELECT invoice_id, is_deleted FROM invoices LIMIT 5;
