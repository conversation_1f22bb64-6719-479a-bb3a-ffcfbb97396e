import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface ActivityItemProps {
  icon: LucideIcon;
  iconBgColor: string;
  iconColor: string;
  message: React.ReactNode;
  timestamp: string;
}

export function ActivityItem({
  icon: Icon,
  iconBgColor,
  iconColor,
  message,
  timestamp,
}: ActivityItemProps) {
  return (
    <div className="flex">
      <div className="flex-shrink-0 mr-3">
        <div 
          className={cn(
            "w-8 h-8 rounded-full flex items-center justify-center",
            iconBgColor
          )}
        >
          <Icon className={cn("h-4 w-4", iconColor)} />
        </div>
      </div>
      <div>
        <p className="text-sm">{message}</p>
        <p className="text-xs text-muted-foreground">{timestamp}</p>
      </div>
    </div>
  );
}
