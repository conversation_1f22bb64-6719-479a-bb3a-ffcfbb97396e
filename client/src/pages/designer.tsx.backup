import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { Canvas, CanvasElement } from "@/components/designer/canvas";
import { Toolbox } from "@/components/designer/toolbox";
import { ElementProperties } from "@/components/designer/element-properties";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SaveIcon, Trash, ImagePlus, Settings, ArrowUpToLine, ArrowDownToLine, Play, Square } from "lucide-react";
import { ColorPicker } from "@/components/ui/color-picker";
import ErrorBoundary from "@/components/ui/error-boundary";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const slideSchema = z.object({
  name: z.string().min(1, "Name is required"),
  width: z.number().min(200).max(3840),
  height: z.number().min(200).max(2160),
  backgroundColor: z.string().optional(),
  apiUrl: z.string().optional(),
  apiDataPreviewDuration: z.number().optional(),
});

// Add global typings for the window object
declare global {
  interface Window {
    __API_DATA__: any;
    __API_DATA_FIELDS__?: string[]; // Made optional to match
    __API_DATA_INDEX__?: number; // Add this as optional to avoid type errors
  }
}

type SlideFormValues = z.infer<typeof slideSchema>;

export default function Designer() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [teamId, setTeamId] = useState<string>("");
  const [elements, setElements] = useState<CanvasElement[]>([]);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(true);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentSlideId, setCurrentSlideId] = useState<string | null>(null);
  const [currentSlideName, setCurrentSlideName] = useState<string>("");
  const [canvasSize, setCanvasSize] = useState({ width: 1920, height: 1080 });
  const [canvasBackground, setCanvasBackground] = useState("#ffffff");
  const [apiUrl, setApiUrl] = useState("");
  const [apiPreviewDuration, setApiPreviewDuration] = useState(10);
  const [isSaving, setIsSaving] = useState(false);
  const [isApiLoading, setIsApiLoading] = useState(false);
  const [apiData, setApiData] = useState<any>(null);
  const apiPreviewTimeoutRef = useRef<number | null>(null);
  
  // Flag to prevent unnecessary API fetches when stopping preview
  const apiPreviewStoppingRef = useRef<boolean>(false);
  
  // Preview functionality
  const [isPreviewActive, setIsPreviewActive] = useState(false);
  const previewIntervalRef = useRef<number | null>(null);
  const videoElementRef = useRef<HTMLVideoElement | null>(null);
  
  // Track whether we're in the process of cycling API data to prevent cleanup
  const isApiCyclingRef = useRef<boolean>(false);
  
  const [currentPreviewElements, setCurrentPreviewElements] = useState<{
    type: 'multimedia' | 'playlist',
    elementId: string,
    currentIndex: number
  }[]>([]);
  
  // Reference to keep track of current API data index during preview
  const currentApiDataIndexRef = useRef<number>(0);

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });
  
  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);
  
  // Store references to any timers for image-based playlist items
  const imageTimersRef = useRef<{[key: string]: number}>({});
  
  // Store a reference to the video ended event handler for cleanup
  const videoEndedHandlerRef = useRef<((event: Event) => void) | null>(null);
  
  // Effect to handle preview mode event listeners
  useEffect(() => {
    if (!isPreviewActive) {
      // When preview is deactivated, clean up all timers and event handlers
      cleanupPreviewEventListeners();
      return;
    }
    
    // Clear any existing timers from previous previews
    cleanupPreviewEventListeners();
    
    // Find all video elements in the preview
    const videoElements = document.querySelectorAll('video');
    
    // Force play all videos
    videoElements.forEach(video => {
      // Set necessary attributes for autoplay
      video.muted = true;
      video.loop = false; // We'll handle looping ourselves
      video.preload = 'auto';
      video.autoplay = true;
      
      // Force play the video
      const playPromise = video.play();
      
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.error("Error playing video during preview:", error);
          // Try again after a short delay
          setTimeout(() => {
            video.play().catch(e => console.error("Second attempt to play video failed:", e));
          }, 500);
        });
      }
    });
    
    console.log(`Preview active: Found ${videoElements.length} video elements to play`);
    
    // Create the video ended handler
    const handleVideoEnded = (event: Event) => {
      const videoElement = event.target as HTMLVideoElement;
      console.log("Video ended event fired", videoElement);
      
      // Check if we're still in preview mode
      if (!isPreviewActive) {
        console.log("Video ended but preview is no longer active, ignoring");
        return;
      }
      
      const elementWrapper = videoElement.closest('[data-element-id]');
      if (!elementWrapper) {
        console.log("No element wrapper found for video");
        return;
      }
      
      const elementId = elementWrapper.getAttribute('data-element-id');
      if (!elementId) {
        console.log("No element ID found for video");
        return;
      }
      
      // Find which preview element this is
      const previewElement = currentPreviewElements.find(el => el.elementId === elementId);
      if (!previewElement) {
        console.log("No preview element found with ID:", elementId);
        return;
      }
      
      const element = elements.find(el => el.id === elementId);
      if (!element) {
        console.log("No element found with ID:", elementId);
        return;
      }
      
      console.log("Video ended in element type:", element.type);
      
      if (element.type === 'playlist') {
        const playlist = element.content?.playlist || [];
        if (playlist.length === 0) return;
        
        // Move to the next item in the playlist
        const currentIndex = previewElement.currentIndex;
        const nextIndex = (currentIndex + 1) % playlist.length;
        
        console.log(`Playlist: Advancing from item ${currentIndex} to ${nextIndex}`);
        updatePlaylistElement(elementId, nextIndex);
      } else if (element.type === 'multimedia') {
        if (!apiData) return;
        
        // Check if this element has any API mapping configured
        const hasApiMapping = element.content && 
                            element.content.apiMapping && 
                            Object.keys(element.content.apiMapping).length > 0;
        
        if (!hasApiMapping) {
          console.log("Multimedia element doesn't have API mapping, not cycling API data");
          return;
        }
        
        // Check if API has multiple records
        const apiHasMultipleRecords = checkIfApiHasMultipleRecords();
        if (!apiHasMultipleRecords || apiPreviewDuration <= 0) return;
        
        // Get the appropriate data array
        let dataArray = apiData;
        if (!Array.isArray(apiData)) {
          // Find the first array in the API data
          for (const key in apiData) {
            if (Array.isArray(apiData[key]) && apiData[key].length > 1) {
              dataArray = apiData[key];
              break;
            }
          }
        }
        
        if (!Array.isArray(dataArray) || dataArray.length <= 1) return;
        
        // Move to the next record in the API data
        const currentIndex = previewElement.currentIndex;
        const nextIndex = (currentIndex + 1) % dataArray.length;
        
        console.log(`Multimedia: Advancing from item ${currentIndex} to ${nextIndex}`);
        updateMultimediaElement(elementId, nextIndex);
      }
    };
    
    // Save reference to the event handler for cleanup
    videoEndedHandlerRef.current = handleVideoEnded;
    
    // Attach ended event listener to all video elements
    videoElements.forEach(video => {
      video.addEventListener('ended', handleVideoEnded);
    });
    
    // Set up timer for image-based playlist items
    const playlistElements = elements.filter(el => el.type === 'playlist');
    playlistElements.forEach(element => {
      const playlist = element.content?.playlist || [];
      if (playlist.length === 0) return;
      
      const currentIndex = element.content?.currentIndex || 0;
      const currentItem = playlist[currentIndex];
      
      // If this is an image, set a timer to move to the next item
      if (currentItem && currentItem.fileType?.startsWith('image/')) {
        const duration = currentItem.duration || 5; // Default to 5 seconds if not specified
        const timerId = window.setTimeout(() => {
          if (!isPreviewActive) return; // Don't continue if preview was stopped
          
          const nextIndex = (currentIndex + 1) % playlist.length;
          updatePlaylistElement(element.id, nextIndex);
        }, duration * 1000);
        
        // Store the timer ID for cleanup
        imageTimersRef.current[element.id] = timerId;
      }
    });
    
    // Handle API data cycling separately from multimedia elements
    setupApiDataPreview();
    
    // Cleanup function for effect
    return () => {
      cleanupPreviewEventListeners();
    };
  }, [isPreviewActive, currentPreviewElements, elements, apiData, apiPreviewDuration]);
  
  // Function to clean up all event listeners and timers
  const cleanupPreviewEventListeners = () => {
    console.log("Cleaning up all preview event listeners and timers");
    
    // Don't perform cleanup if we're in the middle of cycling API data
    if (isApiCyclingRef.current) {
      console.log("API data cycling in progress, skipping interval cleanup");
      return;
    }
    
    // Don't clear intervals if preview is still active - this prevents unwanted cleanup
    // during necessary timer operations
    if (isPreviewActive && apiData && apiPreviewDuration > 0) {
      console.log("Preview still active with API data, keeping intervals intact");
      return;
    }
    
    // Clear API data preview interval (for API data refresh)
    if (apiPreviewTimeoutRef.current) {
      window.clearInterval(apiPreviewTimeoutRef.current);
      apiPreviewTimeoutRef.current = null;
      console.log("Cleared API data preview interval");
    }
    
    // Clear API data cycling interval (for API data record cycling)
    if (previewIntervalRef.current) {
      console.log(`Clearing API cycling interval with ID: ${previewIntervalRef.current}`);
      window.clearInterval(previewIntervalRef.current);
      previewIntervalRef.current = null;
      console.log("Cleared API data cycling interval");
    } else {
      console.log("No API cycling interval found to clear");
    }
    
    // Clear all image timers
    Object.keys(imageTimersRef.current).forEach(elementId => {
      window.clearTimeout(imageTimersRef.current[elementId]);
      console.log(`Cleared image timer for element ${elementId}`);
    });
    imageTimersRef.current = {};
    
    // Remove event listeners from all videos
    if (videoEndedHandlerRef.current) {
      const videoElements = document.querySelectorAll('video');
      videoElements.forEach(video => {
        video.removeEventListener('ended', videoEndedHandlerRef.current!);
        console.log("Removed video ended event listener");
      });
      videoEndedHandlerRef.current = null;
    }
  };
  
  // Function to fetch API data
  const fetchApiData = async () => {
    // Reset the stopping flag when we intentionally fetch API data
    apiPreviewStoppingRef.current = false;
    
    if (!apiUrl) {
      setApiData(null);
      return;
    }
    
    try {
      setIsApiLoading(true);
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      setApiData(data);
      
      // Make the API data available to the multimedia elements
      window.__API_DATA__ = data;
      
      // Debug: Log the structure of the API data
      console.log('API Data Structure:', JSON.stringify(data, null, 2));
      
      // Extract data fields for dropdown selection with simplified handling for API response
      const extractDataFields = (obj: any, prefix = '') => {
        const fields: string[] = [];
        const processedKeys = new Set(); // To avoid duplicate keys
        
        // First check if the API response is an array of objects (common pattern)
        if (Array.isArray(obj) && obj.length > 0 && typeof obj[0] === 'object') {
          console.log("Processing array-based API response");
          
          // Just extract the keys from the first object in the array
          // These are the fields we'll offer for mapping
          const firstItem = obj[0];
          return Object.keys(firstItem);
        }
        
        if (!obj || typeof obj !== 'object') return fields;
        
        // Define a function to add a field if it's not already added
        const addField = (field: string) => {
          if (!processedKeys.has(field)) {
            fields.push(field);
            processedKeys.add(field);
          }
        };
        
        // Handle arrays - extract fields from the first item
        if (Array.isArray(obj)) {
          if (obj.length > 0) {
            // Skip adding the array itself as requested
            
            // Handle first array item
            if (obj[0] && typeof obj[0] === 'object') {
              // Extract fields from first array item and keep parent prefix
              Object.keys(obj[0]).forEach(key => {
                const fullKey = prefix ? `${prefix}.${key}` : key;
                addField(fullKey);
                
                // Recursively process nested objects
                if (obj[0][key] && typeof obj[0][key] === 'object') {
                  fields.push(...extractDataFields(obj[0][key], fullKey)
                    .filter(f => !processedKeys.has(f))
                  );
                }
              });
            }
          }
          return fields;
        }
        
        // Handle objects - extract each field and nested structure
        Object.keys(obj).forEach(key => {
          const fullKey = prefix ? `${prefix}.${key}` : key;
          
          // For object values, add the property itself
          if (!Array.isArray(obj[key])) {
            addField(fullKey);
          }
          
          // Recursively process nested objects and arrays
          if (obj[key] && typeof obj[key] === 'object') {
            if (Array.isArray(obj[key])) {
              // For arrays, don't add the array node itself but process children
              if (obj[key].length > 0) {
                // Process first array element as a representative
                if (typeof obj[key][0] === 'object') {
                  // Add fields from array element with parent prefix
                  Object.keys(obj[key][0]).forEach(arrayKey => {
                    const arrayFullKey = `${fullKey}.${arrayKey}`;
                    addField(arrayFullKey);
                    
                    // Recursively process deeper nested structures
                    if (obj[key][0][arrayKey] && typeof obj[key][0][arrayKey] === 'object') {
                      fields.push(...extractDataFields(obj[key][0][arrayKey], arrayFullKey)
                        .filter(f => !processedKeys.has(f))
                      );
                    }
                  });
                } else {
                  // For arrays of primitives, just add the parent key
                  addField(fullKey);
                }
              }
            } else {
              // For regular objects, recursively get all fields
              fields.push(...extractDataFields(obj[key], fullKey)
                .filter(f => !processedKeys.has(f))
              );
            }
          }
        });
        
        return fields;
      };
      
      // Store the extracted fields in the global object
      const extractedFields = extractDataFields(data);
      window.__API_DATA_FIELDS__ = extractedFields;
      
      // Debug: Log the extracted fields
      console.log('Extracted API data fields:', extractedFields);
      
      toast({
        title: "API Data Loaded",
        description: "Successfully loaded data from the API URL."
      });
      
    } catch (error) {
      console.error('Error fetching API data:', error);
      setApiData(null);
      window.__API_DATA__ = {};
      
      toast({
        title: "API Error",
        description: "Failed to load data from the provided API URL.",
        variant: "destructive"
      });
    } finally {
      setIsApiLoading(false);
    }
  };
  
  // Effect to handle API preview duration
  useEffect(() => {
    // Clear any existing interval
    if (apiPreviewTimeoutRef.current) {
      window.clearInterval(apiPreviewTimeoutRef.current);
      apiPreviewTimeoutRef.current = null;
    }
    
    // This is for initializing the API refresh system when the component mounts
    // or when the API URL or duration change
    // We only start the automatic refresh under specific conditions
    const shouldSetupAutoRefresh = apiUrl && 
                                   apiPreviewDuration > 0 && 
                                   !isPreviewActive &&
                                   // This additional check prevents API fetch when stopping preview
                                   !apiPreviewStoppingRef.current;
    
    if (shouldSetupAutoRefresh) {
      // Set up interval for refreshing data
      apiPreviewTimeoutRef.current = window.setInterval(() => {
        fetchApiData();
      }, apiPreviewDuration * 1000);
    }
    
    // Cleanup on unmount or when dependencies change
    return () => {
      if (apiPreviewTimeoutRef.current) {
        window.clearInterval(apiPreviewTimeoutRef.current);
        apiPreviewTimeoutRef.current = null;
      }
    };
  }, [apiUrl, apiPreviewDuration, isPreviewActive]);

  // Get slides for this team
  const { data: slides, isLoading, refetch } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/slides`],
    enabled: !!teamId,
  });

  // Form for creating a new slide
  const form = useForm<SlideFormValues>({
    resolver: zodResolver(slideSchema),
    defaultValues: {
      name: "Untitled Slide",
      width: 1920,
      height: 1080,
      backgroundColor: "#ffffff",
      apiUrl: "",
      apiDataPreviewDuration: 0,
    },
  });
  
  // Form for editing an existing slide
  const editForm = useForm<SlideFormValues>({
    resolver: zodResolver(slideSchema),
    defaultValues: {
      name: "",
      width: 1920,
      height: 1080,
      backgroundColor: "#ffffff",
      apiUrl: "",
      apiDataPreviewDuration: 0,
    },
  });

  // Helper to get a selected element
  const getSelectedElement = (): CanvasElement | null => {
    if (!selectedElementId) return null;
    return elements.find(el => el.id === selectedElementId) || null;
  };
  
  // Bring the selected element forward by incrementing z-index by 1
  const handleBringToFront = () => {
    if (!selectedElementId) return;
    
    // Get the selected element
    const selectedElement = elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;
    
    // Get the current z-index and increment by 1
    const currentZIndex = selectedElement.zIndex || 0;
    const newZIndex = currentZIndex + 1;
    
    // Update the selected element with new z-index
    handleUpdateElement(selectedElementId, { zIndex: newZIndex });
    
    // No toast alert
  };
  
  // Send the selected element backward by decrementing z-index by 1
  const handleSendToBack = () => {
    if (!selectedElementId) return;
    
    // Get the selected element
    const selectedElement = elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;
    
    // Get the current z-index and decrement by 1
    const currentZIndex = selectedElement.zIndex || 0;
    const newZIndex = currentZIndex - 1;
    
    // Update the selected element with new z-index
    handleUpdateElement(selectedElementId, { zIndex: newZIndex });
    
    // No toast alert
  };

  // Add a new element to the canvas
  const handleAddElement = (type: string, content: any = {}) => {
    // Find the highest z-index among existing elements (if any)
    const maxZIndex = elements.length > 0 
      ? Math.max(...elements.map(el => el.zIndex || 0)) 
      : 0;
    
    const newElement: CanvasElement = {
      id: crypto.randomUUID(),
      type: type as any,
      x: Math.round((canvasSize.width / 2) - 150),
      y: Math.round((canvasSize.height / 2) - 75),
      width: 300,
      height: 150,
      content,
      style: {},
      teamId: teamId, // Add teamId to element
      zIndex: maxZIndex + 1, // Always place new elements on top
    };
    
    // Adjust size and content based on type
    if (type === 'text') {
      newElement.height = 50;
      // Set default text content if not provided
      if (!content.text) {
        newElement.content.text = "Sample Text";
      }
      // Set default text style if not provided
      if (!newElement.content.style) {
        newElement.content.style = {
          textAlign: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'date') {
      newElement.height = 50;
      // Set default date style if not provided
      if (!newElement.content.style) {
        newElement.content.style = {
          textAlign: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'multimedia') {
      newElement.height = 100;
      // Set default multimedia content if not provided
      if (!newElement.content.dataType) {
        newElement.content.dataType = 'text';
      }
      // Initialize the apiMapping property as an empty object
      if (!newElement.content.apiMapping) {
        newElement.content.apiMapping = {};
      }
      // Set default text style for multimedia text elements
      if (newElement.content.dataType === 'text' && !newElement.content.style) {
        newElement.content.style = {
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          color: '#000000',
          textAlign: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'playlist') {
      newElement.width = 400;
      newElement.height = 225;
      // Ensure default playlist structure exists
      if (!newElement.content.playlist) {
        newElement.content.playlist = [];
      }
      if (newElement.content.currentIndex === undefined) {
        newElement.content.currentIndex = 0;
      }
    }
    
    setElements(prev => [...prev, newElement]);
    setSelectedElementId(newElement.id);
  };

  // Update an element's properties
  const handleUpdateElement = (id: string, updates: Partial<CanvasElement>) => {
    setElements(prev => 
      prev.map(el => 
        el.id === id ? { ...el, ...updates } : el
      )
    );
  };

  // Delete an element from the canvas
  const handleDeleteElement = (id: string) => {
    setElements(prev => prev.filter(el => el.id !== id));
    setSelectedElementId(null);
  };
  
  // Add a media item to the playlist of a selected multimedia element
  const handleAddToPlaylist = (mediaItem: any) => {
    if (!selectedElementId) return;
    
    // Find the selected element
    const element = elements.find(el => el.id === selectedElementId);
    
    // Only add to playlist if it's a playlist element type
    if (element && element.type === 'playlist') {
      // Create a new playlist array if it doesn't exist
      const playlist = element.content?.playlist || [];
      
      // Create a media item entry for the playlist
      const playlistItem = {
        id: mediaItem.id,
        name: mediaItem.name || 'Untitled',
        fileUrl: mediaItem.url,
        fileType: mediaItem.fileType,
        thumbnailUrl: mediaItem.thumbnailUrl || mediaItem.thumbnail_url,
        thumbnail_url: mediaItem.thumbnailUrl || mediaItem.thumbnail_url,
        duration: mediaItem.fileType?.startsWith('image/') ? 5 : 0, // Default 5 seconds for images
      };
      
      // Update the element with the new playlist item
      setElements(prev => 
        prev.map(el => 
          el.id === selectedElementId 
            ? { 
                ...el, 
                content: { 
                  ...el.content, 
                  playlist: [...playlist, playlistItem]
                } 
              } 
            : el
        )
      );
      
      // No toast notification when adding media items to the playlist
    }
  };

  // Handle element movement on the canvas
  const handleElementMove = (id: string, x: number, y: number) => {
    // Round position values to whole pixels
    const roundedX = Math.round(x);
    const roundedY = Math.round(y);
    
    setElements(prev => 
      prev.map(el => 
        el.id === id ? { ...el, x: roundedX, y: roundedY } : el
      )
    );
  };

  // Handle element resizing on the canvas
  const handleElementResize = (id: string, width: number, height: number) => {
    // Round size values to whole pixels
    const roundedWidth = Math.round(width);
    const roundedHeight = Math.round(height);
    
    setElements(prev => 
      prev.map(el => 
        el.id === id ? { ...el, width: roundedWidth, height: roundedHeight } : el
      )
    );
  };
  
  // Preview functionality
  const togglePreview = () => {
    if (isPreviewActive) {
      stopPreview();
    } else {
      startPreview();
    }
  };
  
  // Start the preview mode
  const startPreview = () => {
    // First check if any multimedia or playlist elements exist
    const multimediaElements = elements.filter(el => el.type === 'multimedia');
    const playlistElements = elements.filter(el => el.type === 'playlist');
    
    if (multimediaElements.length === 0 && playlistElements.length === 0) {
      toast({
        title: "Preview Error",
        description: "No multimedia or API elements found on the canvas.",
        variant: "destructive"
      });
      return;
    }
    
    // Set preview as active and initialize preview elements
    setIsPreviewActive(true);
    
    // Reset all elements to their first state
    const initialPreviewElements = [
      ...playlistElements.map(el => ({
        type: 'playlist' as const,
        elementId: el.id,
        currentIndex: 0
      })),
      ...multimediaElements.map(el => ({
        type: 'multimedia' as const,
        elementId: el.id,
        currentIndex: 0
      }))
    ];
    
    setCurrentPreviewElements(initialPreviewElements);
    
    // Start preview process for playlist elements
    playlistElements.forEach(element => {
      const playlist = element.content?.playlist || [];
      if (playlist.length > 0) {
        // Reset to first item
        updatePlaylistElement(element.id, 0);
      }
    });
    
    // Start preview process for API elements if we have API data
    if (apiData) {
      const apiHasMultipleRecords = checkIfApiHasMultipleRecords();
      console.log("API Preview: Has multiple records:", apiHasMultipleRecords, "Duration:", apiPreviewDuration);
      
      if (apiHasMultipleRecords && apiPreviewDuration > 0) {
        console.log("API Preview: Initializing multimedia elements with API data");
        
        // Find elements that have API mappings or a dataField set
        const apiElements = multimediaElements.filter(el => {
          // Check if element has API mapping in its content
          const hasApiMapping = el.content && 
                             el.content.apiMapping && 
                             Object.keys(el.content.apiMapping).length > 0;
          
          // Also check if element has a dataField set
          const hasDataField = el.content && 
                              el.content.dataField && 
                              el.content.dataField !== "" && 
                              el.content.dataField !== "_none";
          
          console.log(`Element ${el.id} has API mapping: ${hasApiMapping}`, el.content?.apiMapping);
          console.log(`Element ${el.id} has data field: ${hasDataField}`, el.content?.dataField);
          
          // Initialize apiMapping if we have a dataField but no mapping
          if (hasDataField && !hasApiMapping && el.content) {
            console.log(`Initializing missing apiMapping for element ${el.id} with dataField ${el.content.dataField}`);
            el.content.apiMapping = { [el.content.dataField]: true };
            return true;
          }
          
          return hasApiMapping || hasDataField;
        });
        
        console.log(`API Preview: Found ${apiElements.length} elements with API mappings`);
        
        // Start with the first record for all API-mapped elements
        apiElements.forEach(element => {
          console.log(`API Preview: Setting element ${element.id} to index 0`);
          updateMultimediaElement(element.id, 0);
        });
        
        // Force immediate analysis of the data structure to log helpful debugging info
        let dataArray = apiData;
        if (!Array.isArray(apiData)) {
          // Find the first array in the API data
          for (const key in apiData) {
            if (Array.isArray(apiData[key]) && apiData[key].length > 1) {
              dataArray = apiData[key];
              console.log(`API Preview: Found nested array at key "${key}" with ${dataArray.length} items`);
              break;
            }
          }
        } else {
          console.log(`API Preview: Data is an array with ${dataArray.length} items`);
        }
      } else {
        console.log("API Preview: Not setting up preview cycling - multiple records not found or duration is 0");
      }
    }
    
    toast({
      title: "Preview Started",
      description: "Preview mode is now active."
    });
  };
  
  // Stop the preview mode
  const stopPreview = () => {
    // Set flag to prevent API fetch in useEffect
    apiPreviewStoppingRef.current = true;
    
    // Reset the API data index
    currentApiDataIndexRef.current = 0;
    window.__API_DATA_INDEX__ = 0;
    
    // First, clean up all event listeners and timers
    cleanupPreviewEventListeners();
    
    // Then pause and reset all videos before changing elements
    // This prevents transition animations from playing during reset
    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      try {
        // Ensure video is paused
        video.pause();
        
        // Reset video to beginning
        video.currentTime = 0;
        
        // Remove autoplay to prevent re-triggering
        video.autoplay = false;
        
        console.log("Paused and reset video during preview stop");
      } catch (error) {
        console.error("Error stopping video:", error);
      }
    });
    
    // Reset all elements to their initial state (first item)
    // And update the UI to show the first item of each playlist/multimedia element
    setElements(prev => prev.map(element => {
      // Only update playlist and multimedia elements
      if (element.type === 'playlist' || element.type === 'multimedia') {
        return {
          ...element,
          content: {
            ...element.content,
            currentIndex: 0 // Reset to first item
          }
        };
      }
      return element;
    }));
    
    // Clear preview elements and set preview as inactive
    setCurrentPreviewElements([]);
    setIsPreviewActive(false);
    
    toast({
      title: "Preview Stopped",
      description: "All elements have been reset to their initial state."
    });
  };
  
  // Update the current index of a playlist element
  const updatePlaylistElement = (elementId: string, newIndex: number) => {
    setElements(prev => 
      prev.map(el => 
        el.id === elementId ? { 
          ...el, 
          content: { 
            ...el.content, 
            currentIndex: newIndex 
          } 
        } : el
      )
    );
    
    // Update the preview element tracking
    setCurrentPreviewElements(prev => 
      prev.map(item => 
        item.elementId === elementId ? { ...item, currentIndex: newIndex } : item
      )
    );
    
    // If we're in preview mode, we need to ensure videos play right after the currentIndex changes
    if (isPreviewActive) {
      // Give a small delay to allow the DOM to update with the new video element
      setTimeout(() => {
        // Find all videos within this specific element
        const elementWrapper = document.querySelector(`[data-element-id="${elementId}"]`);
        if (!elementWrapper) return;
        
        const videoElements = elementWrapper.querySelectorAll('video');
        videoElements.forEach(video => {
          video.muted = true;
          video.autoplay = true;
          video.loop = false;
          
          // Force play the video
          const playPromise = video.play();
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.error("Error playing video in playlist:", error);
              // Try again after a short delay
              setTimeout(() => {
                video.play().catch(e => console.error("Second attempt to play playlist video failed:", e));
              }, 100);
            });
          }
        });
      }, 100);
    }
  };
  
  // Function to update an element with data from the specified index
  const updateElementWithApiData = (element: CanvasElement, dataIndex: number) => {
    if (!element.content || !apiData) return;
    
    // Get the data array for this element
    let dataArray: any[] = [];
    
    // If element has a specific API data path, use it
    if (element.content.apiDataPath !== undefined) {
      if (element.content.apiDataPath === "") {
        // Top-level array
        if (Array.isArray(apiData)) {
          dataArray = apiData;
        }
      } else {
        // Nested array - follow the path
        try {
          let currentData = apiData;
          const pathParts = element.content.apiDataPath.split('.');
          
          for (const part of pathParts) {
            if (part.includes('[') && part.includes(']')) {
              // Handle array index in path (e.g., "results[0]")
              const arrayName = part.substring(0, part.indexOf('['));
              const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
              currentData = currentData[arrayName][index];
            } else {
              currentData = currentData[part];
            }
          }
          
          if (Array.isArray(currentData)) {
            dataArray = currentData;
          }
        } catch (error) {
          console.error(`Error accessing data at path "${element.content.apiDataPath}":`, error);
        }
      }
    } else {
      // Fallback to the old method
      dataArray = getApiDataArray() || [];
    }
    
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      console.log(`No valid data array found for element ${element.id}`);
      return;
    }
    
    // Ensure dataIndex is within bounds
    const actualIndex = dataIndex % dataArray.length;
    const record = dataArray[actualIndex];
    
    if (!record) {
      console.log(`No data record found at index ${actualIndex}`);
      return;
    }
    
    // Get the mapped fields for this element
    const mappedFields = element.content.apiMapping ? Object.keys(element.content.apiMapping) : [];
    const mainField = element.content.dataField || (mappedFields.length > 0 ? mappedFields[0] : null);
    
    if (!mainField) {
      console.log(`Element ${element.id} has no mapped fields`);
      return;
    }
    
    // Get the content based on the element type
    const elementType = element.content.type || 'text';
    
    // Update element based on its type
    switch (elementType) {
      case 'text':
        // For text, directly set the content text
        if (record.hasOwnProperty(mainField)) {
          element.content.text = String(record[mainField]);
          console.log(`Updated text element ${element.id} with field "${mainField}": "${element.content.text}"`);
        }
        break;
        
      case 'image':
        // For images, set the image source
        if (record.hasOwnProperty(mainField)) {
          element.content.src = String(record[mainField]);
          console.log(`Updated image element ${element.id} with field "${mainField}": "${element.content.src}"`);
        }
        break;
        
      case 'video':
        // For videos, set the video source
        if (record.hasOwnProperty(mainField)) {
          element.content.src = String(record[mainField]);
          console.log(`Updated video element ${element.id} with field "${mainField}": "${element.content.src}"`);
        }
        break;
        
      default:
        // For other types, try to set the text content
        if (record.hasOwnProperty(mainField)) {
          element.content.text = String(record[mainField]);
          console.log(`Updated element ${element.id} with field "${mainField}": "${element.content.text}"`);
        }
    }
    
    // Update the element's current index in preview tracking
    setCurrentPreviewElements(prev => 
      prev.map(item => 
        item.elementId === element.id ? { ...item, currentIndex: actualIndex } : item
      )
    );
  };
  
  // Function to cycle all API elements to the next data record
  const cycleApiDataToNextRecord = () => {
    console.log("Cycling API elements to next data record - START");
    
    // Set the cycling flag to prevent cleanup during the cycling operation
    isApiCyclingRef.current = true;
    
    // Verify preview is still active
    if (!isPreviewActive) {
      console.log("Preview is no longer active, skipping API data cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Find all API elements
    const apiElements = findApiElements();
    
    if (apiElements.length === 0) {
      console.log("No API elements found for cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Increment the current API data index
    const currentIndex = currentApiDataIndexRef.current;
    
    // Find the max length of all data arrays used by elements
    let maxArrayLength = 0;
    
    // First pass: determine max array length to use for cycling
    apiElements.forEach(element => {
      if (!element.content || !element.content.apiDataPath) return;
      
      // Get the data array for this element
      let dataArray: any[] = [];
      
      if (element.content.apiDataPath === "") {
        // Top-level array
        if (Array.isArray(apiData)) {
          dataArray = apiData;
        }
      } else {
        // Nested array - follow the path
        try {
          let currentData = apiData;
          const pathParts = element.content.apiDataPath.split('.');
          
          for (const part of pathParts) {
            if (part.includes('[') && part.includes(']')) {
              // Handle array index in path (e.g., "results[0]")
              const arrayName = part.substring(0, part.indexOf('['));
              const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
              currentData = currentData[arrayName][index];
            } else {
              currentData = currentData[part];
            }
          }
          
          if (Array.isArray(currentData)) {
            dataArray = currentData;
          }
        } catch (error) {
          console.error(`Error accessing data at path "${element.content.apiDataPath}":`, error);
        }
      }
      
      if (Array.isArray(dataArray) && dataArray.length > maxArrayLength) {
        maxArrayLength = dataArray.length;
      }
    });
    
    if (maxArrayLength === 0) {
      console.log("No valid data arrays found for cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Calculate next index
    const nextIndex = (currentIndex + 1) % maxArrayLength;
    
    console.log(`API data cycling from index ${currentIndex} to ${nextIndex} (max array length: ${maxArrayLength})`);
    
    // Update global state
    currentApiDataIndexRef.current = nextIndex;
    window.__API_DATA_INDEX__ = nextIndex;
    
    // Update each API element with the new data record
    apiElements.forEach(element => {
      updateElementWithApiData(element, nextIndex);
    });
    
    // Force canvas re-render to show updated elements
    setElements(prev => {
      console.log("Forcing deep canvas re-render with updated API data index:", nextIndex);
      return prev.map(el => ({
        ...el,
        _forceUpdate: Date.now()
      }));
    });
    
    // Reset the cycling flag now that we're done with the operation
    isApiCyclingRef.current = false;
    
    console.log("Cycling API elements to next data record - COMPLETE");
  };
  
  // Function to set up API data preview cycling
  const setupApiDataPreview = () => {
    // Reset any ongoing preview
    if (previewIntervalRef.current) {
      window.clearInterval(previewIntervalRef.current);
      previewIntervalRef.current = null;
    }
    
    if (!apiData || apiPreviewDuration <= 0) {
      console.log("No API data or preview duration set, skipping API preview setup");
      return;
    }
    
    // Ensure we always start with index 0 when preview begins
    currentApiDataIndexRef.current = 0;
    window.__API_DATA_INDEX__ = 0;
    console.log("API data index reset to 0 at start of preview");
    
    // Find all possible data arrays in the API data
    const apiDataArrays = findApiDataArrays(apiData);
    console.log(`Found ${apiDataArrays.length} potential data arrays for API preview`);
    
    if (apiDataArrays.length === 0) {
      console.log("No data arrays found in API data, skipping preview");
      return;
    }
    
    // Get the API elements on the canvas - find multimedia elements with API data mapping
    const apiElements = findApiElements();
    
    if (apiElements.length === 0) {
      console.log("No API elements found on canvas");
      return;
    }
    
    console.log(`Found ${apiElements.length} API elements for data cycling`);
    
    // Set up preview for each API element based on its data type
    const setupElementsResult = setupApiElementsPreview(apiElements, apiDataArrays);
    
    // If we only have elements that use timer-based cycling (not video ended events)
    if (setupElementsResult.useIntervalCycling) {
      // Set up cycling interval based on apiPreviewDuration
      console.log(`Setting up API data cycling interval with ${apiPreviewDuration}s duration`);
      
      // Set up cycling interval with robust implementation
      previewIntervalRef.current = window.setInterval(() => {
        if (!isPreviewActive) {
          console.log("Preview no longer active, stopping API data cycling interval");
          if (previewIntervalRef.current) {
            window.clearInterval(previewIntervalRef.current);
            previewIntervalRef.current = null;
          }
          return;
        }
        
        console.log("API cycling interval triggered");
        
        // Move to next API data record
        cycleApiDataToNextRecord();
      }, apiPreviewDuration * 1000);
      
      console.log(`API data cycling interval set with ID: ${previewIntervalRef.current}`);
    } else {
      console.log("Using video ended events for API data cycling, no interval needed");
    }
  };
  
  // Find all potential data arrays in the API data
  const findApiDataArrays = (data: any): any[] => {
    const dataArrays: any[] = [];
    
    // If the data is already an array with multiple records, add it
    if (Array.isArray(data) && data.length > 1) {
      console.log(`Found top-level array with ${data.length} records`);
      dataArrays.push({
        path: "",
        data: data
      });
    }
    
    // Function to recursively search for arrays in the data structure
    const findArrays = (obj: any, path: string = "") => {
      if (!obj || typeof obj !== 'object') return;
      
      if (Array.isArray(obj)) {
        if (obj.length > 1 && typeof obj[0] === 'object') {
          console.log(`Found nested array at path "${path}" with ${obj.length} records`);
          dataArrays.push({
            path,
            data: obj
          });
        }
        
        // Don't need to traverse arrays of arrays - we're looking for arrays of objects
        if (obj.length > 0 && typeof obj[0] === 'object' && !Array.isArray(obj[0])) {
          // Process the first item as an example
          findArrays(obj[0], path ? `${path}[0]` : '[0]');
        }
      } else {
        // Process regular objects
        Object.keys(obj).forEach(key => {
          const newPath = path ? `${path}.${key}` : key;
          
          if (obj[key] && typeof obj[key] === 'object') {
            findArrays(obj[key], newPath);
          }
        });
      }
    };
    
    // Start the recursive search
    findArrays(data);
    
    return dataArrays;
  };
  
  // Find all API elements on the canvas
  const findApiElements = () => {
    return elements.filter(el => {
      // Element must be multimedia type
      if (el.type !== 'multimedia') return false;
      
      // Check if element has API mapping in its content
      const hasApiMapping = el.content && 
                          el.content.apiMapping && 
                          Object.keys(el.content.apiMapping).length > 0;
      
      // Also check if element has a dataField set
      const hasDataField = el.content && 
                          el.content.dataField && 
                          el.content.dataField !== "" && 
                          el.content.dataField !== "_none";
      
      console.log(`Element ${el.id} has API mapping: ${hasApiMapping ? 'true' : 'false'}`, el.content?.apiMapping);
      console.log(`Element ${el.id} has data field: ${hasDataField ? 'true' : 'false'}`, el.content?.dataField);
      
      // Initialize apiMapping if we have a dataField but no mapping
      if (hasDataField && !hasApiMapping && el.content) {
        console.log(`Initializing missing apiMapping for element ${el.id} with dataField ${el.content.dataField}`);
        el.content.apiMapping = { [el.content.dataField]: true };
        return true;
      }
      
      return hasApiMapping || hasDataField;
    });
  };
  
  // Set up preview for API elements based on their data type
  const setupApiElementsPreview = (apiElements: CanvasElement[], apiDataArrays: any[]): { 
    useIntervalCycling: boolean 
  } => {
    let useIntervalCycling = false;
    
    // Set all API elements to display data from the initial record
    apiElements.forEach(element => {
      // Try to find the data array that contains the fields needed for this element
      const elementFields = element.content?.apiMapping ? Object.keys(element.content.apiMapping) : [];
      if (!elementFields.length && element.content?.dataField) {
        elementFields.push(element.content.dataField);
      }
      
      if (elementFields.length === 0) {
        console.log(`Element ${element.id} has no mapped fields, skipping`);
        return;
      }
      
      console.log(`Element ${element.id} needs fields: ${elementFields.join(', ')}`);
      
      // Find a data array that has a match for at least one of the needed fields
      const matchingArray = findMatchingDataArray(apiDataArrays, elementFields);
      
      if (!matchingArray) {
        console.log(`No matching data array found for element ${element.id}`);
        return;
      }
      
      // Store the data array path for this element
      if (element.content) {
        element.content.apiDataPath = matchingArray.path;
      }
      
      // Check if element contains a video
      const elementType = element.content?.type || 'text';
      const isVideo = elementType === 'video';
      
      if (!isVideo) {
        // This element will use interval-based cycling
        useIntervalCycling = true;
      } else {
        console.log(`Element ${element.id} is a video element, will use ended event for cycling`);
        
        // Set up ended event for video elements
        // This will be handled in useEffect after the elements are rendered
      }
      
      // Initialize the element with the first record's data
      updateElementWithApiData(element, 0);
    });
    
    return { useIntervalCycling };
  };
  
  // Find a data array that contains at least one of the needed fields
  const findMatchingDataArray = (apiDataArrays: any[], fields: string[]): any => {
    for (const dataArray of apiDataArrays) {
      if (dataArray.data.length > 0) {
        const firstRecord = dataArray.data[0];
        
        // Check if any of the required fields exist in this data array
        for (const field of fields) {
          // For simple field access (not nested)
          if (firstRecord.hasOwnProperty(field)) {
            console.log(`Found matching data array for field ${field} at path "${dataArray.path}"`);
            return dataArray;
          }
          
          // For nested field access (using dot notation)
          if (field.includes('.')) {
            const fieldParts = field.split('.');
            let currentObj = firstRecord;
            let foundMatch = true;
            
            for (const part of fieldParts) {
              if (!currentObj || !currentObj.hasOwnProperty(part)) {
                foundMatch = false;
                break;
              }
              currentObj = currentObj[part];
            }
            
            if (foundMatch) {
              console.log(`Found matching data array for nested field ${field} at path "${dataArray.path}"`);
              return dataArray;
            }
          }
        }
      }
    }
    
    return null;
  };
  
  // Function to get the array of API data for cycling
  const getApiDataArray = () => {
    if (!apiData) return null;
    
    // If apiData is already an array, return it
    if (Array.isArray(apiData)) {
      return apiData;
    }
    
    // Look for arrays in the API data
    for (const key in apiData) {
      if (Array.isArray(apiData[key]) && apiData[key].length > 1) {
        console.log(`Found API data array at key "${key}" with ${apiData[key].length} items`);
        return apiData[key];
      }
    }
    
    return null;
  };
  
  // Function to update an element with data from the specified index
  const updateElementWithApiData = (element: CanvasElement, dataIndex: number) => {
    if (!element.content || !apiData) return;
    
    // Get the data array for this element
    let dataArray: any[] = [];
    
    // If element has a specific API data path, use it
    if (element.content.apiDataPath !== undefined) {
      if (element.content.apiDataPath === "") {
        // Top-level array
        if (Array.isArray(apiData)) {
          dataArray = apiData;
        }
      } else {
        // Nested array - follow the path
        try {
          let currentData = apiData;
          const pathParts = element.content.apiDataPath.split('.');
          
          for (const part of pathParts) {
            if (part.includes('[') && part.includes(']')) {
              // Handle array index in path (e.g., "results[0]")
              const arrayName = part.substring(0, part.indexOf('['));
              const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
              currentData = currentData[arrayName][index];
            } else {
              currentData = currentData[part];
            }
          }
          
          if (Array.isArray(currentData)) {
            dataArray = currentData;
          }
        } catch (error) {
          console.error(`Error accessing data at path "${element.content.apiDataPath}":`, error);
        }
      }
    } else {
      // Fallback to the old method
      dataArray = getApiDataArray() || [];
    }
    
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      console.log(`No valid data array found for element ${element.id}`);
      return;
    }
    
    // Ensure dataIndex is within bounds
    const actualIndex = dataIndex % dataArray.length;
    const record = dataArray[actualIndex];
    
    if (!record) {
      console.log(`No data record found at index ${actualIndex}`);
      return;
    }
    
    // Get the mapped fields for this element
    const mappedFields = element.content.apiMapping ? Object.keys(element.content.apiMapping) : [];
    const mainField = element.content.dataField || (mappedFields.length > 0 ? mappedFields[0] : null);
    
    if (!mainField) {
      console.log(`Element ${element.id} has no mapped fields`);
      return;
    }
    
    // Get the content based on the element type
    const elementType = element.content.type || 'text';
    
    // Update element based on its type
    switch (elementType) {
      case 'text':
        // For text, directly set the content text
        if (record.hasOwnProperty(mainField)) {
          element.content.text = String(record[mainField]);
          console.log(`Updated text element ${element.id} with field "${mainField}": "${element.content.text}"`);
        }
        break;
        
      case 'image':
        // For images, set the image source
        if (record.hasOwnProperty(mainField)) {
          element.content.src = String(record[mainField]);
          console.log(`Updated image element ${element.id} with field "${mainField}": "${element.content.src}"`);
        }
        break;
        
      case 'video':
        // For videos, set the video source
        if (record.hasOwnProperty(mainField)) {
          element.content.src = String(record[mainField]);
          console.log(`Updated video element ${element.id} with field "${mainField}": "${element.content.src}"`);
        }
        break;
        
      default:
        // For other types, try to set the text content
        if (record.hasOwnProperty(mainField)) {
          element.content.text = String(record[mainField]);
          console.log(`Updated element ${element.id} with field "${mainField}": "${element.content.text}"`);
        }
    }
    
    // Update the element's current index in preview tracking
    setCurrentPreviewElements(prev => 
      prev.map(item => 
        item.elementId === element.id ? { ...item, currentIndex: actualIndex } : item
      )
    );
  };
  
  // Function to cycle all API elements to the next data record
  const cycleApiDataToNextRecord = () => {
    console.log("Cycling API elements to next data record - START");
    
    // Set the cycling flag to prevent cleanup during the cycling operation
    isApiCyclingRef.current = true;
    
    // Verify preview is still active
    if (!isPreviewActive) {
      console.log("Preview is no longer active, skipping API data cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Find all API elements
    const apiElements = findApiElements();
    
    if (apiElements.length === 0) {
      console.log("No API elements found for cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Increment the current API data index
    const currentIndex = currentApiDataIndexRef.current;
    
    // Find the max length of all data arrays used by elements
    let maxArrayLength = 0;
    
    // First pass: determine max array length to use for cycling
    apiElements.forEach(element => {
      if (!element.content || !element.content.apiDataPath) return;
      
      // Get the data array for this element
      let dataArray: any[] = [];
      
      if (element.content.apiDataPath === "") {
        // Top-level array
        if (Array.isArray(apiData)) {
          dataArray = apiData;
        }
      } else {
        // Nested array - follow the path
        try {
          let currentData = apiData;
          const pathParts = element.content.apiDataPath.split('.');
          
          for (const part of pathParts) {
            if (part.includes('[') && part.includes(']')) {
              // Handle array index in path (e.g., "results[0]")
              const arrayName = part.substring(0, part.indexOf('['));
              const index = parseInt(part.substring(part.indexOf('[') + 1, part.indexOf(']')));
              currentData = currentData[arrayName][index];
            } else {
              currentData = currentData[part];
            }
          }
          
          if (Array.isArray(currentData)) {
            dataArray = currentData;
          }
        } catch (error) {
          console.error(`Error accessing data at path "${element.content.apiDataPath}":`, error);
        }
      }
      
      if (Array.isArray(dataArray) && dataArray.length > maxArrayLength) {
        maxArrayLength = dataArray.length;
      }
    });
    
    if (maxArrayLength === 0) {
      console.log("No valid data arrays found for cycling");
      isApiCyclingRef.current = false;
      return;
    }
    
    // Calculate next index
    const nextIndex = (currentIndex + 1) % maxArrayLength;
    
    console.log(`API data cycling from index ${currentIndex} to ${nextIndex} (max array length: ${maxArrayLength})`);
    
    // Update global state
    currentApiDataIndexRef.current = nextIndex;
    window.__API_DATA_INDEX__ = nextIndex;
    
    // Update each API element with the new data record
    apiElements.forEach(element => {
      updateElementWithApiData(element, nextIndex);
    });
    
    // Force canvas re-render to show updated elements
    setElements(prev => {
      console.log("Forcing deep canvas re-render with updated API data index:", nextIndex);
      return prev.map(el => ({
        ...el,
        _forceUpdate: Date.now()
      }));
    });
    
    // Reset the cycling flag now that we're done with the operation
    isApiCyclingRef.current = false;
    
    console.log("Cycling API elements to next data record - COMPLETE");
  };

  // Update the current index of a multimedia element for API data preview
  const updateMultimediaElement = (elementId: string, dataIndex: number) => {
    console.log(`Updating multimedia element ${elementId} to data index ${dataIndex}`);
    
    // Update the element's current index in the preview tracking array
    setCurrentPreviewElements(prev => {
      const newPreviewElements = prev.map(item => 
        item.elementId === elementId ? { ...item, currentIndex: dataIndex } : item
      );
      
      console.log("Updated preview elements:", newPreviewElements);
      return newPreviewElements;
    });
    
    // Force the canvas to re-render with the new data index
    // This is critical for ensuring the element shows data from the new index
    setElements(prev => {
      // Use the same deep render approach as in cycleToNextApiDataRecord
      return prev.map(el => ({
        ...el,
        // Add a timestamp to ensure React sees each element as changed
        _forceUpdate: Date.now()
      }));
    });
  };
  
  // Helper to check if API data has multiple records with the same structure
  const checkIfApiHasMultipleRecords = () => {
    if (!apiData) return false;
    
    // Check if the API data is an array with multiple items
    if (Array.isArray(apiData) && apiData.length > 1) {
      // Check if the first two items have the same structure
      const firstItem = apiData[0];
      const secondItem = apiData[1];
      
      if (typeof firstItem === 'object' && typeof secondItem === 'object') {
        const firstKeys = Object.keys(firstItem).sort().join(',');
        const secondKeys = Object.keys(secondItem).sort().join(',');
        
        return firstKeys === secondKeys;
      }
    }
    
    // Check for nested arrays in the data
    for (const key in apiData) {
      if (Array.isArray(apiData[key]) && apiData[key].length > 1) {
        // Check if array items have the same structure
        const firstItem = apiData[key][0];
        const secondItem = apiData[key][1];
        
        if (typeof firstItem === 'object' && typeof secondItem === 'object') {
          const firstKeys = Object.keys(firstItem).sort().join(',');
          const secondKeys = Object.keys(secondItem).sort().join(',');
          
          if (firstKeys === secondKeys) {
            return true;
          }
        }
      }
    }
    
    return false;
  };

  // Create a new slide
  const handleCreateSlide = async (data: SlideFormValues) => {
    if (!teamId) return;
    
    try {
      setIsCreateModalOpen(false);
      setCanvasSize({ width: data.width, height: data.height });
      setCanvasBackground(data.backgroundColor || "#ffffff");
      setApiUrl(data.apiUrl || "");
      setApiPreviewDuration(data.apiDataPreviewDuration || 0);
      
      // Ensure default values
      const backgroundColor = data.backgroundColor || "#ffffff";
      
      // Create slide in the database
      const response = await apiRequest('POST', '/api/slides', {
        teamId,
        name: data.name,
        content: [],
        slideWidth: data.width,
        slideHeight: data.height,
        slideBackgroundColor: backgroundColor,
        apiUrl: data.apiUrl || "",
        apiDataPreviewDuration: data.apiDataPreviewDuration || 0,
      });
      
      // Parse the response to get the slide data
      const slideData = await response.json();
      
      if (slideData && slideData.id) {
        setCurrentSlideId(slideData.id);
        setCurrentSlideName(slideData.name || data.name);
      } else {
        console.error('Invalid slide data received from server');
      }
      setElements([]);
      
      // Refresh the slides list
      refetch();
      
      toast({
        title: "Slide created",
        description: "Your new slide has been created successfully.",
      });
      
      // Set up the edit form values in case user wants to edit later
      // But don't open the modal automatically
      editForm.setValue("name", data.name);
      editForm.setValue("width", data.width);
      editForm.setValue("height", data.height);
      editForm.setValue("backgroundColor", data.backgroundColor || "#ffffff");
      editForm.setValue("apiUrl", data.apiUrl || "");
      editForm.setValue("apiDataPreviewDuration", data.apiDataPreviewDuration || 0);
      
    } catch (error) {
      toast({
        title: "Error creating slide",
        description: "There was a problem creating your slide.",
        variant: "destructive",
      });
    }
  };

  // Save the current slide
  const handleSaveSlide = async () => {
    if (!currentSlideId || !teamId) return;
    
    try {
      setIsSaving(true);
      
      // Update slide in the database
      await apiRequest('PATCH', `/api/slides/${currentSlideId}`, {
        teamId,
        content: elements,
        slideBackgroundColor: canvasBackground,
        apiUrl: apiUrl,
        apiDataPreviewDuration: apiPreviewDuration,
      });
      
      toast({
        title: "Slide saved",
        description: "Your slide has been saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error saving slide",
        description: "There was a problem saving your slide.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Load a slide
  const handleLoadSlide = async (slideId: string) => {
    try {
      const response = await fetch(`/api/slides/${slideId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load slide: ${response.status} ${response.statusText}`);
      }
      
      const slideData = await response.json();
      
      if (!slideData || !slideData.id) {
        throw new Error('Invalid slide data received from server');
      }
      
      setCurrentSlideId(slideData.id);
      setCurrentSlideName(slideData.name || 'Untitled Slide');
      setCanvasSize({ 
        width: slideData.slideWidth || slideData.width || 1920, 
        height: slideData.slideHeight || slideData.height || 1080
      });
      setCanvasBackground(slideData.slideBackgroundColor || "#ffffff");
      setApiUrl(slideData.apiUrl || "");
      setApiPreviewDuration(slideData.apiDataPreviewDuration || 0);
      setElements(slideData.content || []);
      setIsCreateModalOpen(false);
    } catch (error) {
      console.error('Error loading slide:', error);
      toast({
        title: "Error loading slide",
        description: "There was a problem loading the slide. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <MainLayout title={currentSlideName || "Slide Designer"}>
      <div className="flex h-[calc(100vh-5rem)] overflow-hidden border rounded-md">
        {/* Toolbox with Error Boundary */}
        <ErrorBoundary componentName="Toolbox">
          <Toolbox 
            teamId={teamId} 
            onAddElement={handleAddElement} 
            selectedElement={getSelectedElement()}
            onAddToPlaylist={handleAddToPlaylist}
          />
        </ErrorBoundary>
        
        {/* Canvas Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Toolbar */}
          <div className="bg-background border-b p-2 flex justify-between items-center">
            <div>
              <Button variant="outline" size="sm" onClick={() => setIsCreateModalOpen(true)}>
                <ImagePlus className="mr-2 h-4 w-4" />
                New Slide
              </Button>
              {slides && slides.length > 0 && (
                <div className="inline-block ml-2 w-48">
                  <Select
                    value={currentSlideId || ""}
                    onValueChange={handleLoadSlide}
                  >
                    <SelectTrigger className="h-8 text-sm">
                      <SelectValue placeholder="Load slide" />
                    </SelectTrigger>
                    <SelectContent>
                      {slides.map(slide => (
                        <SelectItem key={slide.id} value={slide.id}>
                          {slide.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                disabled={!selectedElementId}
                onClick={handleBringToFront}
                title="Move Forward (+1)"
              >
                <ArrowUpToLine className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                disabled={!selectedElementId}
                onClick={handleSendToBack}
                title="Move Backward (-1)"
              >
                <ArrowDownToLine className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                disabled={elements.length === 0}
                onClick={() => setElements([])}
                title="Clear Canvas"
              >
                <Trash className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline"
                size="icon"
                disabled={!currentSlideId}
                onClick={() => {
                  if (currentSlideId) {
                    // Set the edit form values based on current slide properties
                    editForm.setValue("name", currentSlideName);
                    editForm.setValue("width", canvasSize.width);
                    editForm.setValue("height", canvasSize.height);
                    editForm.setValue("backgroundColor", canvasBackground);
                    editForm.setValue("apiUrl", apiUrl);
                    editForm.setValue("apiDataPreviewDuration", apiPreviewDuration);
                    setIsEditModalOpen(true);
                  }
                }}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button 
                variant={isPreviewActive ? "default" : "outline"}
                size="sm"
                onClick={togglePreview}
                disabled={elements.length === 0 || !currentSlideId}
                title={isPreviewActive ? "Stop Preview" : "Start Preview"}
                className={isPreviewActive ? "bg-red-500 hover:bg-red-600" : ""}
              >
                {isPreviewActive ? (
                  <>
                    <Square className="mr-2 h-4 w-4" />
                    Stop
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Preview
                  </>
                )}
              </Button>
              {apiUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchApiData}
                  disabled={isApiLoading}
                  className="relative"
                >
                  {isApiLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading API
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${apiData ? 'bg-green-500' : 'bg-gray-300'}`}></span>
                      Refresh API
                    </span>
                  )}
                </Button>
              )}
              <Button 
                onClick={handleSaveSlide} 
                disabled={!currentSlideId || elements.length === 0 || isSaving}
              >
                <SaveIcon className="mr-2 h-4 w-4" />
                {isSaving ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>
          
          {/* Canvas with Error Boundary */}
          <ErrorBoundary componentName="Canvas">
            <Canvas
              key={`canvas-${currentApiDataIndexRef.current}-${isPreviewActive ? 'preview' : 'edit'}`}
              width={canvasSize.width}
              height={canvasSize.height}
              elements={elements}
              onElementSelect={setSelectedElementId}
              onElementMove={handleElementMove}
              onElementResize={handleElementResize}
              selectedElementId={selectedElementId}
              backgroundColor={canvasBackground}
              isPreviewMode={isPreviewActive}
            />
          </ErrorBoundary>
        </div>
        
        {/* Element Properties with Error Boundary */}
        <ErrorBoundary componentName="Element Properties">
          <ElementProperties
            selectedElement={getSelectedElement()}
            onUpdateElement={handleUpdateElement}
            onDeleteElement={handleDeleteElement}
          />
        </ErrorBoundary>
      </div>
      
      {/* Create Slide Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Slide</DialogTitle>
            <DialogDescription>
              Enter the details for your new slide
            </DialogDescription>
          </DialogHeader>
          <ErrorBoundary componentName="Create Slide Form">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreateSlide)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Slide Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="width"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Width (px)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={e => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="height"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Height (px)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field} 
                            onChange={e => field.onChange(parseInt(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="backgroundColor"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Background Color</FormLabel>
                      <FormControl>
                        <ColorPicker 
                          value={field.value || "#ffffff"} 
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="apiUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API URL (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          {...field}
                          placeholder="https://example.com/api/data"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="apiDataPreviewDuration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Data Preview Duration (seconds)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value))}
                          placeholder="0"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  {slides && slides.length > 0 && (
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setIsCreateModalOpen(false)}
                    >
                      Cancel
                    </Button>
                  )}
                  <Button type="submit">Create Slide</Button>
                </DialogFooter>
              </form>
            </Form>
          </ErrorBoundary>
        </DialogContent>
      </Dialog>
      
      {/* Edit Slide Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Slide Properties</DialogTitle>
            <DialogDescription>
              Modify the dimensions, background color, and API settings for this slide
            </DialogDescription>
          </DialogHeader>
          
          <ErrorBoundary componentName="Edit Slide Form">
            <Form {...editForm}>
            <form 
              onSubmit={editForm.handleSubmit(async (data) => {
                if (!currentSlideId || !teamId) return;
                
                try {
                  setIsEditModalOpen(false);
                  setCurrentSlideName(data.name);
                  setCanvasSize({ width: data.width, height: data.height });
                  setCanvasBackground(data.backgroundColor || "#ffffff");
                  setApiUrl(data.apiUrl || "");
                  setApiPreviewDuration(data.apiDataPreviewDuration || 0);
                  
                  // Update slide in the database
                  await apiRequest('PATCH', `/api/slides/${currentSlideId}`, {
                    teamId,
                    name: data.name,
                    slideWidth: data.width,
                    slideHeight: data.height,
                    slideBackgroundColor: data.backgroundColor,
                    apiUrl: data.apiUrl,
                    apiDataPreviewDuration: data.apiDataPreviewDuration,
                  });
                  
                  // Refresh the slides list
                  refetch();
                  
                  toast({
                    title: "Slide updated",
                    description: "Your slide properties have been updated successfully.",
                  });
                } catch (error) {
                  toast({
                    title: "Error updating slide",
                    description: "There was a problem updating your slide properties.",
                    variant: "destructive",
                  });
                }
              })} 
              className="space-y-4"
            >
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slide Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width (px)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={editForm.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (px)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={editForm.control}
                name="backgroundColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Background Color</FormLabel>
                    <FormControl>
                      <ColorPicker 
                        value={field.value || "#ffffff"} 
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="apiUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API URL (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        {...field}
                        placeholder="https://example.com/api/data"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editForm.control}
                name="apiDataPreviewDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Data Preview Duration (seconds)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        onChange={e => field.onChange(parseInt(e.target.value))}
                        placeholder="0"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Update Slide</Button>
              </DialogFooter>
            </form>
          </Form>
          </ErrorBoundary>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
