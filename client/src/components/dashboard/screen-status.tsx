import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  <PERSON><PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";

interface ScreenStatusProps {
  online: number;
  offline: number;
}

export function ScreenStatus({ online, offline }: ScreenStatusProps) {
  const total = online + offline;
  const onlinePercentage = total > 0 ? Math.round((online / total) * 100) : 0;
  const circleCircumference = 2 * Math.PI * 58; // 58 is the radius of our circle
  const offset = circleCircumference - (onlinePercentage / 100) * circleCircumference;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Screen Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-center mb-6">
          <div className="relative flex items-center justify-center">
            <svg className="w-32 h-32 transform -rotate-90">
              <circle 
                cx="64" 
                cy="64" 
                r="58" 
                fill="none" 
                stroke="hsl(var(--muted))" 
                strokeWidth="12" 
              />
              <circle 
                cx="64" 
                cy="64" 
                r="58" 
                fill="none" 
                stroke="hsl(var(--success))" 
                strokeWidth="12" 
                strokeDasharray={circleCircumference} 
                strokeDashoffset={offset} 
              />
            </svg>
            <div className="absolute flex flex-col items-center">
              <span className="text-3xl font-bold">{onlinePercentage}%</span>
              <span className="text-sm text-muted-foreground">Online</span>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="p-3 bg-success/10 rounded-lg">
            <h5 className="text-xl font-bold text-success">{online}</h5>
            <p className="text-sm text-muted-foreground">Online</p>
          </div>
          <div className="p-3 bg-destructive/10 rounded-lg">
            <h5 className="text-xl font-bold text-destructive">{offline}</h5>
            <p className="text-sm text-muted-foreground">Offline</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
