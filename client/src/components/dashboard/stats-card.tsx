import { ReactNode } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowUp, ArrowDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  iconBgColor: string;
  iconColor: string;
  trend?: {
    value: number;
    timeframe: string;
    isPositive: boolean;
  };
  subtitle?: string;
}

export function StatsCard({
  title,
  value,
  icon,
  iconBgColor,
  iconColor,
  trend,
  subtitle,
}: StatsCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div>
            <p className="text-muted-foreground text-sm font-medium">{title}</p>
            <h3 className="text-2xl font-bold mt-2">{value}</h3>
            
            {trend && (
              <p 
                className={cn(
                  "text-xs mt-1 flex items-center",
                  trend.isPositive ? "text-green-500" : "text-red-500"
                )}
              >
                {trend.isPositive ? (
                  <ArrowUp className="mr-1 h-3 w-3" />
                ) : (
                  <ArrowDown className="mr-1 h-3 w-3" />
                )}
                {Math.abs(trend.value)}% from {trend.timeframe}
              </p>
            )}
            
            {subtitle && (
              <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
          
          <div 
            className={cn(
              "p-3 rounded-lg flex items-center justify-center",
              iconBgColor
            )}
          >
            <div className={cn("text-xl", iconColor)}>{icon}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
