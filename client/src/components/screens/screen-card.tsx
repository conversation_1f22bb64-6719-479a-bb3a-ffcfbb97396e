import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { formatDateTime } from "@/lib/utils";
import { MoreVertical, Trash2, PowerOff, Settings, ExternalLink, ListVideo } from "lucide-react";

interface ScreenCardProps {
  id: string;
  code: string;
  name: string;
  location?: string;
  status?: "online" | "offline";
  lastSeen?: Date | string | null;
  updateFrequency?: string;
  tags?: string[];
  // Enhanced fields for trial functionality
  subscriptionStatus?: string;
  trialEndsAt?: Date | string;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onConfigure: (id: string) => void;
  onViewDetails: (id: string) => void;
}

export function ScreenCard({
  id,
  code,
  name,
  location,
  status: providedStatus,
  lastSeen,
  updateFrequency,
  tags,
  subscriptionStatus,
  trialEndsAt,
  onEdit,
  onDelete,
  onConfigure,
  onViewDetails,
}: ScreenCardProps) {
  // Calculate status based on lastSeen
  // If lastSeen is null or undefined, status is "offline"
  // If lastSeen is within the last 5 minutes, status is "online"
  // Otherwise, status is "offline"
  const calculateStatus = (): "online" | "offline" => {
    if (!lastSeen) return "offline";

    // Parse the lastSeen date, handling both string and Date objects
    const lastSeenDate = typeof lastSeen === 'string' ? new Date(lastSeen) : lastSeen;

    // Calculate a timestamp 5 minutes ago in UTC to match the database timestamp format
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    // For debugging - uncommenting these would help troubleshoot any timezone issues
    // console.log('Last seen date (UTC):', lastSeenDate.toISOString());
    // console.log('Five minutes ago (UTC):', fiveMinutesAgo.toISOString());
    // console.log('Current time (UTC):', now.toISOString());

    // Compare timestamps - both dates are now handled in the same timezone context
    return lastSeenDate > fiveMinutesAgo ? "online" : "offline";
  };

  // Use the calculated status instead of the provided one
  const status = calculateStatus();

  // Check if trial has expired (only disable buttons for expired trials, not unregistered)
  const isTrialExpired = (() => {
    if (subscriptionStatus !== 'trial' || !trialEndsAt) return false;

    try {
      const trialEndDate = typeof trialEndsAt === 'string' ? new Date(trialEndsAt) : trialEndsAt;

      // Check if the date is valid
      if (isNaN(trialEndDate.getTime())) return false;

      return new Date() > trialEndDate;
    } catch (error) {
      console.error('Error checking trial expiration:', error);
      return false;
    }
  })();
  const isUnregistered = subscriptionStatus === 'unregistered';
  const shouldDisableButtons = isTrialExpired; // Only disable for expired trials

  // Format trial expiration date for tooltip
  const formatTrialExpiration = (date: Date | string): string => {
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        return 'Invalid date';
      }

      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      }).format(dateObj);
    } catch (error) {
      console.error('Error formatting trial expiration date:', error);
      return 'Invalid date';
    }
  };

  const cardContent = (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-medium">{name}</h3>
              <Badge variant={status === "online" ? "default" : "secondary"} className={status === "online" ? "bg-green-500 hover:bg-green-600" : ""}>
                {status === "online" ? "Online" : "Offline"}
              </Badge>
              {subscriptionStatus === 'trial' && (
                <Badge variant="destructive" className="bg-red-500 hover:bg-red-600">
                  Trial
                </Badge>
              )}
              {subscriptionStatus === 'unregistered' && (
                <Badge variant="secondary" className="bg-gray-500 hover:bg-gray-600 text-white">
                  Unregistered
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              ID: {code} {location && `• ${location}`}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Last Ping Date & Time: {lastSeen ? formatDateTime(lastSeen) : "Never"}
            </p>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Screen Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onConfigure(id)}
                disabled={shouldDisableButtons}
                className={shouldDisableButtons ? "opacity-50 cursor-not-allowed" : ""}
              >
                <Settings className="mr-2 h-4 w-4" /> Configure
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewDetails(id)}>
                <ExternalLink className="mr-2 h-4 w-4" /> View Screen
              </DropdownMenuItem>
              <DropdownMenuItem>
                <PowerOff className="mr-2 h-4 w-4" /> {status === "online" ? "Turn Off" : "Turn On"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(id)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {tags && tags.length > 0 && (
          <div className="mt-4 flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-0 pb-4 px-6">
        <div className="grid grid-cols-2 gap-2 w-full">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onConfigure(id)}
            disabled={shouldDisableButtons}
          >
            <Settings className="mr-2 h-4 w-4" /> Configure
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(id)}
            disabled={shouldDisableButtons}
          >
            <ListVideo className="mr-2 h-4 w-4" /> Campaigns
          </Button>
        </div>
      </CardFooter>
    </Card>
  );

  // Wrap with tooltip if trial is expired
  if (isTrialExpired && trialEndsAt) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {cardContent}
          </TooltipTrigger>
          <TooltipContent>
            <p className="font-medium">Trial Expired</p>
            <p className="text-sm">Trial ended on {formatTrialExpiration(trialEndsAt)}</p>
            <p className="text-sm text-muted-foreground">Configure and Campaigns are disabled</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return cardContent;
}
