import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { formatDate, calculateProgress } from "@/lib/utils";
import { Monitor, Image } from "lucide-react";

interface CampaignItemProps {
  name: string;
  startDate: Date | string;
  endDate: Date | string;
  status: "active" | "scheduled" | "completed";
  screenCount: number;
  mediaCount: number;
}

export function CampaignItem({
  name,
  startDate,
  endDate,
  status,
  screenCount,
  mediaCount,
}: CampaignItemProps) {
  const progress = calculateProgress(startDate, endDate);

  return (
    <div className="border border-border rounded-lg p-4">
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-medium">{name}</h4>
          <p className="text-sm text-muted-foreground">
            {formatDate(startDate)} - {formatDate(endDate)}
          </p>
        </div>
        <Badge
          variant={
            status === "active"
              ? "default"
              : status === "scheduled"
                ? "secondary"
                : "destructive"
          }
          className={status === "active" ? "bg-[#05DAC3] text-[#020627] hover:bg-[#05DAC3]/80" : ""}
        >
          {status === "active"
            ? "Active"
            : status === "scheduled"
              ? "Scheduled"
              : "Completed"}
        </Badge>
      </div>

      <div className="mb-2">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-muted-foreground">Progress</span>
          <span className="font-medium">{progress}%</span>
        </div>
        <Progress
          value={progress}
          className={`h-2 ${
            status === "completed"
              ? "[&>div]:bg-destructive"
              : status === "active"
                ? "[&>div]:bg-[#05DAC3]"
                : ""
          }`}
        />
      </div>

      <div className="flex items-center text-sm text-muted-foreground">
        <span className="flex items-center mr-4">
          <Monitor className="mr-1 h-4 w-4" /> {screenCount} screens
        </span>
        <span className="flex items-center">
          <Image className="mr-1 h-4 w-4" /> {mediaCount} media files
        </span>
      </div>
    </div>
  );
}
