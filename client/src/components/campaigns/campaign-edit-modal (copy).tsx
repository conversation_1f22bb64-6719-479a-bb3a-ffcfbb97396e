import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  PlusCircle, 
  Search, 
  Calendar,
  Check,
  X,
  Presentation,
  CalendarRange,
  Monitor,
  Image as ImageIcon,
  ArrowUp,
  ArrowDown,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

// Schema for validation
const campaignSchema = z.object({
  name: z.string().min(1, "Name is required"),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
  status: z.enum(["active", "scheduled", "draft", "paused", "completed"]),
});

type CampaignFormValues = z.infer<typeof campaignSchema>;

interface CampaignEditModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  campaign: any; // The campaign to edit
  teamId: string;
  screens: any[]; // All available screens
  mediaItems: any[]; // All available media items
  slides: any[]; // All available slides
  selectedScreens: string[];
  setSelectedScreens: (ids: string[]) => void;
  selectedMedia: string[];
  setSelectedMedia: (ids: string[]) => void;
  screenTags: any[];
  mediaTags: any[];
  onSave: (data: CampaignFormValues, mediaOrder?: string[]) => void;
}

export function CampaignEditModal({
  isOpen,
  onOpenChange,
  campaign,
  teamId,
  screens,
  mediaItems,
  slides,
  selectedScreens,
  setSelectedScreens,
  selectedMedia,
  setSelectedMedia,
  screenTags,
  mediaTags,
  onSave,
}: CampaignEditModalProps) {
  // State for the popover controls
  const [screenSearchOpen, setScreenSearchOpen] = useState(false);
  const [mediaSearchOpen, setMediaSearchOpen] = useState(false);
  const [screenSearchQuery, setScreenSearchQuery] = useState("");
  const [mediaSearchQuery, setMediaSearchQuery] = useState("");
  const [selectedScreenStatusTag, setSelectedScreenStatusTag] = useState<string>("");
  const [selectedScreenTag, setSelectedScreenTag] = useState<string>("");
  const [selectedMediaTag, setSelectedMediaTag] = useState<string>("");
  // Use local state for media selection and ordering - decoupled from parent
  const [localSelectedMedia, setLocalSelectedMedia] = useState<string[]>([]);
  const [mediaDisplayOrder, setMediaDisplayOrder] = useState<string[]>([]);

  // Form to handle campaign settings
  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: campaign?.name || "",
      startDate: campaign?.startDate ? new Date(campaign.startDate) : new Date(),
      endDate: campaign?.endDate ? new Date(campaign.endDate) : new Date(new Date().setDate(new Date().getDate() + 7)),
      status: campaign?.status || "draft",
    },
  });

  // Update form and local state when campaign or selected media changes
  useEffect(() => {
    if (campaign) {
      form.reset({
        name: campaign.name,
        startDate: new Date(campaign.startDate),
        endDate: new Date(campaign.endDate),
        status: campaign.status as any,
      });
    }
    
    // Initialize local state from props
    setLocalSelectedMedia([...selectedMedia]);
    
    // Set initial display order based on selected media
    setMediaDisplayOrder([...selectedMedia]);
  }, [campaign, form, selectedMedia, isOpen]); // Include isOpen to reset when modal reopens

  // Selection helpers
  const toggleScreenSelection = (screenId: string) => {
    setSelectedScreens(
      selectedScreens.includes(screenId)
        ? selectedScreens.filter(id => id !== screenId)
        : [...selectedScreens, screenId]
    );
  };

  const toggleMediaSelection = (mediaId: string) => {
    if (localSelectedMedia.includes(mediaId)) {
      // Remove the media
      setLocalSelectedMedia(localSelectedMedia.filter(id => id !== mediaId));
      setMediaDisplayOrder(mediaDisplayOrder.filter(id => id !== mediaId));
    } else {
      // Add the media
      setLocalSelectedMedia([...localSelectedMedia, mediaId]);
      setMediaDisplayOrder([...mediaDisplayOrder, mediaId]);
    }
  };

  // Media reordering functions - only update local state
  const moveMediaUp = (mediaId: string) => {
    const index = mediaDisplayOrder.indexOf(mediaId);
    if (index > 0) {
      const newOrder = [...mediaDisplayOrder];
      [newOrder[index], newOrder[index - 1]] = [newOrder[index - 1], newOrder[index]];
      setMediaDisplayOrder(newOrder);
    }
  };

  const moveMediaDown = (mediaId: string) => {
    const index = mediaDisplayOrder.indexOf(mediaId);
    if (index < mediaDisplayOrder.length - 1) {
      const newOrder = [...mediaDisplayOrder];
      [newOrder[index], newOrder[index + 1]] = [newOrder[index + 1], newOrder[index]];
      setMediaDisplayOrder(newOrder);
    }
  };

  // Submit handler - pass local state with form data
  const handleSubmit = (data: CampaignFormValues) => {
    // Instead of updating parent state (which won't be reflected immediately),
    // pass our mediaDisplayOrder directly with the data to the onSave function
    
    // Add detailed logging to track the media ordering process
    console.log(`Submitting form with ${mediaDisplayOrder.length} ordered media items:`);
    if (mediaDisplayOrder.length > 0) {
      // Log each media item and its position for debugging
      mediaDisplayOrder.forEach((mediaId, idx) => {
        const isSlide = slides?.some(slide => slide.id === mediaId) || false;
        const type = isSlide ? "slide" : "media";
        const name = isSlide 
          ? slides?.find(slide => slide.id === mediaId)?.name 
          : mediaItems?.find(media => media.id === mediaId)?.name;
        
        console.log(`  ${idx+1}. ${type}: ${name} (ID: ${mediaId})`);
      });
    } else {
      console.log("  No media items selected");
    }
    
    // Pass both the form data and the ordered media IDs
    try {
      onSave(data, mediaDisplayOrder);
    } catch (error) {
      console.error("Error in campaign save:", error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl w-[95%]">
        <DialogHeader>
          <DialogTitle>Edit Campaign</DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs defaultValue="general" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="general">
                  <div className="flex items-center">
                    <CalendarRange className="h-4 w-4 mr-2" /> General
                  </div>
                </TabsTrigger>
                <TabsTrigger value="media">
                  <div className="flex items-center">
                    <ImageIcon className="h-4 w-4 mr-2" /> Media/Slides
                  </div>
                </TabsTrigger>
                <TabsTrigger value="screens">
                  <div className="flex items-center">
                    <Monitor className="h-4 w-4 mr-2" /> Screens
                  </div>
                </TabsTrigger>
              </TabsList>
              
              {/* General Settings Tab */}
              <TabsContent value="general" className="space-y-4 py-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Campaign Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Start Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarRange className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="endDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>End Date</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarRange className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="scheduled">Scheduled</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="paused">Paused</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
              
              {/* Media/Slides Tab */}
              <TabsContent value="media" className="space-y-6 py-4">
                <div className="flex flex-col space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-base font-medium">Selected Content</h3>
                    <Popover open={mediaSearchOpen} onOpenChange={setMediaSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="sm">
                          <PlusCircle className="h-4 w-4 mr-1" /> Add Content
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80 p-0" align="end">
                        <Command>
                          <CommandInput placeholder="Search media and slides..." value={mediaSearchQuery} onValueChange={setMediaSearchQuery} />
                          
                          <div className="border-t border-border p-2">
                            <div className="mb-2">
                              <p className="text-xs text-muted-foreground mb-1">Type</p>
                              <div className="flex flex-wrap gap-1 mb-1">
                                <Button 
                                  variant={selectedMediaTag === "" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedMediaTag("")}
                                  className="h-7 text-xs"
                                >
                                  All Types
                                </Button>
                                <Button 
                                  variant={selectedMediaTag === "image" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedMediaTag("image")}
                                  className="h-7 text-xs"
                                >
                                  Images
                                </Button>
                                <Button 
                                  variant={selectedMediaTag === "video" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedMediaTag("video")}
                                  className="h-7 text-xs"
                                >
                                  Videos
                                </Button>
                                <Button 
                                  variant={selectedMediaTag === "slide" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedMediaTag("slide")}
                                  className="h-7 text-xs"
                                >
                                  Slides
                                </Button>
                              </div>
                            </div>
                            
                            {/* Media tag filter */}
                            {mediaTags.length > 0 && (
                              <div className="mb-2">
                                <p className="text-xs text-muted-foreground mb-1">Tags</p>
                                <div className="flex flex-wrap gap-1 mb-1">
                                  <Button 
                                    variant={selectedMediaTag === "" ? "secondary" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedMediaTag("")}
                                    className="h-7 text-xs"
                                  >
                                    All Tags
                                  </Button>
                                  {mediaTags.map(tag => (
                                    <Button 
                                      key={tag.id}
                                      variant={selectedMediaTag === tag.id ? "secondary" : "outline"} 
                                      size="sm" 
                                      onClick={() => setSelectedMediaTag(tag.id)}
                                      className="h-7 text-xs"
                                    >
                                      {tag.name}
                                    </Button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <CommandList>
                            <CommandEmpty>No content found.</CommandEmpty>
                            <CommandGroup heading="Media Files">
                              {mediaItems
                                ?.filter(media => {
                                  // Filter by search query
                                  const matchesQuery = !mediaSearchQuery || 
                                    media.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());
                                  
                                  // Filter by type
                                  const matchesType = !selectedMediaTag || 
                                    selectedMediaTag === "image" && media.fileType.startsWith("image/") ||
                                    selectedMediaTag === "video" && media.fileType.startsWith("video/") ||
                                    (mediaTags.find(tag => tag.id === selectedMediaTag)?.media || []).includes(media.id);
                                  
                                  return matchesQuery && matchesType && selectedMediaTag !== "slide";
                                })
                                .map(media => (
                                  <CommandItem
                                    key={media.id}
                                    onSelect={() => {
                                      toggleMediaSelection(media.id);
                                    }}
                                  >
                                    <div className="flex items-center gap-2 w-full">
                                      <Checkbox checked={localSelectedMedia.includes(media.id)} />
                                      <div className="mr-2 h-8 w-8 overflow-hidden rounded border border-border flex-shrink-0">
                                        <img 
                                          src={media.thumbnailUrl || media.fileUrl} 
                                          alt={media.name} 
                                          className="h-full w-full object-cover"
                                        />
                                      </div>
                                      <div className="flex-1 overflow-hidden">
                                        <p className="truncate">{media.name}</p>
                                        <p className="text-xs text-muted-foreground truncate">
                                          {media.fileType.split("/")[0]}
                                        </p>
                                      </div>
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                            
                            {/* Add slides section when implemented */}
                            {slides && slides.length > 0 && (
                              <CommandGroup heading="Slides">
                                {slides
                                  ?.filter(slide => {
                                    // Filter by search query
                                    const matchesQuery = !mediaSearchQuery || 
                                      slide.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());
                                    
                                    // Only show slides when slide filter is selected or no filter is selected
                                    return matchesQuery && (selectedMediaTag === "slide" || !selectedMediaTag);
                                  })
                                  .map(slide => (
                                    <CommandItem
                                      key={slide.id}
                                      onSelect={() => {
                                        toggleMediaSelection(slide.id);
                                      }}
                                    >
                                      <div className="flex items-center gap-2 w-full">
                                        <Checkbox checked={localSelectedMedia.includes(slide.id)} />
                                        <div className="mr-2 h-8 w-8 overflow-hidden rounded border border-border flex-shrink-0 bg-muted flex items-center justify-center">
                                          <Presentation className="h-4 w-4" />
                                        </div>
                                        <div className="flex-1 overflow-hidden">
                                          <p className="truncate">{slide.name}</p>
                                          <p className="text-xs text-muted-foreground truncate">
                                            Slide
                                          </p>
                                        </div>
                                      </div>
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            )}
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div className="border rounded-md">
                    {mediaDisplayOrder.length === 0 ? (
                      <div className="p-8 text-center">
                        <ImageIcon className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">
                          No content selected. Add media or slides to display in this campaign.
                        </p>
                      </div>
                    ) : (
                      <div className="divide-y">
                        {mediaDisplayOrder.map((mediaId, index) => {
                          // Find the media item or slide
                          const mediaItem = mediaItems?.find(m => m.id === mediaId);
                          const slideItem = slides?.find(s => s.id === mediaId);
                          const item = mediaItem || slideItem;
                          
                          if (!item) return null;
                          
                          const isSlide = !!slideItem;
                          
                          return (
                            <div key={mediaId} className="flex items-center p-3 hover:bg-muted/30">
                              <div className="mr-3 font-mono text-xs bg-muted text-muted-foreground h-6 w-6 rounded-full flex items-center justify-center">
                                {index + 1}
                              </div>
                              {isSlide ? (
                                <div className="mr-3 h-10 w-10 overflow-hidden rounded border border-border flex-shrink-0 bg-muted flex items-center justify-center">
                                  <Presentation className="h-5 w-5" />
                                </div>
                              ) : (
                                <div className="mr-3 h-10 w-10 overflow-hidden rounded border border-border flex-shrink-0">
                                  <img 
                                    src={mediaItem.thumbnailUrl || mediaItem.fileUrl} 
                                    alt={mediaItem.name} 
                                    className="h-full w-full object-cover"
                                  />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{item.name}</p>
                                <p className="text-xs text-muted-foreground truncate">
                                  {isSlide ? "Slide" : mediaItem.fileType.split("/")[0]}
                                </p>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-7 w-7" 
                                  title="Move up"
                                  onClick={() => moveMediaUp(mediaId)}
                                  disabled={index === 0}
                                >
                                  <ArrowUp className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-7 w-7" 
                                  title="Move down"
                                  onClick={() => moveMediaDown(mediaId)}
                                  disabled={index === mediaDisplayOrder.length - 1}
                                >
                                  <ArrowDown className="h-4 w-4" />
                                </Button>
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-7 w-7 text-muted-foreground hover:text-destructive" 
                                  onClick={() => toggleMediaSelection(mediaId)}
                                  title="Remove"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
              
              {/* Screens Tab */}
              <TabsContent value="screens" className="space-y-6 py-4">
                <div className="flex flex-col space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-base font-medium">Selected Screens ({selectedScreens.length})</h3>
                    <Popover open={screenSearchOpen} onOpenChange={setScreenSearchOpen}>
                      <PopoverTrigger asChild>
                        <Button variant="outline" size="sm">
                          <PlusCircle className="h-4 w-4 mr-1" /> Add Screens
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80 p-0" align="end">
                        <Command>
                          <CommandInput placeholder="Search screens..." value={screenSearchQuery} onValueChange={setScreenSearchQuery} />
                          
                          <div className="border-t border-border p-2">
                            <div className="mb-2">
                              <p className="text-xs text-muted-foreground mb-1">Status</p>
                              <div className="flex flex-wrap gap-1 mb-1">
                                <Button 
                                  variant={selectedScreenStatusTag === "" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedScreenStatusTag("")}
                                  className="h-7 text-xs"
                                >
                                  All
                                </Button>
                                <Button 
                                  variant={selectedScreenStatusTag === "online" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedScreenStatusTag("online")}
                                  className="h-7 text-xs"
                                >
                                  Online
                                </Button>
                                <Button 
                                  variant={selectedScreenStatusTag === "offline" ? "secondary" : "outline"} 
                                  size="sm" 
                                  onClick={() => setSelectedScreenStatusTag("offline")}
                                  className="h-7 text-xs"
                                >
                                  Offline
                                </Button>
                              </div>
                            </div>
                            
                            {/* Screen tag filter */}
                            {screenTags.length > 0 && (
                              <div className="mb-2">
                                <p className="text-xs text-muted-foreground mb-1">Tags</p>
                                <div className="flex flex-wrap gap-1 mb-1">
                                  <Button 
                                    variant={selectedScreenTag === "" ? "secondary" : "outline"} 
                                    size="sm" 
                                    onClick={() => setSelectedScreenTag("")}
                                    className="h-7 text-xs"
                                  >
                                    All Tags
                                  </Button>
                                  {screenTags.map(tag => (
                                    <Button 
                                      key={tag.id}
                                      variant={selectedScreenTag === tag.id ? "secondary" : "outline"} 
                                      size="sm" 
                                      onClick={() => setSelectedScreenTag(tag.id)}
                                      className="h-7 text-xs"
                                    >
                                      {tag.name}
                                    </Button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <CommandList>
                            <CommandEmpty>No screens found.</CommandEmpty>
                            <CommandGroup>
                              {screens
                                ?.filter(screen => {
                                  // Filter by search query
                                  const matchesQuery = !screenSearchQuery || 
                                    screen.name.toLowerCase().includes(screenSearchQuery.toLowerCase()) ||
                                    (screen.location && screen.location.toLowerCase().includes(screenSearchQuery.toLowerCase()));
                                  
                                  // Filter by status tag
                                  const matchesStatusTag = !selectedScreenStatusTag || screen.status === selectedScreenStatusTag;
                                  
                                  // Filter by screen tag
                                  const matchesScreenTag = !selectedScreenTag || 
                                    (screenTags.find(tag => tag.id === selectedScreenTag)?.screens || []).includes(screen.id);
                                  
                                  return matchesQuery && matchesStatusTag && matchesScreenTag;
                                })
                                .map(screen => (
                                  <CommandItem
                                    key={screen.id}
                                    onSelect={() => {
                                      toggleScreenSelection(screen.id);
                                    }}
                                  >
                                    <div className="flex items-center gap-2 w-full">
                                      <Checkbox checked={selectedScreens.includes(screen.id)} />
                                      <div className="flex-1 overflow-hidden">
                                        <p className="truncate">{screen.name}</p>
                                        {screen.location && (
                                          <p className="text-xs text-muted-foreground truncate">
                                            {screen.location}
                                          </p>
                                        )}
                                      </div>
                                      <Badge 
                                        variant="outline"
                                        className="ml-auto text-[10px] h-5"
                                      >
                                        {screen.status}
                                      </Badge>
                                    </div>
                                  </CommandItem>
                                ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  </div>
                  
                  <div className="border rounded-md">
                    {selectedScreens.length === 0 ? (
                      <div className="p-8 text-center">
                        <Monitor className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">
                          No screens selected. Add screens to display this campaign.
                        </p>
                      </div>
                    ) : (
                      <div className="divide-y">
                        {screens
                          ?.filter(screen => selectedScreens.includes(screen.id))
                          .map(screen => (
                            <div key={screen.id} className="flex items-center p-3 hover:bg-muted/30">
                              <div className="mr-2 h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                <Monitor className="h-4 w-4 text-foreground" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">{screen.name}</p>
                                {screen.location && (
                                  <p className="text-xs text-muted-foreground truncate">
                                    {screen.location}
                                  </p>
                                )}
                              </div>
                              <Badge 
                                variant="outline"
                                className="mr-2"
                              >
                                {screen.status}
                              </Badge>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-7 w-7 text-muted-foreground hover:text-destructive"
                                onClick={() => toggleScreenSelection(screen.id)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Save Changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}