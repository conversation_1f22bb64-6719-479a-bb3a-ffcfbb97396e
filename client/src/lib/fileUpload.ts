import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Storage bucket name
const STORAGE_BUCKET = 'medialibrary';

// Supported file types
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/svg+xml',
  'image/webp'
];

export const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/webm',
  'video/ogg'
];

export const SUPPORTED_AUDIO_TYPES = [
  'audio/mpeg',
  'audio/ogg',
  'audio/wav',
  'audio/webm'
];

export const SUPPORTED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation'
];

export const SUPPORTED_FILE_TYPES = [
  ...SUPPORTED_IMAGE_TYPES,
  ...SUPPORTED_VIDEO_TYPES,
  ...SUPPORTED_AUDIO_TYPES,
  ...SUPPORTED_DOCUMENT_TYPES
];

// Maximum file size (10MB)
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

type ProgressCallback = (progress: number) => void;

interface FileMetadata {
  width?: number;
  height?: number;
  duration?: number;
}

/**
 * Upload a file to Supabase Storage
 * @param file - The file to upload
 * @param teamId - The team ID for the folder structure
 * @param onProgress - Optional progress callback
 * @returns The file URL and metadata
 */
export async function uploadFile(
  file: File, 
  teamId: string, 
  onProgress?: ProgressCallback
): Promise<{
  fileUrl: string;
  thumbnailUrl?: string;
  fileType: string;
  fileSize: number;
  width?: number;
  height?: number;
  duration?: number;
}> {
  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds the maximum allowed size of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
  }

  if (!SUPPORTED_FILE_TYPES.includes(file.type)) {
    throw new Error(`File type ${file.type} is not supported`);
  }

  try {
    // Generate a unique filename
    const fileExt = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExt}`;
    const filePath = `${teamId}/${fileName}`;

    // Start extracting metadata from the file
    const metadataPromise = extractFileMetadata(file);

    // Upload the file to Supabase
    const { data, error } = await supabase.storage
      .from(STORAGE_BUCKET)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      throw new Error(`Error uploading file: ${error.message}`);
    }

    // Get the file URL
    const fileUrlResult = supabase.storage
      .from(STORAGE_BUCKET)
      .getPublicUrl(filePath);

    const fileUrl = fileUrlResult.data.publicUrl;

    // Generate thumbnail if it's an image or video
    let thumbnailUrl: string | undefined;
    if (SUPPORTED_IMAGE_TYPES.includes(file.type)) {
      // For images, we can use a resized version as the thumbnail
      thumbnailUrl = fileUrl;
    } else if (SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      // For videos, we should generate a thumbnail
      // This would ideally be done server-side but for now we'll leave it undefined
      thumbnailUrl = undefined;
    }

    // Get the metadata
    const metadata = await metadataPromise;

    return {
      fileUrl,
      thumbnailUrl,
      fileType: file.type,
      fileSize: file.size,
      ...metadata
    };
  } catch (error) {
    console.error('Error in uploadFile:', error);
    throw error;
  }
}

/**
 * Extract metadata from a file (dimensions for images, duration for audio/video)
 * @param file - The file to extract metadata from
 * @returns Object containing width, height, and/or duration
 */
export async function extractFileMetadata(file: File): Promise<FileMetadata> {
  const metadata: FileMetadata = {};

  if (SUPPORTED_IMAGE_TYPES.includes(file.type)) {
    // Extract image dimensions
    const dimensions = await getImageDimensions(file);
    metadata.width = dimensions.width;
    metadata.height = dimensions.height;
  } else if (SUPPORTED_VIDEO_TYPES.includes(file.type) || SUPPORTED_AUDIO_TYPES.includes(file.type)) {
    // Extract audio/video duration
    const duration = await getMediaDuration(file);
    metadata.duration = duration;
    
    // For videos, also get dimensions
    if (SUPPORTED_VIDEO_TYPES.includes(file.type)) {
      const dimensions = await getVideoDimensions(file);
      metadata.width = dimensions.width;
      metadata.height = dimensions.height;
    }
  }

  return metadata;
}

/**
 * Get the dimensions of an image
 * @param file - The image file
 * @returns Promise resolving to width and height
 */
function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      });
      URL.revokeObjectURL(img.src); // Clean up
    };
    img.onerror = () => {
      reject(new Error('Failed to load image'));
      URL.revokeObjectURL(img.src); // Clean up
    };
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Get the duration of an audio or video file
 * @param file - The media file
 * @returns Promise resolving to duration in seconds
 */
function getMediaDuration(file: File): Promise<number> {
  return new Promise((resolve, reject) => {
    const media = SUPPORTED_VIDEO_TYPES.includes(file.type) 
      ? document.createElement('video') 
      : document.createElement('audio');
    
    media.onloadedmetadata = () => {
      resolve(media.duration);
      URL.revokeObjectURL(media.src); // Clean up
    };
    media.onerror = () => {
      reject(new Error('Failed to load media'));
      URL.revokeObjectURL(media.src); // Clean up
    };
    media.src = URL.createObjectURL(file);
  });
}

/**
 * Get the dimensions of a video
 * @param file - The video file
 * @returns Promise resolving to width and height
 */
function getVideoDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    video.onloadedmetadata = () => {
      resolve({
        width: video.videoWidth,
        height: video.videoHeight
      });
      URL.revokeObjectURL(video.src); // Clean up
    };
    video.onerror = () => {
      reject(new Error('Failed to load video'));
      URL.revokeObjectURL(video.src); // Clean up
    };
    video.src = URL.createObjectURL(file);
  });
}

/**
 * Generate the SQL script to create the storage bucket
 * This function returns the SQL script that can be run in the Supabase SQL Editor
 */
export function generateStorageBucketScript(): string {
  return `
  -- Create a storage bucket for media files
  INSERT INTO storage.buckets (id, name, public)
  VALUES ('medialibrary', 'Media Library', true)
  ON CONFLICT (id) DO NOTHING;

  -- Create a policy for authenticated users to read any files
  CREATE POLICY "Media Library Read Policy"
  ON storage.objects FOR SELECT
  TO authenticated
  USING (bucket_id = 'medialibrary');

  -- Create a policy to allow authenticated users to upload files
  CREATE POLICY "Media Library Insert Policy"
  ON storage.objects FOR INSERT
  TO authenticated
  WITH CHECK (bucket_id = 'medialibrary' AND (auth.uid())::text = (storage.foldername(name))[1]);

  -- Create a policy to allow authenticated users to update their own files
  CREATE POLICY "Media Library Update Policy"
  ON storage.objects FOR UPDATE
  TO authenticated
  USING (bucket_id = 'medialibrary' AND (auth.uid())::text = (storage.foldername(name))[1]);

  -- Create a policy to allow authenticated users to delete their own files
  CREATE POLICY "Media Library Delete Policy"
  ON storage.objects FOR DELETE
  TO authenticated
  USING (bucket_id = 'medialibrary' AND (auth.uid())::text = (storage.foldername(name))[1]);
  `;
}