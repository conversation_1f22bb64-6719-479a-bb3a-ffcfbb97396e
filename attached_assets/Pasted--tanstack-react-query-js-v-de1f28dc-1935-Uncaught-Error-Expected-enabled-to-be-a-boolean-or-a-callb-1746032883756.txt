@tanstack_react-query.js?v=de1f28dc:1935 Uncaught Error: Expected enabled to be a boolean or a callback that returns a boolean
    at useScreenCampaigns (use-screen-campaigns.ts:37:7)
    at ScreenCampaignsModal (screen-campaigns-modal.tsx:49:7)

@tanstack_react-query.js?v=de1f28dc:1935 Uncaught Error: Expected enabled to be a boolean or a callback that returns a boolean
    at useScreenCampaigns (use-screen-campaigns.ts:37:7)
    at ScreenCampaignsModal (screen-campaigns-modal.tsx:49:7)
Console.js:61 The above error occurred in the <ScreenCampaignsModal> component:

    at ScreenCampaignsModal (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/screens/screen-campaigns-modal.tsx:42:3)
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Screens (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/screens.tsx:77:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?v=6GvrBNziHqjd9CWQMKwEx:32:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=de1f28dc:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=de1f28dc:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:39:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=de1f28dc:2805:3)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-276SZO74.js?v=de1f28dc:9129 Uncaught Error: Expected enabled to be a boolean or a callback that returns a boolean
    at useScreenCampaigns (use-screen-campaigns.ts:37:7)
    at ScreenCampaignsModal (screen-campaigns-modal.tsx:49:7)
﻿
