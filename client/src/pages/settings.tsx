import { useState, useEffect, useRef } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useLocation } from "wouter";

import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { TagInput } from "@/components/media/tag-input";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { getInitials } from "@/lib/utils";
import {
  User,
  Lock,
  Bell,
  Users,
  CreditCard,
  Tag,
  Upload,
  Pencil,
  X,
  Plus,
  Check,
  Edit,
  Trash2,
  Monitor,
  ShieldAlert,
} from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/hooks/use-toast";
import { useBilling } from "@/hooks/use-billing";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { BuyCreditsModal } from "@/components/billing/buy-credits-modal";
import { BillingHistory } from "@/components/billing/billing-history";
import { ScreenSubscriptions } from "@/components/billing/screen-subscriptions";

const profileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Please enter a valid email"),
  company: z.string().optional(),
});

const securitySchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Password must be at least 8 characters"),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

const tagSchema = z.object({
  name: z.string().min(1, "Tag name is required"),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Please enter a valid hex color"),
});

type ProfileFormValues = z.infer<typeof profileSchema>;
type SecurityFormValues = z.infer<typeof securitySchema>;
type TagFormValues = z.infer<typeof tagSchema>;

export default function Settings() {
  const { user, updatePassword, signOut } = useAuth();
  const [location] = useLocation();
  const { toast } = useToast();
  const [teamId, setTeamId] = useState<string>("");
  const [activeTab, setActiveTab] = useState("account");
  const [editingTag, setEditingTag] = useState<string | null>(null);
  const [isAvatarDialogOpen, setIsAvatarDialogOpen] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState("");
  const [isBuyCreditsModalOpen, setIsBuyCreditsModalOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    // Set the active tab based on the URL
    if (location === "/settings/tags") {
      setActiveTab("tags");
    }
  }, [location]);

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });

  // Get current team
  const { data: team } = useQuery<any>({
    queryKey: [`/api/teams/${teamId}`],
    enabled: !!teamId,
  });

  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  // Get user profile
  const { data: profile } = useQuery<any>({
    queryKey: [`/api/profiles/${user?.id}`],
    enabled: !!user?.id,
  });

  // Get team members
  const { data: teamMembers, isLoading: teamMembersLoading } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/members`],
    enabled: !!teamId,
  });

  // Get tags
  const {
    data: tags,
    isLoading: tagsLoading,
    refetch: refetchTags
  } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/tags`],
    enabled: !!teamId,
  });

  // Get billing data
  const { billingSummary } = useBilling(teamId);

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      company: "",
    },
  });

  const securityForm = useForm<SecurityFormValues>({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const tagForm = useForm<TagFormValues>({
    resolver: zodResolver(tagSchema),
    defaultValues: {
      name: "",
      color: "#3b82f6",
    },
  });

  // Update forms when data is loaded
  useEffect(() => {
    if (profile && team) {
      profileForm.reset({
        firstName: profile.firstName || "",
        lastName: profile.lastName || "",
        email: profile.email || "",
        company: team.name || "",
      });

      // Set the avatar URL if it exists, but filter out any blob URLs
      if (profile.avatarUrl) {
        // Validate that the avatarUrl is not a blob URL (which won't persist)
        if (profile.avatarUrl.startsWith('blob:')) {
          // If it's a blob URL, clear it since it's no longer valid
          // But don't try to automatically update, as this might fail
          setAvatarUrl("");
        } else {
          // It's a valid URL (like a data URL), so use it
          setAvatarUrl(profile.avatarUrl);
        }
      }
    }
  }, [profile, team, profileForm, user?.id, queryClient]);

  const onProfileSubmit = async (data: ProfileFormValues) => {
    try {
      if (!user?.id || !teamId) return;

      // Ensure the profile exists
      await ensureProfileExists();

      try {
        // Also try direct Supabase approach as a primary option
        const supabase = (window as any).supabase;
        if (supabase) {
          await supabase.from('profiles')
            .update({
              first_name: data.firstName,
              last_name: data.lastName
            })
            .eq('id', user.id);
        }
      } catch (err) {
        console.error("Direct profile update error:", err);
      }

      // Then try API approach as a backup
      try {
        // Update profile information
        await apiRequest('PATCH', `/api/profiles/${user.id}`, {
          firstName: data.firstName,
          lastName: data.lastName,
          // Email updates might require verification, so we might not update it directly
        });
      } catch (err) {
        console.error("API profile update error:", err);
      }

      // Update team name if it has changed
      if (team && team.name !== data.company) {
        try {
          await apiRequest('PATCH', `/api/teams/${teamId}`, {
            name: data.company
          });

          // Invalidate team cache to refresh the data
          queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}`] });
        } catch (err) {
          console.error("Team update error:", err);
        }
      }

      // Refresh user data
      queryClient.invalidateQueries({ queryKey: [`/api/profiles/${user.id}`] });

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error updating your profile",
        variant: "destructive",
      });
    }
  };

  // Helper function to ensure profile exists
  const ensureProfileExists = async () => {
    if (!user?.id || !user?.email) return false;

    try {
      // Try direct Supabase approach
      const supabase = (window as any).supabase;
      if (supabase) {
        // Create/update the profile with Supabase directly
        const { error } = await supabase
          .from('profiles')
          .upsert([{
            id: user.id,
            email: user.email,
            first_name: user.firstName || '',
            last_name: user.lastName || '',
            avatar_url: profile?.avatarUrl || null // Use profile data first since that should come from our API
          }]);

        return !error;
      }

      return false;
    } catch (error) {
      console.error("Error ensuring profile exists:", error);
      return false;
    }
  };

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      if (!user?.id) return;

      // Read the file as a data URL (base64)
      const reader = new FileReader();

      reader.onload = async (event) => {
        if (!event.target?.result) return;

        // Use a data URL (base64) for the avatar so it persists
        const dataUrl = event.target.result as string;

        // Update UI optimistically
        setAvatarUrl(dataUrl);

        try {
          // First ensure the profile exists
          await ensureProfileExists();

          // Try direct Supabase approach as a primary option
          let directUpdateSuccess = false;
          try {
            const supabase = (window as any).supabase;
            if (supabase) {
              const { error } = await supabase.from('profiles')
                .update({ avatar_url: dataUrl })
                .eq('id', user.id);

              directUpdateSuccess = !error;
            }
          } catch (err) {
            console.error("Direct avatar update error:", err);
          }

          // If direct approach failed or not available, try API approach
          if (!directUpdateSuccess) {
            // Then update the profile with the new avatar URL
            await apiRequest('PATCH', `/api/profiles/${user.id}`, {
              avatarUrl: dataUrl
            });
          }

          // Invalidate profile cache to refresh the data
          queryClient.invalidateQueries({ queryKey: [`/api/profiles/${user.id}`] });

          toast({
            title: "Avatar updated",
            description: "Your profile photo has been updated successfully",
          });
        } catch (error: any) {
          let errorMessage = "There was an error updating your profile with the new photo";

          // Check if it's a Profile not found error
          if (error?.message?.includes("Profile not found")) {
            errorMessage = "Your profile hasn't been created yet. Please try refreshing the page and try again.";
          }

          toast({
            title: "Error",
            description: errorMessage,
            variant: "destructive",
          });
        }
      };

      reader.onerror = () => {
        toast({
          title: "Error",
          description: "There was an error reading the image file",
          variant: "destructive",
        });
      };

      // Read the file as data URL (base64)
      reader.readAsDataURL(file);
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error processing your photo",
        variant: "destructive",
      });
    }
  };

  const handleRemoveAvatar = async () => {
    try {
      if (!user?.id) return;

      // Clear the avatar URL (optimistic update)
      setAvatarUrl("");

      try {
        // First ensure the profile exists
        await ensureProfileExists();

        // Update the profile
        await apiRequest('PATCH', `/api/profiles/${user.id}`, {
          avatarUrl: null
        });

        // Also try direct Supabase approach as a backup
        try {
          const supabase = (window as any).supabase;
          if (supabase) {
            await supabase.from('profiles')
              .update({ avatar_url: null })
              .eq('id', user.id);
          }
        } catch (err) {
          console.error("Backup avatar removal error:", err);
        }

        // Invalidate profile cache to refresh the data
        queryClient.invalidateQueries({ queryKey: [`/api/profiles/${user.id}`] });

        toast({
          title: "Avatar removed",
          description: "Your profile photo has been removed",
        });
      } catch (error: any) {
        let errorMessage = "There was an error removing your photo";

        // Check if it's a Profile not found error
        if (error?.message?.includes("Profile not found")) {
          errorMessage = "Your profile hasn't been created yet. Please try refreshing the page and try again.";
        }

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error processing your request",
        variant: "destructive",
      });
    }
  };

  const onSecuritySubmit = async (data: SecurityFormValues) => {
    try {
      // Use the updatePassword method from the auth hook to update the password
      await updatePassword(data.currentPassword, data.newPassword);

      // Reset the form after successful update
      securityForm.reset({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error) {
      // Display form errors if needed
      if (error instanceof Error && error.message === "Current password is incorrect") {
        securityForm.setError("currentPassword", {
          type: "manual",
          message: "Current password is incorrect"
        });
      } else {
        // Other errors are already handled in the updatePassword method
        console.error("Form submission error:", error);
      }
    }
  };

  const onTagSubmit = async (data: TagFormValues) => {
    try {
      if (!teamId) return;

      if (editingTag) {
        await apiRequest('PATCH', `/api/tags/${editingTag}`, {
          name: data.name,
          color: data.color
        });

        toast({
          title: "Tag updated",
          description: "The tag has been updated successfully",
        });
      } else {
        await apiRequest('POST', `/api/tags`, {
          teamId,
          name: data.name,
          color: data.color
        });

        toast({
          title: "Tag created",
          description: "The tag has been created successfully",
        });
      }

      tagForm.reset({
        name: "",
        color: "#3b82f6",
      });

      setEditingTag(null);
      refetchTags();
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error saving the tag",
        variant: "destructive",
      });
    }
  };

  const handleEditTag = (tag: any) => {
    setEditingTag(tag.id);
    tagForm.reset({
      name: tag.name,
      color: tag.color || "#3b82f6",
    });
  };

  const handleDeleteTag = async (tagId: string) => {
    try {
      await apiRequest('DELETE', `/api/tags/${tagId}`);

      toast({
        title: "Tag deleted",
        description: "The tag has been deleted successfully",
      });

      refetchTags();
    } catch (error) {
      toast({
        title: "Error",
        description: "There was an error deleting the tag",
        variant: "destructive",
      });
    }
  };

  const handleCancelTagEdit = () => {
    setEditingTag(null);
    tagForm.reset({
      name: "",
      color: "#3b82f6",
    });
  };

  return (
    <MainLayout>
      <div className="flex flex-col space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Settings</h2>
          <p className="text-muted-foreground">Manage your account and preferences</p>
        </div>

        <Tabs defaultValue="account" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 md:grid-cols-5 w-full">
            <TabsTrigger value="account" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span className="hidden md:inline">Account</span>
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              <span className="hidden md:inline">Security</span>
            </TabsTrigger>
            <TabsTrigger value="team" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span className="hidden md:inline">Team</span>
            </TabsTrigger>
            <TabsTrigger value="billing" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className="hidden md:inline">Billing</span>
            </TabsTrigger>
            <TabsTrigger value="tags" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              <span className="hidden md:inline">Tags</span>
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="account">
              <Card>
                <CardHeader>
                  <CardTitle>Profile</CardTitle>
                  <CardDescription>
                    Manage your account information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-20 w-20">
                      <AvatarImage src={profile?.avatarUrl} />
                      <AvatarFallback className="text-lg">
                        {getInitials(`${profile?.firstName || ""} ${profile?.lastName || ""}`)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="space-y-2">
                      <h3 className="font-medium">Profile Photo</h3>
                      <div className="flex space-x-2">
                        <input
                          type="file"
                          ref={fileInputRef}
                          className="hidden"
                          accept="image/*"
                          onChange={handleAvatarChange}
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Change
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleRemoveAvatar}
                          disabled={!profile?.avatarUrl}
                        >
                          <X className="mr-2 h-4 w-4" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <Form {...profileForm}>
                    <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={profileForm.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={profileForm.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input {...field} disabled />
                            </FormControl>
                            <FormDescription>
                              Your email address cannot be changed
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={profileForm.control}
                        name="company"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Company</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button type="submit">Save Changes</Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>Security</CardTitle>
                  <CardDescription>
                    Update your password and security settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Form {...securityForm}>
                    <form onSubmit={securityForm.handleSubmit(onSecuritySubmit)} className="space-y-4">
                      <FormField
                        control={securityForm.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Current Password</FormLabel>
                            <FormControl>
                              <Input type="password" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={securityForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>New Password</FormLabel>
                            <FormControl>
                              <Input type="password" {...field} />
                            </FormControl>
                            <FormDescription>
                              Password must be at least 8 characters
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={securityForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm New Password</FormLabel>
                            <FormControl>
                              <Input type="password" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button type="submit">Update Password</Button>
                      </div>
                    </form>
                  </Form>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">Active Sessions</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Manage your active login sessions across devices
                    </p>

                    <div className="rounded-md border p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="rounded-full bg-primary/10 p-2">
                            <Monitor className="h-5 w-5" />
                          </div>
                          <div>
                            <p className="font-medium">Current Session</p>
                            <p className="text-sm text-muted-foreground">
                              {/* Show device info - fallback to browser detection */}
                              {typeof navigator !== 'undefined' ? navigator.userAgent.split(' ').slice(-1)[0] : 'Unknown Browser'}
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>
                      </div>
                      <div className="mt-4 flex justify-between items-center">
                        <p className="text-sm text-muted-foreground">
                          Signed in: {new Date().toLocaleDateString()}
                        </p>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="sm">Sign Out</Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure you want to sign out?</AlertDialogTitle>
                              <AlertDialogDescription>
                                You will be redirected to the login page after signing out.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={async () => {
                                try {
                                  await signOut();
                                  toast({
                                    title: "Signed out",
                                    description: "You have been signed out successfully"
                                  });
                                } catch (error) {
                                  console.error("Error signing out:", error);
                                  toast({
                                    title: "Error",
                                    description: "There was an error signing out",
                                    variant: "destructive"
                                  });
                                }
                              }}>Sign Out</AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>

                    <div className="mt-6">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={async () => {
                          try {
                            await signOut();
                            toast({
                              title: "Signed out from all devices",
                              description: "You have been signed out from all devices successfully"
                            });
                          } catch (error) {
                            console.error("Error signing out from all devices:", error);
                            toast({
                              title: "Error",
                              description: "There was an error signing out from all devices",
                              variant: "destructive"
                            });
                          }
                        }}
                      >
                        <ShieldAlert className="mr-2 h-4 w-4" />
                        Sign Out From All Devices
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Notifications tab removed as requested */}

            <TabsContent value="team">
              <Card>
                <CardHeader>
                  <CardTitle>Team Management</CardTitle>
                  <CardDescription>
                    Manage your team members and their permissions
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Team Members</h3>
                    <Button>
                      <Plus className="mr-2 h-4 w-4" />
                      Invite Member
                    </Button>
                  </div>

                  {teamMembersLoading ? (
                    <div className="py-4 text-center">
                      <p className="text-muted-foreground">Loading team members...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {teamMembers?.map((member: any) => (
                        <div key={member.profileId} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center space-x-4">
                            <Avatar>
                              <AvatarFallback>
                                {getInitials(member.profile?.firstName + " " + member.profile?.lastName)}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{member.profile?.firstName} {member.profile?.lastName}</p>
                              <p className="text-sm text-muted-foreground">{member.profile?.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={member.role === "owner" ? "default" : "outline"}>
                              {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
                            </Badge>
                            {member.role !== "owner" && (
                              <Button variant="ghost" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="billing">
              <Card>
                <CardHeader>
                  <CardTitle>Billing & Subscription</CardTitle>
                  <CardDescription>
                    Manage your subscription credits and billing information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Credits Summary */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium">Credits Overview</h3>
                      <Button onClick={() => setIsBuyCreditsModalOpen(true)}>
                        Buy Credits
                      </Button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-green-600">
                          {billingSummary?.totalCreditsBought || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Credits Bought</div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-red-600">
                          {billingSummary?.totalCreditsUsed || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Credits Used</div>
                      </div>

                      <div className="p-4 border rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">
                          {billingSummary?.creditsAvailable || 0}
                        </div>
                        <div className="text-sm text-muted-foreground">Credits Available</div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Billing Tabs */}
                  <div>
                    <Tabs defaultValue="billing-history" className="w-full">
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="billing-history">Billing History</TabsTrigger>
                        <TabsTrigger value="screen-subscriptions">Screen Subscriptions</TabsTrigger>
                      </TabsList>

                      <TabsContent value="billing-history" className="mt-6">
                        <BillingHistory teamId={teamId} teamName={team?.name || ""} />
                      </TabsContent>

                      <TabsContent value="screen-subscriptions" className="mt-6">
                        <ScreenSubscriptions teamId={teamId} teamName={team?.name || ""} />
                      </TabsContent>
                    </Tabs>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="tags">
              <Card>
                <CardHeader>
                  <CardTitle>Tag Management</CardTitle>
                  <CardDescription>
                    Create and manage tags to organize your content
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Form {...tagForm}>
                    <form onSubmit={tagForm.handleSubmit(onTagSubmit)} className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="md:col-span-2">
                          <FormField
                            control={tagForm.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Tag Name</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="e.g. Promotion, Sale, Holiday" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={tagForm.control}
                          name="color"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Color</FormLabel>
                              <FormControl>
                                <div className="flex gap-2">
                                  <Input {...field} placeholder="#3b82f6" />
                                  <div
                                    className="h-10 w-10 rounded-md border"
                                    style={{ backgroundColor: field.value }}
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="flex justify-end space-x-2">
                        {editingTag && (
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleCancelTagEdit}
                          >
                            Cancel
                          </Button>
                        )}
                        <Button type="submit">
                          {editingTag ? "Update Tag" : "Create Tag"}
                        </Button>
                      </div>
                    </form>
                  </Form>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium mb-4">Existing Tags</h3>

                    {tagsLoading ? (
                      <div className="py-4 text-center">
                        <p className="text-muted-foreground">Loading tags...</p>
                      </div>
                    ) : !tags || tags.length === 0 ? (
                      <div className="py-4 text-center border rounded-lg">
                        <p className="text-muted-foreground">No tags created yet</p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {tags.map((tag) => (
                          <div key={tag.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center space-x-2">
                              <div
                                className="h-4 w-4 rounded-full"
                                style={{ backgroundColor: tag.color || "#3b82f6" }}
                              />
                              <span>{tag.name}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleEditTag(tag)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-destructive hover:text-destructive"
                                onClick={() => handleDeleteTag(tag.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Buy Credits Modal */}
      <BuyCreditsModal
        isOpen={isBuyCreditsModalOpen}
        onClose={() => setIsBuyCreditsModalOpen(false)}
        teamId={teamId}
        teamName={team?.name || ""}
      />
    </MainLayout>
  );
}
