﻿
Console.js:61 Updating text element 16414db1-ea28-4630-b163-7272eb6bd6df with field "productname" at index 0 (global: 0)
Console.js:61 Field value: "Jarritos <PERSON>iiiiii"
Console.js:61 Updated text element 16414db1-ea28-4630-b163-7272eb6bd6df with "Jarritos Tutti Fruttiiiiii" (index: 0)
Console.js:61 Setting up API data cycling interval with 5s duration
Console.js:61 API data cycling interval set with ID: 365
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 16414db1-ea28-4630-b163-7272eb6bd6df
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
5
The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Preview active: Looking for video elements to play
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 16414db1-ea28-4630-b163-7272eb6bd6df
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 16414db1-ea28-4630-b163-7272eb6bd6df
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 API cycling interval triggered - checking if preview still active
Console.js:61 Preview no longer active, will stop on next interval check
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 16414db1-ea28-4630-b163-7272eb6bd6df
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Clearing API cycling interval: 365
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview still active with API data, keeping intervals intact
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (global=0) for element 16414db1-ea28-4630-b163-7272eb6bd6df
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in data record at index 0 for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview still active with API data, keeping intervals intact
﻿

