import React from 'react';
import { X, Download, ExternalLink } from 'lucide-react';
import { formatFileSize, formatDuration } from '@/lib/utils';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

interface MediaPreviewModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  media: {
    id: string;
    name: string;
    fileUrl: string;
    fileType: string;
    fileSize: number;
    thumbnailUrl?: string;
    width?: number;
    height?: number;
    duration?: number;
  } | null;
}

export function MediaPreviewModal({ 
  open, 
  onOpenChange, 
  media 
}: MediaPreviewModalProps) {
  if (!media) return null;

  const { name, fileUrl, fileType, fileSize, width, height, duration } = media;
  
  const isImage = fileType.startsWith('image/');
  const isVideo = fileType.startsWith('video/');
  const isAudio = fileType.startsWith('audio/');
  const isPdf = fileType === 'application/pdf';
  
  const handleDownload = () => {
    // Create an anchor element and trigger download
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl w-full p-0 overflow-hidden bg-background">
        <DialogHeader className="p-4 flex flex-row items-center justify-between border-b">
          <DialogTitle className="truncate pr-4">{name}</DialogTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" size="sm" asChild>
              <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open
              </a>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onOpenChange(false)}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
        </DialogHeader>
        
        <div className="flex flex-col">
          <div className="flex-1 bg-muted/40 flex items-center justify-center p-4 min-h-[400px]">
            {isImage && (
              <div className="max-h-[70vh] w-full flex items-center justify-center">
                <img
                  src={fileUrl}
                  alt={name}
                  className="max-w-full max-h-[70vh] object-contain"
                />
              </div>
            )}
            
            {isVideo && (
              <div className="w-full h-full">
                <video
                  src={fileUrl}
                  controls
                  className="max-w-full max-h-[70vh]"
                  controlsList="nodownload"
                >
                  Your browser does not support the video tag.
                </video>
              </div>
            )}
            
            {isAudio && (
              <div className="w-full max-w-md p-6 bg-card rounded-md shadow">
                <div className="mb-4 text-center text-xl font-semibold">
                  {name}
                </div>
                <audio
                  src={fileUrl}
                  controls
                  className="w-full"
                  controlsList="nodownload"
                >
                  Your browser does not support the audio tag.
                </audio>
              </div>
            )}
            
            {isPdf && (
              <iframe
                src={`${fileUrl}#toolbar=0`}
                title={name}
                className="w-full h-[70vh]"
              />
            )}
            
            {!isImage && !isVideo && !isAudio && !isPdf && (
              <div className="text-center p-8">
                <div className="mb-4">
                  This file type cannot be previewed directly.
                </div>
                <Button onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download File
                </Button>
              </div>
            )}
          </div>
          
          <div className="p-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="font-medium">File type</div>
                <div className="text-muted-foreground">{fileType}</div>
              </div>
              <div>
                <div className="font-medium">File size</div>
                <div className="text-muted-foreground">{formatFileSize(fileSize)}</div>
              </div>
              {width && height && (
                <div>
                  <div className="font-medium">Dimensions</div>
                  <div className="text-muted-foreground">{width} × {height}px</div>
                </div>
              )}
              {duration && (
                <div>
                  <div className="font-medium">Duration</div>
                  <div className="text-muted-foreground">{formatDuration(duration)}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}