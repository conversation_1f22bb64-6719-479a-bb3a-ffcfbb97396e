import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { generateRandomCode } from "@/lib/utils";
import type { Screen, InsertScreen, Tag } from "@shared/schema";

// Define a type with tags for screens
interface EnhancedScreen extends Screen {
  tags: string[];
  lastSeen?: string | null; // For compatibility with screen cards
  // Enhanced fields from joins
  maxFileDownloadCount?: number;
  maxTotalFileDownload?: number;
  trialEndsAt?: Date;
  subscriptionStatus?: string;
  lemonsqueezySubscriptionId?: string;
  billingCycle?: string;
}

export function useScreens(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [selectedScreen, setSelectedScreen] = useState<string | null>(null);

  // Helper function to calculate screen status
  const calculateScreenStatus = (screen: Screen): "online" | "offline" => {
    if (!screen.lastPingAt) return "offline";

    const lastSeenDate = new Date(screen.lastPingAt);
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    return lastSeenDate > fiveMinutesAgo ? "online" : "offline";
  };

  // Fetch all screens with tags in a single optimized query using server-side join
  const screensWithTagsQuery = useQuery<EnhancedScreen[]>({
    queryKey: [`/api/teams/${teamId}/enhanced-screens-with-tags`],
    queryFn: async () => {
      if (!teamId) return [];

      // Use the enhanced endpoint with LEFT JOINs
      const response = await apiRequest('GET', `/api/teams/${teamId}/enhanced-screens-with-tags`);
      const screensWithTags = await response.json();

      if (!Array.isArray(screensWithTags)) {
        return [];
      }

      // Transform the data to include status calculation and lastSeen formatting
      return screensWithTags.map((screen: any) => ({
        ...screen,
        lastSeen: screen.lastPingAt ? new Date(screen.lastPingAt).toISOString() : null,
        status: calculateScreenStatus(screen)
      }));
    },
    enabled: !!teamId && teamId.trim() !== '',
    staleTime: 0, // Always fresh for real-time updates
    refetchOnWindowFocus: false,
  });

  // Simplified state management
  const screens = screensWithTagsQuery.data || [];
  const isLoading = screensWithTagsQuery.isLoading;
  const error = screensWithTagsQuery.error;

  // Get a single screen with tags
  const useScreen = (screenId: string | null) => {
    const [screenWithTags, setScreenWithTags] = useState<EnhancedScreen | null>(null);
    const [isTagsFetchLoading, setIsTagsFetchLoading] = useState(false);

    const {
      data: screen,
      isLoading: isItemLoading,
      error: itemError,
      refetch: refetchScreen
    } = useQuery<any>({
      queryKey: [`/api/screens/${screenId}/enhanced`],
      enabled: !!screenId,
      staleTime: 0, // Consider data stale immediately
      refetchOnMount: 'always', // Always refetch when component mounts
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
    });

    // Use React Query for tags
    const tagsQuery = useQuery<any[]>({
      queryKey: [`/api/screens/${screenId}/tags`],
      enabled: !!screenId,
      staleTime: 0, // Always fresh
      refetchOnWindowFocus: false,
    });

    // Combine screen data with tags whenever either updates
    useEffect(() => {
      if (screen && screenId) {
        const tags = Array.isArray(tagsQuery.data) ? tagsQuery.data : [];
        const tagNames = tags.map((tag: any) => tag.name || '');

        // Set the screen with tags and status
        setScreenWithTags({
          ...screen,
          tags: tagNames,
          lastSeen: screen.lastPingAt ? new Date(screen.lastPingAt).toISOString() : null,
          status: calculateScreenStatus(screen)
        });
      } else {
        setScreenWithTags(null);
      }
    }, [screen, tagsQuery.data, screenId]);

    // Update loading state
    useEffect(() => {
      setIsTagsFetchLoading(tagsQuery.isLoading);
    }, [tagsQuery.isLoading]);

    // Function to manually refresh data
    const refreshData = async () => {
      if (screenId) {
        setIsTagsFetchLoading(true);
        try {
          await refetchScreen();
          await tagsQuery.refetch();
        } finally {
          setIsTagsFetchLoading(false);
        }
      }
    };

    return {
      data: screenWithTags,
      isLoading: isItemLoading || tagsQuery.isLoading || isTagsFetchLoading,
      error: itemError,
      refreshData
    };
  };

  // Create a new screen with tags
  const createScreenMutation = useMutation({
    mutationFn: async (screenData: Omit<InsertScreen, "code" | "teamId"> & { tags?: string[] }) => {
      // Extract tags from screenData
      const { tags, ...screenDataWithoutTags } = screenData;

      // Create screen
      const newScreen: InsertScreen = {
        ...screenDataWithoutTags,
        teamId,
        code: generateRandomCode(5)
      };

      const createdScreen = await apiRequest('POST', `/api/screens`, newScreen);

      // If we have tags, add them
      if (tags && tags.length > 0) {
        await Promise.all(
          tags.map(async (tagName) => {
            // First find or create the tag
            try {
              // Check if tag already exists
              let allTags = await apiRequest('GET', `/api/teams/${teamId}/tags`);
              if (allTags instanceof Response) {
                allTags = await allTags.json();
              }

              let tagId;

              // Find matching tag (case-insensitive)
              const existingTag = Array.isArray(allTags) ?
                allTags.find((t: Tag) => t.name.toLowerCase() === tagName.toLowerCase()) :
                null;

              if (existingTag) {
                tagId = existingTag.id;
              } else {
                // Create new tag
                let newTag = await apiRequest('POST', `/api/tags`, {
                  name: tagName,
                  teamId,
                  color: "#" + Math.floor(Math.random()*16777215).toString(16) // Random color
                });

                if (newTag instanceof Response) {
                  newTag = await newTag.json();
                }

                tagId = newTag.id;
              }

              // Add tag to screen
              let screenId = createdScreen.id;
              if (createdScreen instanceof Response) {
                const screenData = await createdScreen.json();
                screenId = screenData.id;
              }

              await apiRequest('POST', `/api/screens/${screenId}/tags/${tagId}`, {});
            } catch (error) {
              console.error(`Error adding tag ${tagName} to screen:`, error);
            }
          })
        );
      }

      return createdScreen;
    },
    onSuccess: () => {
      // Invalidate the optimized screens-with-tags query
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/screens-with-tags`] });
      toast({
        title: "Screen created",
        description: "The screen has been created successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating screen",
        description: error.message || "An error occurred while creating the screen",
        variant: "destructive"
      });
    },
  });

  // Update a screen with tags
  const updateScreenMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<InsertScreen> & { tags?: string[] } }) => {

      // Extract tags from data
      const { tags, ...screenDataWithoutTags } = data;

      // Update screen basic data including updateguid
      const updatedScreen = await apiRequest('PATCH', `/api/screens/${id}`, {
        ...screenDataWithoutTags
      });

      console.log(updatedScreen);

      // If we have tags, handle them
      if (tags !== undefined) {
        try {
          // First get all existing tags for this screen
          let existingTags = await apiRequest('GET', `/api/screens/${id}/tags`);
          if (existingTags instanceof Response) {
            existingTags = await existingTags.json();
          }
          existingTags = Array.isArray(existingTags) ? existingTags : [];

          // Get all available tags for this team
          let allTeamTags = await apiRequest('GET', `/api/teams/${teamId}/tags`);
          if (allTeamTags instanceof Response) {
            allTeamTags = await allTeamTags.json();
          }
          allTeamTags = Array.isArray(allTeamTags) ? allTeamTags : [];

          // Remove tags that are no longer needed
          await Promise.all(
            existingTags.map(async (tag: Tag) => {
              if (!tags.some((tagName: string) => tagName.toLowerCase() === tag.name.toLowerCase())) {
                await apiRequest('DELETE', `/api/screens/${id}/tags/${tag.id}`);
              }
            })
          );

          // Add new tags
          await Promise.all(
            tags.map(async (tagName: string) => {
              // Check if tag is already associated with this screen
              const tagAlreadyExists = existingTags.some(
                (tag: Tag) => tag.name.toLowerCase() === tagName.toLowerCase()
              );

              if (!tagAlreadyExists) {
                // Find or create the tag
                let tagId;
                const existingTag = allTeamTags.find(
                  (t: Tag) => t.name.toLowerCase() === tagName.toLowerCase()
                );

                if (existingTag) {
                  tagId = existingTag.id;
                } else {
                  // Create new tag
                  let newTag = await apiRequest('POST', `/api/tags`, {
                    name: tagName,
                    teamId,
                    color: "#" + Math.floor(Math.random()*16777215).toString(16) // Random color
                  });

                  if (newTag instanceof Response) {
                    newTag = await newTag.json();
                  }

                  tagId = newTag.id;
                }

                // Add tag to screen
                await apiRequest('POST', `/api/screens/${id}/tags/${tagId}`, {});
              }
            })
          );
        } catch (error) {
          console.error("Error managing screen tags:", error);
        }
      }

      return updatedScreen;
    },
    onSuccess: (_, variables) => {
      const { id } = variables;
      // Invalidate the optimized screens-with-tags query and individual screen queries
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/screens-with-tags`] });
      queryClient.invalidateQueries({ queryKey: [`/api/screens/${id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/screens/${id}/tags`] });

      toast({
        title: "Screen updated",
        description: "The screen has been updated successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating screen",
        description: error.message || "An error occurred while updating the screen",
        variant: "destructive"
      });
    },
  });

  // Delete a screen
  const deleteScreenMutation = useMutation({
    mutationFn: async (screenId: string) => {
      await apiRequest('DELETE', `/api/screens/${screenId}`);
    },
    onSuccess: () => {
      // Invalidate the optimized screens-with-tags query
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/screens-with-tags`] });
      toast({
        title: "Screen deleted",
        description: "The screen has been deleted successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting screen",
        description: error.message || "An error occurred while deleting the screen",
        variant: "destructive"
      });
    },
  });

  // Simplified empty state logic
  const isEmpty = !isLoading && screens.length === 0;

  return {
    screens,
    isLoading,
    isEmpty,
    error,
    selectedScreen,
    setSelectedScreen,
    useScreen,
    refetchScreens: screensWithTagsQuery.refetch,
    createScreen: createScreenMutation.mutate,
    isCreating: createScreenMutation.isPending,
    updateScreen: updateScreenMutation.mutate,
    isUpdating: updateScreenMutation.isPending,
    deleteScreen: deleteScreenMutation.mutate,
    isDeleting: deleteScreenMutation.isPending,
  };
}
