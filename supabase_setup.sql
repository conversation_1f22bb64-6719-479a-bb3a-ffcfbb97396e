-- Digital Signage Content Management System Database Setup

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Profiles table to store user information
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  first_name TEXT,
  last_name TEX<PERSON>,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Teams table
CREATE TABLE IF NOT EXISTS public.teams (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  logo_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Team Members table (relationship between profiles and teams)
CREATE TABLE IF NOT EXISTS public.team_members (
  profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  PRIMARY KEY (profile_id, team_id)
);

-- Media Items table
CREATE TABLE IF NOT EXISTS public.media_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  file_url TEXT NOT NULL,
  thumbnail_url TEXT,
  file_type TEXT NOT NULL,
  file_size INTEGER,
  width INTEGER,
  height INTEGER,
  duration INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Screens table
CREATE TABLE IF NOT EXISTS public.screens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  location TEXT,
  code TEXT UNIQUE NOT NULL,
  status TEXT DEFAULT 'offline',
  last_ping_at TIMESTAMP WITH TIME ZONE,
  start_time TIME,
  end_time TIME,
  update_frequency TEXT,
  site_email TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Campaigns table
CREATE TABLE IF NOT EXISTS public.campaigns (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  start_date TIMESTAMP WITH TIME ZONE,
  end_date TIMESTAMP WITH TIME ZONE,
  status TEXT DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Campaign Media bridge table
CREATE TABLE IF NOT EXISTS public.campaign_medias (
  campaign_id UUID REFERENCES public.campaigns(id) ON DELETE CASCADE,
  media_id UUID REFERENCES public.media_items(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  PRIMARY KEY (campaign_id, media_id)
);

-- Campaign Screens bridge table
CREATE TABLE IF NOT EXISTS public.campaign_screens (
  campaign_id UUID REFERENCES public.campaigns(id) ON DELETE CASCADE,
  screen_id UUID REFERENCES public.screens(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  PRIMARY KEY (campaign_id, screen_id)
);

-- Slides table (for the slide designer)
CREATE TABLE IF NOT EXISTS public.slides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  content JSONB NOT NULL,
  thumbnail_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
);

-- Tags table
CREATE TABLE IF NOT EXISTS public.tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  color TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  UNIQUE (team_id, name)
);

-- Media Tags bridge table
CREATE TABLE IF NOT EXISTS public.media_tags (
  media_id UUID REFERENCES public.media_items(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  PRIMARY KEY (media_id, tag_id)
);

-- Screen Tags bridge table
CREATE TABLE IF NOT EXISTS public.screen_tags (
  screen_id UUID REFERENCES public.screens(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
  PRIMARY KEY (screen_id, tag_id)
);

-- Set up Row Level Security (RLS) policies
-- Profiles Table Policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- Teams Table Policies
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view their teams" ON public.teams
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = id AND profile_id = auth.uid()
    )
  );

CREATE POLICY "Team owners can insert new teams" ON public.teams
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = id AND profile_id = auth.uid() AND role = 'owner'
    )
  );

CREATE POLICY "Team owners can update their teams" ON public.teams
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = id AND profile_id = auth.uid() AND role = 'owner'
    )
  );

-- Team Members Table Policies
ALTER TABLE public.team_members ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view team member list" ON public.team_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.team_members AS tm
      WHERE tm.team_id = team_id AND tm.profile_id = auth.uid()
    )
  );

CREATE POLICY "Team owners can add team members" ON public.team_members
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.team_members AS tm
      WHERE tm.team_id = team_id AND tm.profile_id = auth.uid() AND tm.role = 'owner'
    )
  );

CREATE POLICY "Team owners can remove team members" ON public.team_members
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.team_members AS tm
      WHERE tm.team_id = team_id AND tm.profile_id = auth.uid() AND tm.role = 'owner'
    )
  );

-- Media Items Table Policies
ALTER TABLE public.media_items ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Team members can view media items" ON public.media_items
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = media_items.team_id AND profile_id = auth.uid()
    )
  );

CREATE POLICY "Team members can insert media items" ON public.media_items
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = media_items.team_id AND profile_id = auth.uid()
    )
  );

CREATE POLICY "Team members can update media items" ON public.media_items
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = media_items.team_id AND profile_id = auth.uid()
    )
  );

CREATE POLICY "Team members can delete media items" ON public.media_items
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.team_members
      WHERE team_id = media_items.team_id AND profile_id = auth.uid()
    )
  );

-- Apply similar policies to the other tables...

-- Create a storage bucket for media files
-- Note: This is a placeholder. In Supabase UI, create a bucket named 'medialibrary'
-- and set appropriate RLS policies for it.

-- Set up public storage bucket permissions
-- In the Supabase dashboard, make sure to create a storage bucket named 'medialibrary'
-- with the following policy:
/*
STORAGE BUCKET POLICY:
CREATE POLICY "Team members can upload media" ON storage.objects
  FOR INSERT WITH CHECK (
    auth.uid() IN (
      SELECT profile_id FROM public.team_members 
      WHERE team_id = (storage.foldername(name))[1]::uuid
    )
  );

CREATE POLICY "Team members can view media" ON storage.objects
  FOR SELECT USING (
    auth.uid() IN (
      SELECT profile_id FROM public.team_members 
      WHERE team_id = (storage.foldername(name))[1]::uuid
    )
  );

CREATE POLICY "Team members can update media" ON storage.objects
  FOR UPDATE USING (
    auth.uid() IN (
      SELECT profile_id FROM public.team_members 
      WHERE team_id = (storage.foldername(name))[1]::uuid
    )
  );

CREATE POLICY "Team members can delete media" ON storage.objects
  FOR DELETE USING (
    auth.uid() IN (
      SELECT profile_id FROM public.team_members 
      WHERE team_id = (storage.foldername(name))[1]::uuid
    )
  );
*/

-- Enable realtime 
ALTER publication supabase_realtime ADD TABLE profiles;
ALTER publication supabase_realtime ADD TABLE teams;
ALTER publication supabase_realtime ADD TABLE team_members;

-- Function to handle new user signup
CREATE OR REPLACE FUNCTION handle_new_user_signup()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_team_id uuid;
BEGIN
  -- Only proceed if email is confirmed
  IF NEW.email_confirmed_at IS NOT NULL THEN
    -- Create profile if it doesn't exist
    INSERT INTO profiles (id, email, first_name, last_name)
    VALUES (
      NEW.id,
      NEW.email,
      NEW.raw_user_meta_data->>'first_name',
      NEW.raw_user_meta_data->>'last_name'
    )
    ON CONFLICT (id) DO NOTHING;

    -- Create team
    INSERT INTO teams (name)
    VALUES (NEW.raw_user_meta_data->>'company')
    RETURNING id INTO new_team_id;

    -- Add user as team owner
    INSERT INTO team_members (profile_id, team_id, role)
    VALUES (NEW.id, new_team_id, 'owner');
  END IF;

  RETURN NEW;
END;
$$;

-- Trigger for handling new confirmed users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT OR UPDATE OF email_confirmed_at
  ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_user_signup();