import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Handle array-based query keys properly
    let url: string;
    if (queryKey.length === 1) {
      // Simple string URL
      url = queryKey[0] as string;
    } else {
      // Array of path segments - join them properly
      // First, strip any leading/trailing slashes from individual segments
      const cleanedSegments = queryKey.map(segment =>
        typeof segment === 'string' ? segment.replace(/^\/+|\/+$/g, '') : segment
      );
      // Then join with a single slash
      url = cleanedSegments.join('/');
      console.log('Constructed URL from query key segments:', url, 'original queryKey:', queryKey);
    }

    const res = await fetch(url, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false, // Prevent refetching when window regains focus
      staleTime: 0, // Always consider data stale for real-time digital signage
      gcTime: 2 * 60 * 1000, // Keep data in cache for 2 minutes only
      retry: false,
      refetchOnMount: true, // Always refetch when component mounts
    },
    mutations: {
      retry: false,
    },
  },
});
