// This script creates the database tables in Supabase
import { createClient } from '@supabase/supabase-js';

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createTables() {
  try {
    console.log('Creating profiles table...');
    const { error: profilesError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'profiles',
      definition: `
        id UUID PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        first_name TEXT,
        last_name TEXT,
        avatar_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
      `
    });
    
    if (profilesError) {
      console.error('Error creating profiles table:', profilesError);
    } else {
      console.log('Profiles table created successfully.');
    }

    console.log('Creating teams table...');
    const { error: teamsError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'teams',
      definition: `
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        logo_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW())
      `
    });
    
    if (teamsError) {
      console.error('Error creating teams table:', teamsError);
    } else {
      console.log('Teams table created successfully.');
    }

    console.log('Creating team_members table...');
    const { error: teamMembersError } = await supabase.rpc('create_table_if_not_exists', {
      table_name: 'team_members',
      definition: `
        profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
        team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
        role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc', NOW()),
        PRIMARY KEY (profile_id, team_id)
      `
    });
    
    if (teamMembersError) {
      console.error('Error creating team_members table:', teamMembersError);
    } else {
      console.log('Team members table created successfully.');
    }

    // Add more tables here...
    
    console.log('All tables created successfully.');
  } catch (error) {
    console.error('Error creating tables:', error);
  }
}

createTables().catch(console.error);