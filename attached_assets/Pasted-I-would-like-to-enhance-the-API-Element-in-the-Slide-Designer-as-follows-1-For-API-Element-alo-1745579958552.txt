I would like to enhance the API Element in the Slide Designer as follows;

1. For API Element, along with "Text", "Image", "Video", add one more data type called "Gauge"

2. Please analyze the code and properties structure of a react component called "react-d3-speedometer" from following link;
   https://github.com/palerdot/react-d3-speedometer

3. When "Gauge" data type is selected, allow following properties to be set up by the user;
   a. set minValue = 0 (add input box with similar styling as existing UI)
   b. set maxValue = 100 (add input box with similar styling as existing UI)
   c. set segments = 4 (add input box with similar styling as existing UI)
   d. set needleColor = "#4682B4" (add color picker input with similar styling as existing UI)
   f. set ringWidth = 60 (add input box with similar styling as existing UI)
   g. set textColor = "#666"  (add color picker input with similar styling as existing UI)
   h. set labelFontSize = 32px (add input box with similar styling as existing UI)
   i. set valueTextFontSize = 48px (add input box with similar styling as existing UI)
   j. set labelFontSize = 14px (add input box with similar styling as existing UI)
   k. set valueTextFontWeight = "bold" (add a dropdown having "normal", "bold", "italic")
   l. set paddingHorizontal = 15 (add input box with similar styling as existing UI)
   l. set paddingVertical = 15 (add input box with similar styling as existing UI)

4. "width" and "height" properties will be setup from "Position" tab. 

5. set dimensionUnit = 'px'

6. When "Gauage" data type is selected, show another tab called "Segments". For each segment from "segments" property set above, create following sub properties;
   a. Ability to add and remove segment stop value between the "minValue" and "maxValue" set. The values added or removed should be handled via "customSegmentLabels" array property.
   b. Ability to change segment text using input box. By default, it should have the segment stop value setup above. Store each segment text value in the customSegmentLabels.text property.
   c. Ability to change segment background color using color picker input. The values should be stored in the "segmentColors" property. 
   d. Ability to change segment text color using color picker input. The values should be stored in the customSegmentLabels.color property.
   d. By default customSegmentLabels.position = "INSIDE"
   e. Ability to set currentValueText based on the segment. Add inputbox with similar styling as existing UI

   The "Segment" tab will provide user ability to dynamically manage its properties.

7. After the "Gauge" componenet is added into the "Api Element", set forceRender = true so that whenever the user modifies its properties, the component will be force to re-render. Set "fluidWidth" = true.

8. The "Value" property will be set by getting the field value of "Data Mapping" selected item.