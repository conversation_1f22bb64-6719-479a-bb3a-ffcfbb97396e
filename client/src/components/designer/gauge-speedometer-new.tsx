import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

export interface GaugeProps {
  value: number;
  minValue: number;
  maxValue: number;
  segments: number;
  needleColor: string;
  ringWidth: number;
  textColor: string;
  labelFontSize: number;
  valueTextFontSize: number;
  valueTextFontWeight: 'normal' | 'bold' | 'italic';
  paddingHorizontal: number;
  paddingVertical: number;
  width: number;
  height: number;
  dimensionUnit: string;
  segmentColors: string[];
  segmentStops?: number[];  // New property for custom segment stop values
  customSegmentLabels: {
    text: string;
    position: string;
    color: string;
    currentValueText?: string;
  }[];
  currentValueText?: string;
  valueLabel?: string;
  forceRender?: boolean;
  fluidWidth?: boolean;
}

const GaugeChart: React.FC<GaugeProps> = ({
  value,
  minValue = 0,
  maxValue = 100,
  segments = 4,
  needleColor = "#4682B4",
  ringWidth = 60,
  textColor = "#666",
  labelFontSize = 32,
  valueTextFontSize = 48,
  valueTextFontWeight = "bold",
  paddingHorizontal = 15,
  paddingVertical = 15,
  width = 300,
  height = 200,
  dimensionUnit = 'px',
  segmentColors = ['#00BFFF', '#89EC5B', '#FFC371','#FF5F6D'],
  segmentStops = [],
  customSegmentLabels = [],
  currentValueText = 'The current value is Low',
  valueLabel = '20',
  forceRender = true,
  fluidWidth = true
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  
  // Force component to update when value changes
  useEffect(() => {
    if (!svgRef.current) return;
    
    // Clear previous SVG content
    d3.select(svgRef.current).selectAll("*").remove();
    
    renderGauge();
  }, [
    value, minValue, maxValue, segments, needleColor, ringWidth, textColor,
    labelFontSize, valueTextFontSize, valueTextFontWeight, paddingHorizontal,
    paddingVertical, width, height, dimensionUnit, segmentColors, segmentStops,
    customSegmentLabels, currentValueText, valueLabel, forceRender
  ]);
  
  const renderGauge = () => {
    if (!svgRef.current) return;
    
    const svg = d3.select(svgRef.current);
    
    // Calculate dimensions
    const viewBox = `0 0 ${width} ${height}`;
    svg.attr("viewBox", viewBox);
    
    if (fluidWidth) {
      svg.attr("width", "100%")
         .attr("height", "100%");
    } else {
      svg.attr("width", `${width}${dimensionUnit}`)
         .attr("height", `${height}${dimensionUnit}`);
    }
    
    // Calculate gauge parameters
    const margin = { 
      top: paddingVertical, 
      right: paddingHorizontal, 
      bottom: paddingVertical + valueTextFontSize * 3, 
      left: paddingHorizontal 
    };
    const chartWidth = width - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;
    const radius = Math.min(chartWidth, chartHeight * 2) / 2;
    
    // Center point for the gauge
    const cx = width / 2;
    const cy = height - margin.bottom;
    
    // Determine segment stops - either use provided custom stops or calculate equally spaced stops
    let stops: number[] = [];
    
    if (segmentStops && segmentStops.length > 0) {
      // Use custom segment stops if provided
      stops = [...segmentStops];
      
      // Ensure stops are within range and sorted
      stops = stops.filter(stop => stop > minValue && stop <= maxValue).sort((a, b) => a - b);
      
      // Make sure we have the right number of segments
      if (stops.length !== segments - 1) {
        console.warn('Number of segment stops does not match segments-1. Using equally spaced segments instead.');
        stops = [];
      }
    }
    
    // If custom stops aren't provided or are invalid, calculate equally spaced stops
    if (stops.length === 0) {
      const segmentStep = (maxValue - minValue) / segments;
      for (let i = 1; i < segments; i++) {
        stops.push(minValue + i * segmentStep);
      }
    }
    
    // Calculate all segment boundaries including min and max values
    const segmentBoundaries = [minValue, ...stops, maxValue];
    
    // Map values from [minValue, maxValue] to angles from left (-90°) to right (90°) 
    const angleScale = d3.scaleLinear()
      .domain([minValue, maxValue])
      .range([-Math.PI / 2, Math.PI / 2]);
    
    // Determine which segment the current value falls into
    let activeSegmentIndex = 0;
    for (let i = 0; i < segmentBoundaries.length - 1; i++) {
      if (value >= segmentBoundaries[i] && value <= segmentBoundaries[i+1]) {
        activeSegmentIndex = i;
        break;
      }
    }
    activeSegmentIndex = Math.min(activeSegmentIndex, segments - 1);
    
    // Create a group for the gauge
    const gauge = svg.append("g")
      .attr("transform", `translate(${cx}, ${cy})`);
    
    // Create segment arcs
    for (let i = 0; i < segments; i++) {
      const segmentStart = segmentBoundaries[i];
      const segmentEnd = segmentBoundaries[i + 1];
      const startAngle = angleScale(segmentStart);
      const endAngle = angleScale(segmentEnd);
      
      const segmentArc = d3.arc()
        .innerRadius(radius - ringWidth)
        .outerRadius(radius)
        .startAngle(startAngle)
        .endAngle(endAngle);
      
      gauge.append("path")
        .attr("d", segmentArc as any)
        .attr("fill", segmentColors[i] || d3.schemeCategory10[i % 10]);
    }
    
    // Define the segment label values
    const labelValues = [];
    for (let i = 1; i <= segments; i++) {
      labelValues.push(segmentBoundaries[i]);
    }
    
    // For each segment, calculate the position of its label
    for (let i = 0; i < segments; i++) {
      // Calculate the middle angle of this segment arc
      const segmentStart = segmentBoundaries[i];
      const segmentEnd = segmentBoundaries[i + 1];
      const startAngle = angleScale(segmentStart);
      const endAngle = angleScale(segmentEnd);
      const midAngle = (startAngle + endAngle) / 2;
      
      // Calculate the midpoint position on the arc
      const labelRadius = radius - (ringWidth / 2); // Place in middle of ring width
      
      // Convert polar to Cartesian coordinates
      const adjustedAngle = midAngle - Math.PI / 2;
      const labelX = Math.cos(adjustedAngle) * labelRadius;
      const labelY = Math.sin(adjustedAngle) * labelRadius;
      
      // Determine label text
      let labelText = labelValues[i].toString();
      let labelColor = "#000";
      
      // Use custom label if provided
      if (customSegmentLabels && customSegmentLabels[i] && customSegmentLabels[i].text) {
        labelText = customSegmentLabels[i].text;
        labelColor = customSegmentLabels[i].color || "#000";
      }
      
      // Create a group for the label
      const labelGroup = gauge.append("g")
        .attr("transform", `translate(${labelX}, ${labelY})`);
      
      // Calculate rotation for label to follow curve
      const rotationDegrees = (midAngle * 180) / Math.PI;
      
      // Create text element with rotation
      labelGroup.append("text")
        .attr("class", "segment-label")
        .attr("transform", `rotate(${rotationDegrees})`)
        .attr("x", 0)
        .attr("y", 0)
        .attr("text-anchor", "middle")
        .attr("dominant-baseline", "central")
        .attr("fill", labelColor)
        .style("font-size", `${labelFontSize}px`)
        .style("font-weight", "bold")
        .text(labelText);
    }
    
    // Draw the needle
    const needleLength = radius - 20;
    const needleRadius = 5;
    
    // ChatGPT's suggested angle calculation function
    function calculateNeedleAngle(min: number, max: number, val: number) {
      // Clamp the value between min and max
      const clampedValue = Math.max(min, Math.min(max, val));
      
      const range = max - min;
      
      // Normalize value to [0, 1]
      const normalized = (clampedValue - min) / range;
      
      // After multiple attempts, we'll use the simplest formula possible
      // For a semi-circular gauge (180 degrees total):
      // - 0% = -90 degrees (left)
      // - 50% = 0 degrees (top)
      // - 100% = 90 degrees (right)
      // 
      // So for 30% of the way (value of 30 in range 0-100),
      // we want to be 30% of the way from -90 to 90, which is:
      // -90 + (normalized * 180)
      const angle = -90 + (normalized * 180);
      
      return angle; // in degrees
    }
    
    // Calculate the needle angle using the function
    const needleAngle = calculateNeedleAngle(minValue, maxValue, value);
    
    // Debug values
    const normalizedValue = (Math.max(minValue, Math.min(maxValue, value)) - minValue) / (maxValue - minValue);
    
    // Log details for debugging
    //console.log(`Gauge calculation: min=${minValue}, max=${maxValue}, value=${value}, normalized=${normalizedValue.toFixed(2)}, angle=${needleAngle.toFixed(1)}°`);
    
    // Create the needle with explicit rotation origin at (0,0)
    // We need to be explicit about the rotation center point
    const needle = gauge.append("g")
      .attr("transform", `rotate(${needleAngle}, 0, 0)`);
    
    // Needle triangle - create a pointer shape pointing up by default
    // (this ensures that at angle 0, the needle points to 12 o'clock position)
    // The rotation will then correctly rotate it around the circle
    needle.append("path")
      .attr("d", `M0 ${-needleLength} L${needleRadius} 0 L-${needleRadius} 0 Z`)
      .attr("fill", needleColor);
    
    // Needle center circle
    gauge.append("circle")
      .attr("cx", 0)
      .attr("cy", 0)
      .attr("r", needleRadius * 2)
      .attr("fill", needleColor);
    
    // Determine the current value text
    let segmentCurrentValueText = '';
    if (customSegmentLabels && customSegmentLabels[activeSegmentIndex] && 
        customSegmentLabels[activeSegmentIndex].currentValueText) {
      // Use specific segment's current value text if available
      segmentCurrentValueText = customSegmentLabels[activeSegmentIndex].currentValueText || '';
    } else if (currentValueText) {
      // Fall back to global current value text
      segmentCurrentValueText = currentValueText;
    }
    
    // Replace {{value}} with the actual value
    if (segmentCurrentValueText) {
      segmentCurrentValueText = segmentCurrentValueText.replace(/\{\{value\}\}/g, value.toString());
    }
    
    // CORRECTED IMPLEMENTATION BASED ON NEW SCREENSHOT
    
    // 1. Position the main value in the center with no outline or background
    const valueGroup = gauge.append("g")
      .attr("transform", `translate(0, ${-radius / 4})`); // Move up from center
    
    // NO BORDER OR OUTLINE as requested - removed completely
    
    // Value text inside the box
    valueGroup.append("text")
      .attr("x", 0)
      .attr("y", valueTextFontSize/4)
      .attr("text-anchor", "middle")
      .attr("fill", textColor)
      .style("font-size", `${valueTextFontSize}px`)
      .style("font-weight", valueTextFontWeight)
      .text(value.toString());
    
    // 2. Position the status text closer to the gauge with no border or background
    if (segmentCurrentValueText) {
      const statusGroup = gauge.append("g")
        .attr("transform", `translate(0, ${radius / 6})`); // Move closer to the gauge (reduced space)
        
      // Status text with no border or background
      // Apply the valueTextFontWeight property to this text as requested
      statusGroup.append("text")
        .attr("x", 0)
        .attr("y", 0)
        .attr("text-anchor", "middle")
        .attr("dominant-baseline", "middle")
        .attr("fill", textColor)
        .style("font-size", `${labelFontSize}px`)
        .style("font-weight", valueTextFontWeight) // Using the same font weight as the value text
        .text(segmentCurrentValueText.toLowerCase()); // lowercase as per screenshot
    }
  };
  
  return (
    <svg 
      ref={svgRef} 
      className="gauge-speedometer"
      style={{ width: "100%", height: "100%", overflow: "visible" }}
    />
  );
};

export default GaugeChart;