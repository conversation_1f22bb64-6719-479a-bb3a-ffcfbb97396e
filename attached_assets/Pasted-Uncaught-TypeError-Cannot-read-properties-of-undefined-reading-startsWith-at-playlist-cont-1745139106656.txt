Uncaught TypeError: Cannot read properties of undefined (reading 'startsWith')
    at playlist-controls.tsx:138:47
    at Array.map (<anonymous>)
    at PlaylistControls (playlist-controls.tsx:137:25)
Console.js:61 The above error occurred in the <PlaylistControls> component:

    at PlaylistControls (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/playlist-controls.tsx:31:36)
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at Presence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-F2PRPNCO.js?v=4236f1a5:24:11)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=4236f1a5:175:13
    at _c5 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=4236f1a5:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-XSD2Y4RK.js?v=4236f1a5:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=4236f1a5:53:7
    at div
    at ElementProperties (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/element-properties.tsx:59:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx:61:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?v=-61KkM-kRhCVpd5Dm6KRX:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4236f1a5:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4236f1a5:247:17)
    at App
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=4236f1a5:2805:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.

Console.js:61 Error caught by ErrorBoundary: TypeError: Cannot read properties of undefined (reading 'startsWith')
    at playlist-controls.tsx:138:47
    at Array.map (<anonymous>)
    at PlaylistControls (playlist-controls.tsx:137:25)
 
Object

