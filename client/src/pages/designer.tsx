import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { Canvas, CanvasElement } from "@/components/designer/canvas";
import { Toolbox } from "@/components/designer/toolbox";
import { ElementProperties } from "@/components/designer/element-properties";
import { useAuth } from "@/hooks/use-auth";
import { apiRequest } from "@/lib/queryClient";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SaveIcon, Trash, ImagePlus, Settings, ArrowUpToLine, ArrowDownToLine, Play, Square } from "lucide-react";
import { ColorPicker } from "@/components/ui/color-picker";
import ErrorBoundary from "@/components/ui/error-boundary";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const slideSchema = z.object({
  name: z.string().min(1, "Name is required"),
  width: z.number().min(200).max(3840),
  height: z.number().min(200).max(2160),
  backgroundColor: z.string().optional(),
  apiUrl: z.string().optional(),
  apiDataPreviewDuration: z.number().optional(),
});

// Add global typings for the window object
declare global {
  interface Window {
    __API_DATA__: any;
    __API_DATA_FIELDS__: string[]; 
    __API_DATA_INDEX__: number;
    __API_DATA_TOTAL_ROWS__ : number;
    __API_DATA_IS_VIDEO_PLAYING__: boolean;
  }
}

type SlideFormValues = z.infer<typeof slideSchema>;

export default function Designer() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [teamId, setTeamId] = useState<string>("");
  const [elements, setElements] = useState<CanvasElement[]>([]);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false); // Initially false, we'll control this based on slides query
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentSlideId, setCurrentSlideId] = useState<string | null>(null);
  const [currentSlideName, setCurrentSlideName] = useState<string>("");
  const [canvasSize, setCanvasSize] = useState({ width: 1920, height: 1080 });
  const [canvasBackground, setCanvasBackground] = useState("#ffffff");
  const [apiUrl, setApiUrl] = useState("");
  const [apiPreviewDuration, setApiPreviewDuration] = useState(10);
  const [isSaving, setIsSaving] = useState(false);
  const [isApiLoading, setIsApiLoading] = useState(false);
  //const [apiData, setApiData] = useState<any>(null);

  // Flag to prevent unnecessary API fetches when stopping preview
  const apiPreviewStoppingRef = useRef<boolean>(false);

  // Preview functionality
  const [isPreviewActive, setIsPreviewActive] = useState(false);
  //const previewIntervalRef = useRef<number | null>(null);
  const apiPreviewTimeoutRef = useRef<number | null>(null);
  const videoEndedHandlerRef = useRef<((event: Event) => void) | null>(null);

  // Track whether we're in the process of cycling API data to prevent cleanup
  const isApiCyclingRef = useRef<boolean>(false);

  // Store timers for image-based playlist items
  const imageTimersRef = useRef<{[elementId: string]: number}>({});

  // Store reference to multimedia playlist items and their playing status
  const multimediaPlaylistsRef = useRef<{
    [elementId: string]: {
      currentIndex: number;
      isPlaying: boolean;
      timer: number | null;
    }
  }>({});

  const [currentPreviewElements, setCurrentPreviewElements] = useState<{
    type: 'multimedia' | 'playlist' | 'api',
    elementId: string,
    currentIndex: number
  }[]>([]);

  // Reference to keep track of current API data index during preview
  const currentApiDataIndexRef = useRef<number>(0);

  // Set team ID from user data
  useEffect(() => {
    if (user && user.id) {
      // Fetch team info from API since we don't store it directly on the user object
      const fetchTeams = async () => {
        try {
          const response = await fetch(`/api/profiles/${user.id}/teams`);
          if (response.ok) {
            const teams = await response.json();
            if (teams && teams.length > 0) {
              console.log("Found teams for user:", teams);
              setTeamId(teams[0].id);
            }
          }
        } catch (error) {
          console.error("Error fetching team data:", error);
        }
      };

      fetchTeams();
    }
  }, [user]);

  // Effect to set up video playback for preview mode
  useEffect(() => {
    if (!isPreviewActive) return;

    //console.log("Preview active: Looking for video elements to play");


    // Set up timer for image-based playlist items
    const playlistElements = elements.filter(el => el.type === 'playlist');
    playlistElements.forEach(element => {
      const playlist = element.content?.playlist || [];
      if (playlist.length === 0) return;

      const currentIndex = element.content?.currentIndex || 0;
      const currentItem = playlist[currentIndex];

      // If this is an image, set a timer to move to the next item
      if (currentItem && currentItem.fileType?.startsWith('image/')) {
        const duration = currentItem.duration || 5; // Default to 5 seconds if not specified
        const timerId = window.setTimeout(() => {
          // Only advance if still in preview mode
          if (isPreviewActive) {
            const nextIndex = (currentIndex + 1) % playlist.length;
            updatePlaylistElement(element.id, nextIndex);
          }
        }, duration * 1000);

        // Store the timer ID for cleanup
        imageTimersRef.current[element.id] = timerId;
      }
    });

    // Clean up when preview is stopped or elements change
    return () => {

      //cleanupPreviewEventListeners();
    };
  }, [isPreviewActive, currentPreviewElements, elements, window.__API_DATA__, apiPreviewDuration]);

  // Function to clean up all event listeners and timers
  const cleanupPreviewEventListeners = () => {
    //console.log("CLEANUP: Clearing all preview timers and event handlers");

    // ALWAYS clear intervals when this function is called - regardless of cycling state

    // Clear API data cycling interval
    if (apiPreviewTimeoutRef.current) {
      console.log(`CLEANUP: Clearing interval ID: ${apiPreviewTimeoutRef.current}`);
      window.clearInterval(apiPreviewTimeoutRef.current);
        apiPreviewTimeoutRef.current = null;
      //console.log("CLEANUP: Cleared API cycling interval");
    } else {
     // console.log("CLEANUP: No active API cycling interval to clear");
    }

    // Reset important state references
    isApiCyclingRef.current = false;
    currentApiDataIndexRef.current = 0;
    window.__API_DATA_INDEX__ = 0;
    window.__API_DATA_TOTAL_ROWS__ = 0;
    window.__API_DATA_IS_VIDEO_PLAYING__ = false;

    // Clear all multimedia playlist timers
    Object.keys(multimediaPlaylistsRef.current).forEach(elementId => {
      const playlist = multimediaPlaylistsRef.current[elementId];
      if (playlist && playlist.timer) {
        window.clearTimeout(playlist.timer);
        //console.log(`Cleared multimedia playlist timer for element ${elementId}`);
      }
    });
    multimediaPlaylistsRef.current = {};

    // Clear all image timers
    Object.keys(imageTimersRef.current).forEach(elementId => {
      window.clearTimeout(imageTimersRef.current[elementId]);
      //console.log(`Cleared image timer for element ${elementId}`);
    });
    imageTimersRef.current = {};

    // Remove event listeners from all videos
    //if (videoEndedHandlerRef.current) {
      const videoElements = document.querySelectorAll('video');
      videoElements.forEach(video => {
        video.pause();         // Stop the video
        //video.currentTime = 0; // Set to the beginning (frame 1)
         video.onended = null;
          console.log("Removed video ended event listener");
      });
      //videoEndedHandlerRef.current = null;
    //}

    //console.log("CLEANUP: All preview resources have been released");
  };

  // Smart path resolver - handles automatic array index application
  const getValueAtPathWithIndex = (data: any, path: string, rowIndex: number): any => {
    if (!path || !data) return null;

    try {
      const keys = path.split('.');
      let current = data;

      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];

        if (Array.isArray(current)) {
          // If we're not at the last key yet and the next level is array-like
          current = current.map(item => item[key]).filter(item => item !== undefined).flat();
        } else if (typeof current === 'object' && current !== null) {
          current = current[key];
        } else {
          return undefined;
        }
      }

      // At this point, current can still be an array, especially if the final property was a list
      if (Array.isArray(current)) {
        return current[rowIndex] !== undefined ? current[rowIndex] : undefined;
      }

      return current;

    } catch (error) {
      console.error(`Error accessing path "${path}" with rowIndex ${rowIndex}:`, error);
      return null;
    }
  };

  // Helper function to get value using dot notation path (simple version for direct access)
  const getNestedValue = (obj: any, path: string): any => {
    if (!path || !obj) return null;

    try {
      return path.split('.').reduce((acc, key) => acc && acc[key], obj);
    } catch (error) {
      console.error(`Error accessing path "${path}":`, error);
      return null;
    }
  };

  // Function to fetch API data
  const fetchApiData = async () => {
    // Reset the stopping flag when we intentionally fetch API data
    apiPreviewStoppingRef.current = false;

    if (!apiUrl) {
        window.__API_DATA__ =  {};
        return;
    }

    try {
      setIsApiLoading(true);
      
      // Use the server proxy endpoint to avoid CORS issues
      const encodedUrl = encodeURIComponent(apiUrl);
      const proxyUrl = `/api/proxy?url=${encodedUrl}`;
      
      console.log(`Fetching API data via proxy: ${proxyUrl}`);
      const response = await fetch(proxyUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Initialize global objects if they don't exist (matching our type declarations)
      if (!window.__API_DATA__) window.__API_DATA__ = {};
      window.__API_DATA_FIELDS__ = []; // Always initialize to empty array
      window.__API_DATA_INDEX__ = 0; // Always reset to 0 when loading new data
      window.__API_DATA_TOTAL_ROWS__ = 0; // Always reset to 0 when loading new data
      window.__API_DATA_IS_VIDEO_PLAYING__ = false;
      // Make the API data available to the multimedia elements
      window.__API_DATA__ = data;

      // Debug: Log the complete structure of the API data
      //console.log('API Data Structure:', data);

      // Function 1: Extract API data columns/keys for the dropdown menu
      // This shows fields without array indices, only using first record in nested arrays
      const apiDataColumnExtraction = (obj: any): string[] => {
        const result: string[] = [];
        const seen = new Set<string>();
        const mappings: Record<string, string> = {};

        // Helper to identify primitive values (leaf nodes)
        const isPrimitive = (val: any): boolean => {
          return val === null || 
                 val === undefined ||
                 typeof val === 'string' || 
                 typeof val === 'number' || 
                 typeof val === 'boolean';
        };

        // Process data recursively to extract column/key names
        function extractColumns(data: any, path: string = '', indexPath: string = '') {
          // Skip null/undefined
          if (data === null || data === undefined) return;

          //console.log(`Extracting from path: ${path}, indexPath: ${indexPath}, data type: ${typeof data}, isArray: ${Array.isArray(data)}`);

          // Handle arrays - use first item but hide index in display
          if (Array.isArray(data)) {
            if (data.length === 0) return;

            // Get first item from array (per requirements, only show first record)
            const item = data[0];
            const itemIndexPath = indexPath ? `${indexPath}.0` : `${path}.0`;

            //console.log(`Processing array at ${path} - first item type: ${typeof item}`);

            // Process based on item type
            if (isPrimitive(item)) {
              // For array of primitives, add array path directly
              if (!seen.has(path)) {
                result.push(path);
                seen.add(path);
                mappings[path] = itemIndexPath;
                //console.log(`Added primitive array path: ${path} -> ${itemIndexPath}`);
              }
            } else if (typeof item === 'object') {
              // Process object properties without showing array index in path
              //console.log(`Recursing into array item at ${path} with indexPath ${itemIndexPath}`);
              extractColumns(item, path, itemIndexPath);
            }
          }
          // Handle objects
          else if (typeof data === 'object') {
            for (const key in data) {
              const value = data[key];
              const newPath = path ? `${path}.${key}` : key;
              const newIndexPath = indexPath ? `${indexPath}.${key}` : newPath;

              //console.log(`Processing object property: ${newPath}`);

              // Add primitive values to result
              if (isPrimitive(value)) {
                if (!seen.has(newPath)) {
                  result.push(newPath);
                  seen.add(newPath);
                  // Only add mapping if paths differ
                  if (newPath !== newIndexPath) {
                    mappings[newPath] = newIndexPath;
                    //console.log(`Added mapping: ${newPath} -> ${newIndexPath}`);
                  } else {
                    //console.log(`Added direct path: ${newPath}`);
                  }
                }
              }
              // Process nested structures
              else if (typeof value === 'object' && value !== null) {
                //console.log(`Recursing into nested object at ${newPath}`);
                extractColumns(value, newPath, newIndexPath);
              }
            }
          }
        }

        // Start extraction
        extractColumns(obj);

        // Store mappings in global variables for access between components
        window.__API_PATH_MAPPINGS__ = mappings;
        window.__GET_REAL_PATH__ = (path: string): string => mappings[path] || path;

        // For debugging - log the data and the generated mappings
        //console.log("API Data used for extraction:", obj);
        //console.log("Path mappings generated:", mappings);

        // Convert just the top 30 fields to keep debug logs manageable
        const topFields = result.slice(0, 30);
        const fieldsWithValues = topFields.map(path => {
          return { path, value: getNestedValue(obj, path) };
        });
        //console.log("Sample extracted fields with values:", fieldsWithValues);

        // Sort results for easier navigation
        return result.sort();
      };

      // Store the extracted fields in the global object
      const extractedFields = apiDataColumnExtraction(data);
      window.__API_DATA_FIELDS__ = extractedFields;

      // Debug: Log the extracted fields
      //console.log('Extracted API data fields:', extractedFields);

      toast({
        title: "API Data Loaded",
        description: "Successfully loaded data from the API URL."
      });

    } catch (error) {
      console.error('Error fetching API data:', error);
      window.__API_DATA__ = {};

      toast({
        title: "API Error",
        description: "Failed to load data from the provided API URL.",
        variant: "destructive"
      });
    } finally {
      setIsApiLoading(false);
    }
  };

  // Function to update an element with data from the specified index - simplified version
  const updateElementWithApiData = (element: CanvasElement, dataIndex: number) => {
    if (!element.content) {
      console.log(`Can't update element ${element.id}: No content configuration`);
      return;
    }

    // Get the data field to display (prioritize dataField, then fall back to apiMapping)
    let mainField = element.content.dataField;

    // If no direct dataField, try to get from apiMapping
    if (!mainField) {
      const mappedFields = element.content.apiMapping ? Object.keys(element.content.apiMapping) : [];
      if (mappedFields.length > 0) {
        mainField = mappedFields[0];
      }
    }

    if (!mainField) {
      console.log(`Element ${element.id} has no defined data field`);
      return;
    }

    // Use our new simplified getValueAt function
    //const fieldValue = getValueAt(mainField, dataIndex);
    
    const fieldValue = getValueAtPathWithIndex(window.__API_DATA__, mainField, dataIndex);
    // Check if we found a valid field
    if (fieldValue === null || fieldValue === undefined) {
      console.log(`No data found for field "${mainField}" at index ${dataIndex}`);
      return;
    }

    // Get the element type and update accordingly
    const elementType = element.content.type || 'text';

    if(element.content.subtype == "api.video"){
      window.__API_DATA_IS_VIDEO_PLAYING__ = true;
    }
    // Log the update
    //console.log(`Updating ${elementType} element ${element.id} with field "${mainField}" at index ${dataIndex}`);
    //console.log(`Designer dataIndex value:`, dataIndex);

    // Update element based on its type
    switch (elementType) {
      case 'text':
        // For text, directly set the content text
        element.content.text = String(fieldValue);
        //console.log(`Updated text element ${element.id} with "${element.content.text}"`);
        break;

      case 'image':
        // For images, check if we should use placeholder URL with replacements
        if (element.content.subtype === 'api.image' && element.content.placeholderUrl) {
          const placeholderUrl = element.content.placeholderUrl;
          
          // Process template URL if it contains variables
          if (placeholderUrl && placeholderUrl.includes('{') && placeholderUrl.includes('}')) {
            // Replace all {fieldName} patterns with actual values from API data
            const processedUrl = placeholderUrl.replace(/\{([^}]+)\}/g, (match: string, fieldPath: string) => {
              if (fieldPath.trim() === mainField) {
                return String(fieldValue);
              } else {
                // Get value for other fields that might be in the URL
                const otherFieldValue = getValueAtPathWithIndex(window.__API_DATA__, fieldPath.trim(), dataIndex);
                return otherFieldValue !== null && otherFieldValue !== undefined ? String(otherFieldValue) : match;
              }
            });
            
            console.log(`Processed image URL template: ${placeholderUrl} → ${processedUrl}`);
            element.content.src = processedUrl;
          } else {
            element.content.src = String(fieldValue);
          }
        } else {
          // Default behavior - set source directly
          element.content.src = String(fieldValue);
        }
        break;

      case 'video':
        // For videos, check if we should use placeholder URL with replacements
        if (element.content.subtype === 'api.video' && element.content.placeholderUrl) {
          const placeholderUrl = element.content.placeholderUrl;
          
          // Process template URL if it contains variables
          if (placeholderUrl && placeholderUrl.includes('{') && placeholderUrl.includes('}')) {
            // Replace all {fieldName} patterns with actual values from API data
            const processedUrl = placeholderUrl.replace(/\{([^}]+)\}/g, (match: string, fieldPath: string) => {
              if (fieldPath.trim() === mainField) {
                return String(fieldValue);
              } else {
                // Get value for other fields that might be in the URL
                const otherFieldValue = getValueAtPathWithIndex(window.__API_DATA__, fieldPath.trim(), dataIndex);
                return otherFieldValue !== null && otherFieldValue !== undefined ? String(otherFieldValue) : match;
              }
            });
            
            console.log(`Processed video URL template: ${placeholderUrl} → ${processedUrl}`);
            element.content.src = processedUrl;
          } else {
            element.content.src = String(fieldValue);
          }
        } else {
          // Default behavior - set source directly
          element.content.src = String(fieldValue);
        }
        break;

      default:
        // For other types, try to set the text content
        element.content.text = String(fieldValue);
        //console.log(`Updated element ${element.id} with "${element.content.text}"`);
    }

    // Update the element's current index in preview tracking
    setCurrentPreviewElements(prev => 
      prev.map(item => 
        item.elementId === element.id ? { ...item, currentIndex: dataIndex } : item
      )
    );
  };

  // Step 1 Function to set up API data preview cycling - completely rewritten for reliability
  const setupApiDataPreview = () => {

    if (apiPreviewTimeoutRef.current) {
      //console.log(`Clearing previous interval ID: ${apiPreviewTimeoutRef.current}`);
      window.clearInterval(apiPreviewTimeoutRef.current);
        apiPreviewTimeoutRef.current = null;
    }

    // Validate we have data and duration
    //if (!apiData || apiPreviewDuration <= 0) {
    if (!window.__API_DATA__) {
      console.log("No API data set, skipping API preview setup");
      return;
    }

    // Explicitly reset data index to 0
    currentApiDataIndexRef.current = 0;
    window.__API_DATA_INDEX__ = 0;
    window.__API_DATA_TOTAL_ROWS__ = 0;
    window.__API_DATA_IS_VIDEO_PLAYING__ = false;
    //console.log("API data index RESET to 0 at start of preview");

    // Find all API elements on the canvas
    const apiElements = findApiElements();

    if (apiElements.length === 0) {
      console.log("No API elements found on canvas - skipping API preview setup");
      return;
    }
    
    let maxRows = 0;

    // For each API element, analyze its data mapping path
    apiElements.forEach(element => {
      const dataField = element.content?.dataField;
      const apiMapping = element.content?.apiMapping;
      
      // Check both dataField and apiMapping paths
      const paths = [];
      if (dataField) paths.push(dataField);
      if (apiMapping) paths.push(...Object.keys(apiMapping));

      // For each path, check for array lengths and collect data
      paths.forEach(path => {
        if (!path) return;
        
        // Function to recursively find and collect data at a path
        const findAllNodesAtPath = (obj: any, pathSegments: string[]): { count: number; data: any[] } => {
          if (!obj) return { count: 0, data: [] };
          if (pathSegments.length === 0) return { count: 1, data: [obj] };
          
          const [currentSegment, ...remainingSegments] = pathSegments;
          let results: { count: number; data: any[] } = { count: 0, data: [] };
          
          // Handle arrays
          if (Array.isArray(obj)) {
            obj.forEach(item => {
              const itemResult = findAllNodesAtPath(item, pathSegments);
              results.count += itemResult.count;
              results.data.push(...itemResult.data);
            });
            return results;
          }
          
          // Handle objects
          if (typeof obj === 'object' && obj !== null) {
            if (currentSegment in obj) {
              const nestedResult = findAllNodesAtPath(obj[currentSegment], remainingSegments);
              results.count += nestedResult.count;
              results.data.push(...nestedResult.data);
            }
            
            // Also search through all object properties for nested matches
            Object.values(obj).forEach(value => {
              if (typeof value === 'object' && value !== null) {
                const nestedResult = findAllNodesAtPath(value, pathSegments);
                results.count += nestedResult.count;
                results.data.push(...nestedResult.data);
              }
            });
          }
          
          return results;
        };

          // Split path and find all matching nodes
        const pathSegments = path.split('.');
        const { count: nodeCount, data: nodeData } = findAllNodesAtPath(window.__API_DATA__, pathSegments);

        // Update maxRows if we found a higher count
        if (nodeCount > 0) {
          maxRows = Math.max(maxRows, nodeCount);
          // Add the data to our dataArray
          //dataArray = Array.from(new Set([...dataArray, ...nodeData]));
        }
      });
    });
    
    // Set the total rows to the maximum found
    window.__API_DATA_TOTAL_ROWS__ = maxRows;

    // If we don't have multiple records, no need to set up cycling
    if (window.__API_DATA_TOTAL_ROWS__ <= 1) {
      //console.log("Data has 1 or 0 records - no cycling needed");

      // Just initialize with the first (only) record
      if (window.__API_DATA__.length === 1) {
        apiElements.forEach(element => {
          updateElementWithApiData(element, 0);
        });
      }

      return;
    }
    
    // Initialize all elements with the first record (index 0)
    apiElements.forEach(element => {
      updateElementWithApiData(element, 0);
    });

    if(window.__API_DATA_IS_VIDEO_PLAYING__ == false){
      // Set up the interval to cycle records
      const intervalId = window.setInterval(cycleToNextRecord, apiPreviewDuration * 1000);

      // Store the interval ID for cleanup
      //previewIntervalRef.current = intervalId;
      apiPreviewTimeoutRef.current = intervalId;
      //console.log(`API data cycling interval CREATED with ID: ${intervalId}`);
    }

  };

  // Create a simple cycling function that will be called by the interval
  const cycleToNextRecord = () => {

      if (apiPreviewTimeoutRef.current) {
        window.clearInterval(apiPreviewTimeoutRef.current);
          apiPreviewTimeoutRef.current = null;
      }
    // Find all API elements on the canvas
    const apiElements = findApiElements();

    if (apiElements.length === 0) {
      console.log("No API elements found on canvas - skipping API preview setup");
      return;
    }

    // Get current index
    const currentIndex = currentApiDataIndexRef.current || 0;

    // Calculate next index
    var nextIndex = 0;
    if(((currentIndex + 1) < window.__API_DATA_TOTAL_ROWS__) && apiPreviewDuration > 0){
      nextIndex = currentIndex + 1;
    }

    //console.log(`API CYCLING: moving from record ${currentIndex} to record ${nextIndex} of ${window.__API_DATA_TOTAL_ROWS__-1} total records`);

    // Update global references
    currentApiDataIndexRef.current = nextIndex;
    window.__API_DATA_INDEX__ = nextIndex;
    window.__API_DATA_IS_VIDEO_PLAYING__ = false;

    // Update each API element with the data from the new index
    apiElements.forEach(element => {
      updateElementWithApiData(element, nextIndex);
    });

    // Force canvas re-render to display the changes
    // This is crucial to make sure the canvas updates with new data
    setElements(prev => {
      return prev.map(el => ({
        ...el,
        _forceUpdate: Date.now()
      }));
    });
    //console.log(`window.__API_DATA_IS_VIDEO_PLAYING__  ${window.__API_DATA_IS_VIDEO_PLAYING__}`)
    if(window.__API_DATA_IS_VIDEO_PLAYING__ == false){
      // Set up the interval to cycle records
      const intervalId = window.setInterval(cycleToNextRecord, apiPreviewDuration * 1000);

      // Store the interval ID for cleanup
      //previewIntervalRef.current = intervalId;
      apiPreviewTimeoutRef.current = intervalId;
      //console.log(`API data cycling interval CREATED with ID: ${intervalId}`);
    }
    //console.log("CYCLING TO NEXT RECORD COMPLETE");
  };
  
  // Find all potential data arrays in the API data
  const findApiDataArrays = (data: any): any[] => {
    const dataArrays: any[] = [];

    // If the data is already an array with multiple records, add it
    if (Array.isArray(data) && data.length > 1) {
      console.log(`Found top-level array with ${data.length} records`);
      dataArrays.push({
        path: "",
        data: data
      });
    }

    // Function to recursively search for arrays in the data structure
    const findArrays = (obj: any, path: string = "") => {
      if (!obj || typeof obj !== 'object') return;

      if (Array.isArray(obj)) {
        if (obj.length > 1 && typeof obj[0] === 'object') {
          console.log(`Found nested array at path "${path}" with ${obj.length} records`);
          dataArrays.push({
            path,
            data: obj
          });
        }

        // Don't need to traverse arrays of arrays - we're looking for arrays of objects
        if (obj.length > 0 && typeof obj[0] === 'object' && !Array.isArray(obj[0])) {
          // Process the first item as an example
          findArrays(obj[0], path ? `${path}[0]` : '[0]');
        }
      } else {
        // Process regular objects
        Object.keys(obj).forEach(key => {
          const newPath = path ? `${path}.${key}` : key;

          if (obj[key] && typeof obj[key] === 'object') {
            findArrays(obj[key], newPath);
          }
        });
      }
    };

    // Start the recursive search
    findArrays(data);

    return dataArrays;
  };

  // Find all API elements on the canvas
  const findApiElements = () => {
    return elements.filter(el => {
      // Element can be either 'api' type or 'multimedia' type with API data
      const isApiElement = el.type === 'api';
      const isMultimediaWithApiData = el.type === 'multimedia' && (
        // For backward compatibility - check if multimedia has API data
        (el.content && el.content.dataField && 
         el.content.dataField !== "" && 
         el.content.dataField !== "_none") ||
        (el.content && el.content.apiMapping && 
         Object.keys(el.content.apiMapping).length > 0)
      );

      // If it's not an API element of any kind, return false
      if (!isApiElement && !isMultimediaWithApiData) {
        return false;
      }

      // Check if element has API mapping in its content
      const hasApiMapping = el.content && 
                          el.content.apiMapping && 
                          Object.keys(el.content.apiMapping).length > 0;

      // Also check if element has a dataField set
      const hasDataField = el.content && 
                          el.content.dataField && 
                          el.content.dataField !== "" && 
                          el.content.dataField !== "_none";

      //console.log(`Element ${el.id} type: ${el.type}, has API mapping: ${hasApiMapping ? 'true' : 'false'}`, el.content?.apiMapping);
      //console.log(`Element ${el.id} has data field: ${hasDataField ? 'true' : 'false'}`, el.content?.dataField);

      // Initialize apiMapping if we have a dataField but no mapping
      if (hasDataField && !hasApiMapping && el.content) {
        //console.log(`Initializing missing apiMapping for element ${el.id} with dataField ${el.content.dataField}`);
        el.content.apiMapping = { [el.content.dataField]: true };
        return true;
      }

      return hasApiMapping || hasDataField;
    });
  };

  // Set up preview for API elements based on their data type
  const setupApiElementsPreview = (apiElements: CanvasElement[], apiDataArrays: any[]): { 
    useIntervalCycling: boolean 
  } => {
    let useIntervalCycling = false;

    // Set all API elements to display data from the initial record
    apiElements.forEach(element => {
      // Try to find the data array that contains the fields needed for this element
      const elementFields = element.content?.apiMapping ? Object.keys(element.content.apiMapping) : [];
      if (!elementFields.length && element.content?.dataField) {
        elementFields.push(element.content.dataField);
      }

      if (elementFields.length === 0) {
        console.log(`Element ${element.id} has no mapped fields, skipping`);
        return;
      }

      console.log(`Element ${element.id} needs fields: ${elementFields.join(', ')}`);

      // Find a data array that has a match for at least one of the needed fields
      const matchingArray = findMatchingDataArray(apiDataArrays, elementFields);

      if (!matchingArray) {
        console.log(`No matching data array found for element ${element.id}`);
        return;
      }

      // Store the data array path for this element
      if (element.content) {
        element.content.apiDataPath = matchingArray.path;
      }

      // Check if element contains a video
      const elementType = element.content?.type || 'text';
      const isVideo = elementType === 'video';

      if (!isVideo) {
        // This element will use interval-based cycling
        useIntervalCycling = true;
      } else {
        console.log(`Element ${element.id} is a video element, will use ended event for cycling`);

        // Set up ended event for video elements
        // This will be handled in useEffect after the elements are rendered
      }

      // Initialize the element with the first record's data
      updateElementWithApiData(element, 0);
    });

    return { useIntervalCycling };
  };

  // Find a data array that contains at least one of the needed fields
  const findMatchingDataArray = (apiDataArrays: any[], fields: string[]): any => {
    for (const dataArray of apiDataArrays) {
      if (dataArray.data.length > 0) {
        const firstRecord = dataArray.data[0];

        // Check if any of the required fields exist in this data array
        for (const field of fields) {
          // For simple field access (not nested)
          if (firstRecord.hasOwnProperty(field)) {
            console.log(`Found matching data array for field ${field} at path "${dataArray.path}"`);
            return dataArray;
          }

          // For nested field access (using dot notation)
          if (field.includes('.')) {
            const fieldParts = field.split('.');
            let currentObj = firstRecord;
            let foundMatch = true;

            for (const part of fieldParts) {
              if (!currentObj || !currentObj.hasOwnProperty(part)) {
                foundMatch = false;
                break;
              }
              currentObj = currentObj[part];
            }

            if (foundMatch) {
              console.log(`Found matching data array for nested field ${field} at path "${dataArray.path}"`);
              return dataArray;
            }
          }
        }
      }
    }

    return null;
  };

  // Get slides for this team
  const { data: slides, isLoading, refetch } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/slides`],
    enabled: !!teamId,
  });

  // Debug and manage the slides query
  useEffect(() => {
    if (teamId) {
      console.log("Fetching slides for teamId:", teamId);
    }
    if (slides) {
      console.log("Slides fetched:", slides);
      
      // Only show create modal if there are no slides
      if (slides.length === 0) {
        setIsCreateModalOpen(true);
      } else {
        setIsCreateModalOpen(false);
      }
    }
  }, [teamId, slides]);

  // Form for creating a new slide
  const form = useForm<SlideFormValues>({
    resolver: zodResolver(slideSchema),
    defaultValues: {
      name: "Untitled Slide",
      width: 1920,
      height: 1080,
      backgroundColor: "#ffffff",
      apiUrl: "",
      apiDataPreviewDuration: 0,
    },
  });

  // Form for editing an existing slide
  const editForm = useForm<SlideFormValues>({
    resolver: zodResolver(slideSchema),
    defaultValues: {
      name: "",
      width: 1920,
      height: 1080,
      backgroundColor: "#ffffff",
      apiUrl: "",
      apiDataPreviewDuration: 0,
    },
  });

  // Helper to get a selected element
  const getSelectedElement = (): CanvasElement | null => {
    if (!selectedElementId) return null;
    return elements.find(el => el.id === selectedElementId) || null;
  };

  // Bring the selected element forward by incrementing z-index by 1
  const handleBringToFront = () => {
    if (!selectedElementId) return;

    // Get the selected element
    const selectedElement = elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    // Get the current z-index and increment by 1
    const currentZIndex = selectedElement.zIndex || 0;
    const newZIndex = currentZIndex + 1;

    // Update the selected element with new z-index
    handleUpdateElement(selectedElementId, { zIndex: newZIndex });

    // No toast alert
  };

  // Send the selected element backward by decrementing z-index by 1
  const handleSendToBack = () => {
    if (!selectedElementId) return;

    // Get the selected element
    const selectedElement = elements.find(el => el.id === selectedElementId);
    if (!selectedElement) return;

    // Get the current z-index and decrement by 1
    const currentZIndex = selectedElement.zIndex || 0;
    const newZIndex = currentZIndex - 1;

    // Update the selected element with new z-index
    handleUpdateElement(selectedElementId, { zIndex: newZIndex });

    // No toast alert
  };

  // Add a new element to the canvas
  const handleAddElement = (type: string, content: any = {}) => {
    // Find the highest z-index among existing elements (if any)
    const maxZIndex = elements.length > 0 
      ? Math.max(...elements.map(el => el.zIndex || 0)) 
      : 0;

    const newElement: CanvasElement = {
      id: crypto.randomUUID(),
      type: type as any,
      x: Math.round((canvasSize.width / 2) - 150),
      y: Math.round((canvasSize.height / 2) - 75),
      width: 300,
      height: 150,
      content,
      style: {},
      teamId: teamId, // Add teamId to element
      zIndex: maxZIndex + 1, // Always place new elements on top
    };

    // Adjust size and content based on type
    if (type === 'text') {
      newElement.height = 50;
      // Set default text content if not provided
      if (!content.text) {
        newElement.content.text = "Sample Text";
      }
      // Set default text style if not provided
      if (!newElement.content.style) {
        newElement.content.style = {
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          color: '#000000',
          textAlign: 'center',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'api') {
      newElement.height = 100;

      // Set default API element subtype
      if (!newElement.content.subtype) {
        newElement.content.subtype = 'api.text';
      }

      // Set default dataField if not provided
      if (!newElement.content.dataField) {
        newElement.content.dataField = '';
      }

      // Set default placeholder value
      if (!newElement.content.placeholder) {
        newElement.content.placeholder = 'API Data';
      }

      // Set styles based on subtype
      if ((newElement.content.subtype === 'api.text' || !newElement.content.subtype) && !newElement.content.style) {
        newElement.content.style = {
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          color: '#000000',
          textAlign: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'multimedia') {
      newElement.height = 100;

      // Set default content type for multimedia
      if (!newElement.content.type) {
        newElement.content.type = 'text';
      }

      // For backwards compatibility
      if (!newElement.content.dataType && newElement.content.type) {
        newElement.content.dataType = newElement.content.type;
      }

      // Initialize the apiMapping property for backwards compatibility
      if (!newElement.content.apiMapping) {
        newElement.content.apiMapping = {};
      }

      // Set default text style for multimedia text elements
      if ((newElement.content.type === 'text' || newElement.content.dataType === 'text') && !newElement.content.style) {
        newElement.content.style = {
          fontFamily: 'Inter, sans-serif',
          fontSize: '16px',
          color: '#000000',
          textAlign: 'center',
          verticalAlign: 'middle',
          backgroundColor: 'transparent'
        };
      }
    } else if (type === 'playlist') {
      newElement.width = 400;
      newElement.height = 225;
      // Ensure default playlist structure exists
      if (!newElement.content.playlist) {
        newElement.content.playlist = [];
      }
      if (newElement.content.currentIndex === undefined) {
        newElement.content.currentIndex = 0;
      }
    }

    setElements(prev => [...prev, newElement]);
    setSelectedElementId(newElement.id);
  };

  // Update an existing element on the canvas
  const handleUpdateElement = (id: string, updates: Partial<CanvasElement>) => {
    setElements(prev => 
      prev.map(el => 
        el.id === id ? { ...el, ...updates } : el
      )
    );
  };

  // Delete an element from the canvas
  const handleDeleteElement = (id: string) => {
    setElements(prev => prev.filter(el => el.id !== id));
    setSelectedElementId(null);
  };

  // Add a media item to the playlist of a selected multimedia element
  const handleAddToPlaylist = (mediaItem: any) => {
    if (!selectedElementId) return;

    // Find the selected element
    const element = elements.find(el => el.id === selectedElementId);

    // Only add to playlist if it's a playlist element type
    if (element && element.type === 'playlist') {
      // Create a new playlist array if it doesn't exist
      const playlist = element.content?.playlist || [];

      // Create a media item entry for the playlist
      const playlistItem = {
        id: mediaItem.id,
        name: mediaItem.name || 'Untitled',
        fileUrl: mediaItem.url,
        fileType: mediaItem.fileType,
        thumbnailUrl: mediaItem.thumbnailUrl || mediaItem.thumbnail_url,
        duration: mediaItem.fileType?.startsWith('image/') ? 5 : 0, // Default 5 seconds for images
      };

      // Update the element with the new playlist item
      setElements(prev => 
        prev.map(el => 
          el.id === selectedElementId 
            ? { 
                ...el, 
                content: { 
                  ...el.content, 
                  playlist: [...playlist, playlistItem]
                } 
              } 
            : el
        )
      );

      toast({
        title: "Media Added",
        description: `Added "${playlistItem.name}" to playlist.`,
      });
    } else {
      toast({
        title: "Cannot Add Media",
        description: "Please select a playlist element first.",
        variant: "destructive"
      });
    }
  };

  // Update the current index of a playlist element
  const updatePlaylistElement = (elementId: string, newIndex: number) => {
    setElements(prev => 
      prev.map(el => 
        el.id === elementId ? { 
          ...el, 
          content: { 
            ...el.content, 
            currentIndex: newIndex 
          } 
        } : el
      )
    );

    // Update the preview element tracking
    setCurrentPreviewElements(prev => 
      prev.map(item => 
        item.elementId === elementId ? { ...item, currentIndex: newIndex } : item
      )
    );

    // If we're in preview mode, we need to ensure videos play right after the currentIndex changes
    if (isPreviewActive) {
      // Give a small delay to allow the DOM to update with the new video element
      setTimeout(() => {
        // Find all videos within this specific element
        const elementWrapper = document.querySelector(`[data-element-id="${elementId}"]`);
        if (elementWrapper) {
          const videoElement = elementWrapper.querySelector('video');
          if (videoElement) {
            // Start playing the video if found
            videoElement.play().catch(err => {
              console.error("Error playing video:", err);
            });
          }
        }
      }, 100);
    }
  };

  // Toggle preview mode
  const togglePreview = () => {
    if (isPreviewActive) {
      stopPreview();
    } else {
      startPreview();
    }
  };

  // Start preview mode
  const startPreview = () => {
    // For preview, we only use already loaded API data, no automatic fetching

    // Set the preview to active
    setIsPreviewActive(true);
  };

  // Handle preview setup when state changes
  useEffect(() => {
    if (!isPreviewActive) return;

    console.log(`Preview mode activated: ${isPreviewActive}`);
    // Reset all elements to initial state
    //const playlistElements = elements.filter(el => el.type === 'playlist');
    const multimediaElements = elements.filter(el => el.type === 'multimedia');
    const apiElements = elements.filter(el => el.type === 'api');

    // Clear any existing multimedia playlist timers
    Object.keys(multimediaPlaylistsRef.current).forEach(elementId => {
      if (multimediaPlaylistsRef.current[elementId].timer) {
        window.clearTimeout(multimediaPlaylistsRef.current[elementId].timer);
      }
    });
    multimediaPlaylistsRef.current = {};

    // Initialize preview tracking for each element
    const initialPreviewElements = [
      //...playlistElements.map(element => ({
      //  type: 'playlist' as const,
      //  elementId: element.id,
      //  currentIndex: 0
      //})),
      ...multimediaElements.map(element => ({
        type: 'multimedia' as const,
        elementId: element.id,
        currentIndex: 0
      })),
      ...apiElements.map(element => ({
        type: 'api' as const,
        elementId: element.id,
        currentIndex: 0
      }))
    ];

    setCurrentPreviewElements(initialPreviewElements);

    // Set up multimedia elements with playlist
    multimediaElements.forEach(element => {
      const playlist = element.content?.playlist || [];
      if (playlist.length > 0) {
        // Initialize the multimedia playlist reference
        multimediaPlaylistsRef.current[element.id] = {
          currentIndex: 0,
          isPlaying: true,
          timer: null
        };

        // Update the element to show the first item
        setElements(prev => 
          prev.map(el => 
            el.id === element.id ? { 
              ...el, 
              content: { 
                ...el.content, 
                currentIndex: 0
              } 
            } : el
          )
        );

      }
    });

    // Start preview process for API elements if we have API data
    if (window.__API_DATA__) {
      setupApiDataPreview();
    } else if (apiUrl) {
      // If we have a URL but no data, show a warning toast to the user
      toast({
        title: "API Data Missing",
        description: "Please use the 'Refresh API Data' button to load data before preview.",
        variant: "destructive"
      });
    }

    toast({
      title: "Preview Started",
      description: "Showing preview of all elements. Click Stop to exit preview mode."
    });
  }, [isPreviewActive]);

  // Stop preview mode
  const stopPreview = () => {
    console.log("PREVIEW STOPPING....................");
    // Mark that we're stopping the preview mode
    apiPreviewStoppingRef.current = true;

    // Stop all interval timers for API data cycling
    /*if (previewIntervalRef.current) {
      console.log(`Clearing API cycling interval: ${previewIntervalRef.current}`);
      window.clearInterval(previewIntervalRef.current);
      previewIntervalRef.current = null;
    }*/
    if (apiPreviewTimeoutRef.current) {
      console.log(`Clearing API cycling interval: ${apiPreviewTimeoutRef.current}`);
      window.clearInterval(apiPreviewTimeoutRef.current);
        apiPreviewTimeoutRef.current = null;
    }

    // Stop any video cycling events and other timers
    cleanupPreviewEventListeners();

    // Reset all elements to initial state
    setElements(prev => prev.map(element => {
      // Update playlist, multimedia, and api elements
      if (element.type === 'playlist' || element.type === 'multimedia' || element.type === 'api') {
        return {
          ...element,
          content: {
            ...element.content,
            currentIndex: 0 // Reset to first item
          }
        };
      }
      return element;
    }));

    // Reset the API data index
    currentApiDataIndexRef.current = 0;
    window.__API_DATA_INDEX__ = 0;
    window.__API_DATA_TOTAL_ROWS__ = 0;
    window.__API_DATA_IS_VIDEO_PLAYING__ = false;
    // Clear preview elements and set preview as inactive
    setCurrentPreviewElements([]);
    setIsPreviewActive(false);

    // Reset flag now that stop is complete
    apiPreviewStoppingRef.current = false;

    toast({
      title: "Preview Stopped",
      description: "All elements have been reset to their initial state."
    });
  };

  // Create a new slide
  const handleCreateSlide = async (data: SlideFormValues) => {
    if (!teamId) return;

    try {
      setIsCreateModalOpen(false);
      setIsApiLoading(true); // Show loading state
      
      // Set UI state
      setCanvasSize({ width: data.width, height: data.height });
      setCanvasBackground(data.backgroundColor || "#ffffff");
      setApiUrl(data.apiUrl || "");
      setApiPreviewDuration(data.apiDataPreviewDuration || 0);

      // Ensure default values
      const backgroundColor = data.backgroundColor || "#ffffff";
      const newApiUrl = data.apiUrl || "";

      // Create slide in the database
      const response = await apiRequest('POST', '/api/slides', {
        teamId,
        name: data.name,
        content: [],
        slideWidth: data.width,
        slideHeight: data.height,
        slideBackgroundColor: backgroundColor,
        apiUrl: newApiUrl,
        apiDataPreviewDuration: data.apiDataPreviewDuration || 0,
      });

      // Parse the response to get the slide data
      const slideData = await response.json();

      if (slideData && slideData.id) {
        setCurrentSlideId(slideData.id);
        setCurrentSlideName(slideData.name || data.name);
      } else {
        console.error('Invalid slide data received from server');
      }
      setElements([]);

      // Refresh the slides list
      refetch();

      // Set up the edit form values in case user wants to edit later
      // But don't open the modal automatically
      editForm.setValue("name", data.name);
      editForm.setValue("width", data.width);
      editForm.setValue("height", data.height);
      editForm.setValue("backgroundColor", data.backgroundColor || "#ffffff");
      editForm.setValue("apiUrl", newApiUrl);
      editForm.setValue("apiDataPreviewDuration", data.apiDataPreviewDuration || 0);

      // If API URL is not empty, fetch the API data via proxy
      if (newApiUrl) {
        try {
          // Use the server proxy endpoint to avoid CORS issues
          const encodedUrl = encodeURIComponent(newApiUrl);
          const proxyUrl = `/api/proxy?url=${encodedUrl}`;
          
          console.log(`Fetching API data via proxy: ${proxyUrl}`);
          const apiResponse = await fetch(proxyUrl, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (!apiResponse.ok) {
            throw new Error(`Failed to fetch API data: ${apiResponse.status} ${apiResponse.statusText}`);
          }

          const apiData = await apiResponse.json();

          // Initialize global objects if they don't exist
          if (!window.__API_DATA__) window.__API_DATA__ = {};
          window.__API_DATA_FIELDS__ = []; // Always initialize to empty array
          window.__API_DATA_INDEX__ = 0; // Always reset to 0 when loading new data
          window.__API_DATA_TOTAL_ROWS__ = 0; // Always reset to 0 when loading new data
          window.__API_DATA_IS_VIDEO_PLAYING__ = false;
          
          // Make the API data available to the multimedia elements
          window.__API_DATA__ = apiData;

          // Extract API data fields using the same logic as in fetchApiData
          // Define the extraction function here
          const apiDataColumnExtraction = (obj: any): string[] => {
            const result: string[] = [];
            const seen = new Set<string>();
            const mappings: Record<string, string> = {};
            
            // Helper to identify primitive values (leaf nodes)
            const isPrimitive = (val: any): boolean => {
              return val === null || 
                     val === undefined ||
                     typeof val === 'string' || 
                     typeof val === 'number' || 
                     typeof val === 'boolean';
            };
            
            // Process data recursively to extract column/key names
            function extractColumns(data: any, path: string = '', indexPath: string = '') {
              // Skip null/undefined
              if (data === null || data === undefined) return;
              
              // Handle arrays - use first item but hide index in display
              if (Array.isArray(data)) {
                if (data.length === 0) return;
                
                // Get first item from array (per requirements, only show first record)
                const item = data[0];
                const itemIndexPath = indexPath ? `${indexPath}.0` : `${path}.0`;
                
                // Process based on item type
                if (isPrimitive(item)) {
                  // For array of primitives, add array path directly
                  if (!seen.has(path)) {
                    result.push(path);
                    seen.add(path);
                    mappings[path] = itemIndexPath;
                  }
                } else if (typeof item === 'object') {
                  // Process object properties without showing array index in path
                  extractColumns(item, path, itemIndexPath);
                }
              }
              // Handle objects
              else if (typeof data === 'object') {
                for (const key in data) {
                  const value = data[key];
                  const newPath = path ? `${path}.${key}` : key;
                  const newIndexPath = indexPath ? `${indexPath}.${key}` : newPath;
                  
                  // Add primitive values to result
                  if (isPrimitive(value)) {
                    if (!seen.has(newPath)) {
                      result.push(newPath);
                      seen.add(newPath);
                      // Only add mapping if paths differ
                      if (newPath !== newIndexPath) {
                        mappings[newPath] = newIndexPath;
                      }
                    }
                  }
                  // Process nested structures
                  else if (typeof value === 'object' && value !== null) {
                    extractColumns(value, newPath, newIndexPath);
                  }
                }
              }
            }
            
            // Start extraction
            extractColumns(obj);
            
            // Store mappings in global variables for access between components
            window.__API_PATH_MAPPINGS__ = mappings;
            window.__GET_REAL_PATH__ = (path: string): string => mappings[path] || path;
            
            // Sort results for easier navigation
            return result.sort();
          };
          
          const extractedFields = apiDataColumnExtraction(apiData);
          window.__API_DATA_FIELDS__ = extractedFields;
          
          toast({
            title: "Slide created with API data",
            description: `Slide created and loaded ${extractedFields.length} data fields from API.`,
          });
        } catch (apiError) {
          console.error('Error fetching API data:', apiError);
          toast({
            title: "Slide created, but API data fetch failed",
            description: apiError instanceof Error ? apiError.message : "Failed to load API data for this slide.",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Slide created",
          description: "Your new slide has been created successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Error creating slide",
        description: "There was a problem creating your slide.",
        variant: "destructive",
      });
    } finally {
      setIsApiLoading(false); // Clear loading state
    }
  };

  // Save the current slide
  const handleSaveSlide = async () => {
    if (!currentSlideId || !teamId) return;

    try {
      setIsSaving(true);

      // Clean up duplicate thumbnail_url in playlist elements before saving
      const cleanedElements = elements.map((element: any) => {
        // Only process playlist elements
        if (element.type === 'playlist' && element.content && element.content.playlist) {
          // Clean up each playlist item to remove duplicate thumbnail_url
          const cleanedPlaylist = element.content.playlist.map((item: any) => {
            // Create a new item without the thumbnail_url property if it exists
            if (item.hasOwnProperty('thumbnail_url')) {
              const { thumbnail_url, ...cleanedItem } = item;
              return cleanedItem;
            }
            return item;
          });
          
          // Return the element with cleaned playlist
          return {
            ...element,
            content: {
              ...element.content,
              playlist: cleanedPlaylist
            }
          };
        }
        // Return other elements unchanged
        return element;
      });

      // Update slide in the database
      await apiRequest('PATCH', `/api/slides/${currentSlideId}`, {
        teamId,
        content: cleanedElements,
        slideBackgroundColor: canvasBackground,
        apiUrl: apiUrl,
        apiDataPreviewDuration: apiPreviewDuration,
      });

      // Update the local state with the cleaned elements
      setElements(cleanedElements);

      toast({
        title: "Slide saved",
        description: "Your slide has been saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error saving slide",
        description: "There was a problem saving your slide.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Load a slide
  const handleLoadSlide = async (slideId: string) => {
    try {
      setIsApiLoading(true);  // Show loading state
      const response = await fetch(`/api/slides/${slideId}`);

      if (!response.ok) {
        throw new Error(`Failed to load slide: ${response.status} ${response.statusText}`);
      }

      const slideData = await response.json();

      if (!slideData || !slideData.id) {
        throw new Error('Invalid slide data received from server');
      }

      // Get the new API URL from slide data
      const newApiUrl = slideData.apiUrl || "";
      const newApiPreviewDuration = slideData.apiDataPreviewDuration || 0;
      const apiUrlChanged = newApiUrl !== apiUrl && newApiUrl !== "";

      // Clean up duplicate thumbnail_url in playlist elements
      const cleanedContent = (slideData.content || []).map((element: any) => {
        // Only process playlist elements
        if (element.type === 'playlist' && element.content && element.content.playlist) {
          // Clean up each playlist item to remove duplicate thumbnail_url
          const cleanedPlaylist = element.content.playlist.map((item: any) => {
            // Create a new item without the thumbnail_url property
            const { thumbnail_url, ...cleanedItem } = item;
            return cleanedItem;
          });
          
          // Return the element with cleaned playlist
          return {
            ...element,
            content: {
              ...element.content,
              playlist: cleanedPlaylist
            }
          };
        }
        // Return other elements unchanged
        return element;
      });

      // Set all the slide data
      setCurrentSlideId(slideData.id);
      setCurrentSlideName(slideData.name || 'Untitled Slide');
      setCanvasSize({ 
        width: slideData.slideWidth || slideData.width || 1920, 
        height: slideData.slideHeight || slideData.height || 1080
      });
      setCanvasBackground(slideData.slideBackgroundColor || "#ffffff");
      setApiUrl(newApiUrl);
      setApiPreviewDuration(newApiPreviewDuration);
      setElements(cleanedContent);
      setIsCreateModalOpen(false);

      // If API URL is not empty, automatically fetch the API data
      if (newApiUrl) {
        try {
          // We set apiUrl state above, but it won't be updated in the current execution context
          // so we need to use newApiUrl directly for the fetch
          
          // Use the server proxy endpoint to avoid CORS issues
          const encodedUrl = encodeURIComponent(newApiUrl);
          const proxyUrl = `/api/proxy?url=${encodedUrl}`;
          
          console.log(`Fetching API data via proxy: ${proxyUrl}`);
          const apiResponse = await fetch(proxyUrl, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
            },
          });

          if (!apiResponse.ok) {
            throw new Error(`Failed to fetch API data: ${apiResponse.status} ${apiResponse.statusText}`);
          }

          const data = await apiResponse.json();

          // Initialize global objects if they don't exist
          if (!window.__API_DATA__) window.__API_DATA__ = {};
          window.__API_DATA_FIELDS__ = []; // Always initialize to empty array
          window.__API_DATA_INDEX__ = 0; // Always reset to 0 when loading new data
          window.__API_DATA_TOTAL_ROWS__ = 0; // Always reset to 0 when loading new data
          window.__API_DATA_IS_VIDEO_PLAYING__ = false;
          
          // Make the API data available to the multimedia elements
          window.__API_DATA__ = data;

          // Use the existing function to extract API data fields
          const apiDataColumnExtraction = (obj: any): string[] => {
            // This is the same implementation as in fetchApiData
            // Re-using the existing function logic here
            const result: string[] = [];
            const seen = new Set<string>();
            const mappings: Record<string, string> = {};
            
            // Helper to identify primitive values (leaf nodes)
            const isPrimitive = (val: any): boolean => {
              return val === null || 
                     val === undefined ||
                     typeof val === 'string' || 
                     typeof val === 'number' || 
                     typeof val === 'boolean';
            };
            
            // Process data recursively to extract column/key names
            function extractColumns(data: any, path: string = '', indexPath: string = '') {
              // Skip null/undefined
              if (data === null || data === undefined) return;
              
              // Handle arrays - use first item but hide index in display
              if (Array.isArray(data)) {
                if (data.length === 0) return;
                
                // Get first item from array (per requirements, only show first record)
                const item = data[0];
                const itemIndexPath = indexPath ? `${indexPath}.0` : `${path}.0`;
                
                // Process based on item type
                if (isPrimitive(item)) {
                  // For array of primitives, add array path directly
                  if (!seen.has(path)) {
                    result.push(path);
                    seen.add(path);
                    mappings[path] = itemIndexPath;
                  }
                } else if (typeof item === 'object') {
                  // Process object properties without showing array index in path
                  extractColumns(item, path, itemIndexPath);
                }
              }
              // Handle objects
              else if (typeof data === 'object') {
                for (const key in data) {
                  const value = data[key];
                  const newPath = path ? `${path}.${key}` : key;
                  const newIndexPath = indexPath ? `${indexPath}.${key}` : newPath;
                  
                  // Add primitive values to result
                  if (isPrimitive(value)) {
                    if (!seen.has(newPath)) {
                      result.push(newPath);
                      seen.add(newPath);
                      // Only add mapping if paths differ
                      if (newPath !== newIndexPath) {
                        mappings[newPath] = newIndexPath;
                      }
                    }
                  }
                  // Process nested structures
                  else if (typeof value === 'object' && value !== null) {
                    extractColumns(value, newPath, newIndexPath);
                  }
                }
              }
            }
            
            // Start extraction
            extractColumns(obj);
            
            // Store mappings in global variables for access between components
            window.__API_PATH_MAPPINGS__ = mappings;
            window.__GET_REAL_PATH__ = (path: string): string => mappings[path] || path;
            
            // Sort results for easier navigation
            return result.sort();
          };
          
          // Store the extracted fields in the global object
          const extractedFields = apiDataColumnExtraction(data);
          window.__API_DATA_FIELDS__ = extractedFields;
          
          // Show success toast
          toast({
            title: "API Data Loaded",
            description: `Successfully loaded ${extractedFields.length} data fields from API.`,
          });
        } catch (apiError) {
          console.error('Error fetching API data:', apiError);
          toast({
            title: "Error loading API data",
            description: apiError instanceof Error ? apiError.message : "Failed to load API data for this slide.",
            variant: "destructive",
          });
        }
      } else if (apiUrlChanged) {
        // If API URL changed but is now empty, show a notification
        toast({
          title: "API URL Changed",
          description: "API URL has been cleared for this slide.",
        });
      }
    } catch (error) {
      console.error('Error loading slide:', error);
      toast({
        title: "Error loading slide",
        description: "There was a problem loading the slide. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsApiLoading(false); // Clear loading state
    }
  };

  return (
    <MainLayout title={currentSlideName || "Slide Designer"}>
      <div className="flex h-[calc(100vh-5rem)] overflow-hidden border rounded-md">
        {/* Toolbox with Error Boundary */}
        <Toolbox 
          teamId={teamId} 
          onAddElement={handleAddElement} 
          selectedElement={getSelectedElement()}
          onAddToPlaylist={handleAddToPlaylist}
        />

        {/* Canvas Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Toolbar */}
          <div className="bg-background border-b p-2 flex justify-between items-center">
            <div>
              <Button variant="outline" size="sm" onClick={() => setIsCreateModalOpen(true)}>
                <ImagePlus className="mr-2 h-4 w-4" />
                New Slide
              </Button>
              {/* Always render the dropdown container but conditionally render SelectItems based on slides */}
              <div className="inline-block ml-2 w-48">
                <Select
                  value={currentSlideId || ""}
                  onValueChange={handleLoadSlide}
                  disabled={!slides || slides.length === 0}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder="Load slide" />
                  </SelectTrigger>
                  <SelectContent>
                    {slides && slides.length > 0 ? (
                      slides.map(slide => (
                        <SelectItem key={slide.id} value={slide.id}>
                          {slide.name}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="no-slides" disabled>No slides found</SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="icon" 
                disabled={!selectedElementId}
                onClick={handleBringToFront}
                title="Move Forward (+1)"
              >
                <ArrowUpToLine className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                disabled={!selectedElementId}
                onClick={handleSendToBack}
                title="Move Backward (-1)"
              >
                <ArrowDownToLine className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline" 
                size="icon" 
                disabled={elements.length === 0}
                onClick={() => setElements([])}
                title="Clear Canvas"
              >
                <Trash className="h-4 w-4" />
              </Button>
              <Button 
                variant="outline"
                size="icon"
                disabled={!currentSlideId}
                onClick={() => {
                  if (currentSlideId) {
                    // Set the edit form values based on current slide properties
                    editForm.setValue("name", currentSlideName);
                    editForm.setValue("width", canvasSize.width);
                    editForm.setValue("height", canvasSize.height);
                    editForm.setValue("backgroundColor", canvasBackground);
                    editForm.setValue("apiUrl", apiUrl);
                    editForm.setValue("apiDataPreviewDuration", apiPreviewDuration);
                    setIsEditModalOpen(true);
                  }
                }}
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button 
                variant={isPreviewActive ? "default" : "outline"}
                size="sm"
                onClick={togglePreview}
                disabled={elements.length === 0 || !currentSlideId}
                title={isPreviewActive ? "Stop Preview" : "Start Preview"}
                className={isPreviewActive ? "bg-red-500 hover:bg-red-600" : ""}
              >
                {isPreviewActive ? (
                  <>
                    <Square className="mr-2 h-4 w-4" />
                    Stop
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Preview
                  </>
                )}
              </Button>
              {apiUrl && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchApiData}
                  disabled={isApiLoading}
                  className="ml-auto"
                >
                  {isApiLoading ? "Loading API..." : "Refresh API Data"}
                </Button>
              )}
              <Button 
                variant="default"
                size="sm"
                onClick={handleSaveSlide}
                disabled={!currentSlideId || isSaving}
                className="ml-auto"
              >
                <SaveIcon className="mr-2 h-4 w-4" />
                {isSaving ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>

          {/* Canvas */}
          <div className="flex-1 overflow-auto bg-gray-100">
            <ErrorBoundary componentName="Canvas">
              <Canvas 
                elements={elements} 
                backgroundColor={canvasBackground}
                width={canvasSize.width} 
                height={canvasSize.height}
                selectedElementId={selectedElementId}
                onSelectElement={setSelectedElementId}
                onUpdateElement={handleUpdateElement}
                onDeleteElement={handleDeleteElement}
                isPreviewMode={isPreviewActive}
                currentPreviewElements={currentPreviewElements}
                cycleToNextRecord={cycleToNextRecord}
              />
            </ErrorBoundary>
          </div>
        </div>

        {/* Properties Panel with Error Boundary */}
        <ErrorBoundary componentName="ElementProperties">
          <ElementProperties
            selectedElement={getSelectedElement()}
            onUpdateElement={handleUpdateElement}
            onDeleteElement={handleDeleteElement}
            apiData={window.__API_DATA__}
            apiPreviewDuration={apiPreviewDuration}
          />
        </ErrorBoundary>
      </div>

      {/* New Slide Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Slide</DialogTitle>
            <DialogDescription>
              Create a new slide with the specified dimensions and settings.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleCreateSlide)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slide Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width (px)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field} 
                        onChange={e => field.onChange(Number(e.target.value))} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
                <FormField
                  control={form.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (px)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          onChange={e => field.onChange(Number(e.target.value))} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="backgroundColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Background Color</FormLabel>
                    <FormControl>
                      <ColorPicker value={field.value || "#ffffff"} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="apiUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API URL (Optional)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="https://api.example.com/data" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="apiDataPreviewDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Data Preview Duration (seconds)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field} 
                        onChange={e => field.onChange(Number(e.target.value))} 
                        placeholder="10"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="submit">Create Slide</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Slide Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Slide Settings</DialogTitle>
            <DialogDescription>
              Modify the current slide's dimensions and settings.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(async (data) => {
              if (!currentSlideId || !teamId) return;

              try {
                setIsEditModalOpen(false);
                setIsApiLoading(true); // Show loading state
                
                // Check if API URL has changed
                const newApiUrl = data.apiUrl || "";
                const apiUrlChanged = newApiUrl !== apiUrl && newApiUrl !== "";
                
                // Update UI state
                setCanvasSize({ width: data.width, height: data.height });
                setCanvasBackground(data.backgroundColor || "#ffffff");
                setCurrentSlideName(data.name);
                setApiUrl(newApiUrl);
                setApiPreviewDuration(data.apiDataPreviewDuration || 0);

                // Update the slide in the database
                await apiRequest('PATCH', `/api/slides/${currentSlideId}`, {
                  teamId,
                  name: data.name,
                  slideWidth: data.width,
                  slideHeight: data.height,
                  slideBackgroundColor: data.backgroundColor || "#ffffff",
                  apiUrl: newApiUrl,
                  apiDataPreviewDuration: data.apiDataPreviewDuration || 0
                });

                // Refresh the slides list
                refetch();
                
                // If API URL is not empty and has changed, fetch the API data
                if (newApiUrl && apiUrlChanged) {
                  try {
                    // Use the server proxy endpoint to avoid CORS issues
                    const encodedUrl = encodeURIComponent(newApiUrl);
                    const proxyUrl = `/api/proxy?url=${encodedUrl}`;
                    
                    console.log(`Fetching API data via proxy: ${proxyUrl}`);
                    const apiResponse = await fetch(proxyUrl, {
                      method: "GET",
                      headers: {
                        "Content-Type": "application/json",
                      },
                    });

                    if (!apiResponse.ok) {
                      throw new Error(`Failed to fetch API data: ${apiResponse.status} ${apiResponse.statusText}`);
                    }

                    const apiData = await apiResponse.json();

                    // Initialize global objects if they don't exist
                    if (!window.__API_DATA__) window.__API_DATA__ = {};
                    window.__API_DATA_FIELDS__ = []; // Always initialize to empty array
                    window.__API_DATA_INDEX__ = 0; // Always reset to 0 when loading new data
                    window.__API_DATA_TOTAL_ROWS__ = 0; // Always reset to 0 when loading new data
                    window.__API_DATA_IS_VIDEO_PLAYING__ = false;
                    
                    // Make the API data available to the multimedia elements
                    window.__API_DATA__ = apiData;

                    // Extract API data fields using the same logic as in fetchApiData
                    // Redefine the extraction function here (same implementation as in fetchApiData and handleLoadSlide)
                    const apiDataColumnExtraction = (obj: any): string[] => {
                      const result: string[] = [];
                      const seen = new Set<string>();
                      const mappings: Record<string, string> = {};
                      
                      // Helper to identify primitive values (leaf nodes)
                      const isPrimitive = (val: any): boolean => {
                        return val === null || 
                               val === undefined ||
                               typeof val === 'string' || 
                               typeof val === 'number' || 
                               typeof val === 'boolean';
                      };
                      
                      // Process data recursively to extract column/key names
                      function extractColumns(data: any, path: string = '', indexPath: string = '') {
                        // Skip null/undefined
                        if (data === null || data === undefined) return;
                        
                        // Handle arrays - use first item but hide index in display
                        if (Array.isArray(data)) {
                          if (data.length === 0) return;
                          
                          // Get first item from array (per requirements, only show first record)
                          const item = data[0];
                          const itemIndexPath = indexPath ? `${indexPath}.0` : `${path}.0`;
                          
                          // Process based on item type
                          if (isPrimitive(item)) {
                            // For array of primitives, add array path directly
                            if (!seen.has(path)) {
                              result.push(path);
                              seen.add(path);
                              mappings[path] = itemIndexPath;
                            }
                          } else if (typeof item === 'object') {
                            // Process object properties without showing array index in path
                            extractColumns(item, path, itemIndexPath);
                          }
                        }
                        // Handle objects
                        else if (typeof data === 'object') {
                          for (const key in data) {
                            const value = data[key];
                            const newPath = path ? `${path}.${key}` : key;
                            const newIndexPath = indexPath ? `${indexPath}.${key}` : newPath;
                            
                            // Add primitive values to result
                            if (isPrimitive(value)) {
                              if (!seen.has(newPath)) {
                                result.push(newPath);
                                seen.add(newPath);
                                // Only add mapping if paths differ
                                if (newPath !== newIndexPath) {
                                  mappings[newPath] = newIndexPath;
                                }
                              }
                            }
                            // Process nested structures
                            else if (typeof value === 'object' && value !== null) {
                              extractColumns(value, newPath, newIndexPath);
                            }
                          }
                        }
                      }
                      
                      // Start extraction
                      extractColumns(obj);
                      
                      // Store mappings in global variables for access between components
                      window.__API_PATH_MAPPINGS__ = mappings;
                      window.__GET_REAL_PATH__ = (path: string): string => mappings[path] || path;
                      
                      // Sort results for easier navigation
                      return result.sort();
                    };
                    
                    const extractedFields = apiDataColumnExtraction(apiData);
                    window.__API_DATA_FIELDS__ = extractedFields;
                    
                    toast({
                      title: "Slide updated with API data",
                      description: `Slide updated and loaded ${extractedFields.length} data fields from API.`,
                    });
                  } catch (apiError) {
                    console.error('Error fetching API data:', apiError);
                    toast({
                      title: "Slide updated, but API data fetch failed",
                      description: apiError instanceof Error ? apiError.message : "Failed to load API data for this slide.",
                      variant: "destructive",
                    });
                  }
                } else {
                  toast({
                    title: "Slide updated",
                    description: "Your slide settings have been updated successfully.",
                  });
                }
              } catch (error) {
                toast({
                  title: "Error updating slide",
                  description: "There was a problem updating your slide settings.",
                  variant: "destructive",
                });
              } finally {
                setIsApiLoading(false); // Clear loading state
              }
            })} className="space-y-6">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Slide Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width (px)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          onChange={e => field.onChange(Number(e.target.value))} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height (px)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field} 
                          onChange={e => field.onChange(Number(e.target.value))} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={editForm.control}
                name="backgroundColor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Background Color</FormLabel>
                    <FormControl>
                      <ColorPicker value={field.value || "#ffffff"} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="apiUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API URL (Optional)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="https://api.example.com/data" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="apiDataPreviewDuration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API Data Preview Duration (seconds)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field} 
                        onChange={e => field.onChange(Number(e.target.value))} 
                        placeholder="10"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="submit">Update Slide</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}