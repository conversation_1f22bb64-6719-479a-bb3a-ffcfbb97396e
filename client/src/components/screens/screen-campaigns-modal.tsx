import React, { useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useScreenCampaigns } from "@/hooks/use-screen-campaigns";
import { Presentation, Calendar, Check, Info, Trash2, PlusCircle } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface ScreenCampaignsModalProps {
  screenId: string;
  screenName: string;
  teamId: string;
  isOpen: boolean;
  onClose: () => void;
}

export function ScreenCampaignsModal({
  screenId,
  screenName,
  teamId,
  isOpen,
  onClose,
}: ScreenCampaignsModalProps) {
  const [selectedCampaignId, setSelectedCampaignId] = useState<string>("");

  const {
    screenCampaigns,
    availableCampaigns,
    isLoading,
    isEmpty,
    addCampaignToScreen,
    removeCampaignFromScreen,
  } = useScreenCampaigns(screenId, teamId);

  const handleAddCampaign = async () => {
    if (!selectedCampaignId) return;

    await addCampaignToScreen.mutateAsync({
      campaignId: selectedCampaignId,
    });

    // Reset selection after adding
    setSelectedCampaignId("");
  };

  const handleRemoveCampaign = async (campaignId: string) => {
    await removeCampaignFromScreen.mutateAsync({
      campaignId,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Screen Campaigns</DialogTitle>
          <DialogDescription>
            Manage campaigns for {screenName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Add campaign section */}
          <div>
            <h3 className="text-sm font-medium mb-2">Add Campaign</h3>
            <div className="flex gap-2">
              <Select
                value={selectedCampaignId}
                onValueChange={setSelectedCampaignId}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a campaign" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Active Campaigns</SelectLabel>
                    {availableCampaigns?.length === 0 ? (
                      <div className="px-2 py-1.5 text-sm text-muted-foreground">
                        No active campaigns available
                      </div>
                    ) : (
                      availableCampaigns?.map((campaign) => (
                        <SelectItem key={campaign.id} value={campaign.id}>
                          {campaign.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectGroup>
                </SelectContent>
              </Select>
              <Button
                onClick={handleAddCampaign}
                disabled={!selectedCampaignId || addCampaignToScreen.isPending}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>
          </div>

          <Separator />

          {/* Campaign list section */}
          <div>
            <h3 className="text-sm font-medium mb-2">Current Campaigns</h3>

            {isLoading ? (
              <div className="py-8 text-center">
                <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
                <p className="text-sm text-muted-foreground">Loading campaigns...</p>
              </div>
            ) : isEmpty ? (
              <div className="py-8 text-center border rounded-md">
                <Presentation className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No campaigns assigned to this screen</p>
              </div>
            ) : (
              <div className="border rounded-md divide-y max-h-[300px] overflow-y-auto">
                {screenCampaigns?.map((item) => {
                  // Find the full campaign details if available
                  const campaign = availableCampaigns?.find(c => c.id === item.campaignId) || item.campaign;

                  return (
                    <div key={item.campaignId} className="p-3 flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{campaign?.name || 'Unknown Campaign'}</h4>
                          <Badge
                            variant={campaign?.status === "active" ? "default" : campaign?.status === "scheduled" ? "secondary" : "destructive"}
                            className={campaign?.status === "active" ? "bg-[#05DAC3] text-[#020627] hover:bg-[#05DAC3]/80" : ""}
                          >
                            {campaign?.status === "active" && <Check className="h-3 w-3 mr-1" />}
                            {campaign?.status ? campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1) : "Active"}
                          </Badge>
                        </div>
                        {campaign?.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {campaign.description}
                          </p>
                        )}
                        <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {campaign?.startDate ? formatDate(campaign.startDate) : 'N/A'} -
                            {campaign?.endDate ? formatDate(campaign.endDate) : 'N/A'}
                          </div>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveCampaign(item.campaignId)}
                        disabled={removeCampaignFromScreen.isPending}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}