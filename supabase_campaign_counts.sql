-- This script creates PostgreSQL functions to retrieve campaigns with counts
-- Execute this in your Supabase SQL editor or directly on your PostgreSQL database

-- Drop both functions if they already exist to recreate them
DROP FUNCTION IF EXISTS public.get_campaigns_with_counts(uuid);
DROP FUNCTION IF EXISTS public.get_campaign_with_counts(uuid);

-- Function 1: Get multiple campaigns with counts by team ID
CREATE OR REPLACE FUNCTION public.get_campaigns_with_counts(team_id_param uuid)
RETURNS TABLE(
  id uuid,
  team_id uuid,
  name text,
  description text,
  start_date timestamp with time zone,
  end_date timestamp with time zone,
  status text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  screen_count bigint,
  media_count bigint,
  slide_count bigint
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id,
    c.team_id,
    c.name,
    c.description,
    c.start_date,
    c.end_date,
    c.status,
    c.created_at,
    c.updated_at,
    -- Count of screens
    COALESCE((SELECT COUNT(*)
      FROM campaign_screens cs
      WHERE cs.campaign_id = c.id), 0) AS screen_count,
    -- Count of media (campaign_type = 0)
    COALESCE((SELECT COUNT(*)
      FROM campaign_medias cm
      WHERE cm.campaign_id = c.id AND cm.campaign_type = 0), 0) AS media_count,
    -- Count of slides (campaign_type = 1)
    COALESCE((SELECT COUNT(*)
      FROM campaign_medias cm
      WHERE cm.campaign_id = c.id AND cm.campaign_type = 1), 0) AS slide_count
  FROM
    campaigns c
  WHERE
    c.team_id = team_id_param;
END;
$$;

-- Function 2: Get a single campaign with counts by campaign ID
CREATE OR REPLACE FUNCTION public.get_campaign_with_counts(campaign_id_param uuid)
RETURNS TABLE(
  id uuid,
  team_id uuid,
  name text,
  description text,
  start_date timestamp with time zone,
  end_date timestamp with time zone,
  status text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone,
  screen_count bigint,
  media_count bigint,
  slide_count bigint
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    c.id,
    c.team_id,
    c.name,
    c.description,
    c.start_date,
    c.end_date,
    c.status,
    c.created_at,
    c.updated_at,
    -- Count of screens
    COALESCE((SELECT COUNT(*)
      FROM campaign_screens cs
      WHERE cs.campaign_id = c.id), 0) AS screen_count,
    -- Count of media (campaign_type = 0)
    COALESCE((SELECT COUNT(*)
      FROM campaign_medias cm
      WHERE cm.campaign_id = c.id AND cm.campaign_type = 0), 0) AS media_count,
    -- Count of slides (campaign_type = 1)
    COALESCE((SELECT COUNT(*)
      FROM campaign_medias cm
      WHERE cm.campaign_id = c.id AND cm.campaign_type = 1), 0) AS slide_count
  FROM
    campaigns c
  WHERE
    c.id = campaign_id_param;
END;
$$;

-- Grant execution privileges to authenticated users for both functions
GRANT EXECUTE ON FUNCTION public.get_campaigns_with_counts(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_campaigns_with_counts(uuid) TO anon;
GRANT EXECUTE ON FUNCTION public.get_campaigns_with_counts(uuid) TO service_role;

GRANT EXECUTE ON FUNCTION public.get_campaign_with_counts(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_campaign_with_counts(uuid) TO anon;
GRANT EXECUTE ON FUNCTION public.get_campaign_with_counts(uuid) TO service_role;