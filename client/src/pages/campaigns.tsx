import { useState, useEffect, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { MainLayout } from "@/components/layout/main-layout";
import { CampaignCard } from "@/components/campaigns/campaign-card";
import { CampaignEditModal } from "@/components/campaigns/campaign-edit-modal-new";
import { useCampaigns } from "@/hooks/use-campaigns";
import { useScreens } from "@/hooks/use-screens";
import { useMedia } from "@/hooks/use-media";
import { useSlides } from "@/hooks/use-slides";
import { useAuth } from "@/hooks/use-auth";
import { TagInput } from "@/components/media/tag-input";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";

import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  PlusCircle,
  Search,
  SlidersHorizontal,
  Calendar,
  Check,
  X,
  Presentation,
  CalendarRange,
  Monitor,
  Image as ImageIcon
} from "lucide-react";
import { format } from "date-fns";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

const campaignSchema = z.object({
  name: z.string().min(1, "Name is required"),
  startDate: z.date({
    required_error: "Start date is required",
  }),
  endDate: z.date({
    required_error: "End date is required",
  }),
  status: z.enum(["active", "scheduled", "draft", "paused", "completed"]),
});

type CampaignFormValues = z.infer<typeof campaignSchema>;

export default function Campaigns() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [teamId, setTeamId] = useState<string>("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCampaignId, setSelectedCampaignId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedScreens, setSelectedScreens] = useState<string[]>([]);
  const [selectedMedia, setSelectedMedia] = useState<string[]>([]);
  const [screenSearchOpen, setScreenSearchOpen] = useState(false);
  const [mediaSearchOpen, setMediaSearchOpen] = useState(false);
  const [screenSearchQuery, setScreenSearchQuery] = useState("");
  const [mediaSearchQuery, setMediaSearchQuery] = useState("");
  const [selectedScreenStatusTag, setSelectedScreenStatusTag] = useState<string>("");
  const [selectedScreenTag, setSelectedScreenTag] = useState<string>("");
  const [selectedMediaTag, setSelectedMediaTag] = useState<string>("");

  // State for tags
  const [screenTags, setScreenTags] = useState<any[]>([]);
  const [mediaTags, setMediaTags] = useState<any[]>([]);

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });

  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  const {
    campaigns,
    isLoading,
    isEmpty,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    useCampaign,
    useCampaignMedia,
    useCampaignScreens,
    useCampaignSlides,
    addMediaToCampaign,
    addScreenToCampaign,
    removeMediaFromCampaign,
    removeScreenFromCampaign,
    addSlideToCampaign,
    removeSlideFromCampaign,
  } = useCampaigns(teamId);

  const { screens } = useScreens(teamId);
  const { mediaItems } = useMedia(teamId);
  const { slides } = useSlides(teamId);

  const { data: selectedCampaign } = useCampaign(selectedCampaignId);
  const { data: campaignScreens } = useCampaignScreens(selectedCampaignId);
  const { data: campaignMedia } = useCampaignMedia(selectedCampaignId);
  const { data: campaignSlides } = useCampaignSlides(selectedCampaignId);

  const form = useForm<CampaignFormValues>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      name: "",
      startDate: new Date(),
      endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
      status: "draft",
    },
  });

  // Use the optimized tags hook to avoid excessive requests
  const { data: tagsWithAssociations } = useQuery<any[]>({
    queryKey: [`/api/teams/${teamId}/tags-with-associations`],
    enabled: !!teamId && isEditModalOpen,
    staleTime: 0, // Always fresh for real-time updates
    refetchOnWindowFocus: false,
  });

  // Set tags when data is loaded
  useEffect(() => {
    if (tagsWithAssociations && isEditModalOpen) {
      // Transform the optimized data format for the UI
      const transformedTags = tagsWithAssociations.map(tag => ({
        ...tag,
        screens: tag.screenIds || [],
        media: tag.mediaIds || []
      }));

      setScreenTags(transformedTags);
      setMediaTags(transformedTags);
    }
  }, [tagsWithAssociations, isEditModalOpen]);

  // Handle form data when campaign selected
  useEffect(() => {
    if (selectedCampaign && isEditModalOpen) {
      form.reset({
        name: selectedCampaign.name,
        startDate: new Date(selectedCampaign.startDate),
        endDate: new Date(selectedCampaign.endDate),
        status: selectedCampaign.status as any,
      });

      // Set selected screens and media when edit modal opens
      if (campaignScreens) {
        setSelectedScreens(campaignScreens.map(cs => cs.screenId));
      }

      if (campaignMedia) {
        // Sort campaign media by display_order before setting selectedMedia
        const sortedMedia = [...campaignMedia].sort((a, b) =>
          (a.displayOrder ?? 0) - (b.displayOrder ?? 0)
        );
        setSelectedMedia(sortedMedia.map(cm => cm.mediaId));
      }
    } else if (!isEditModalOpen && !isAddModalOpen) {
      form.reset({
        name: "",
        startDate: new Date(),
        endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
        status: "draft",
      });
      setSelectedScreens([]);
      setSelectedMedia([]);
      setSelectedScreenStatusTag("");
      setSelectedScreenTag("");
      setSelectedMediaTag("");
    }
  }, [selectedCampaign, isEditModalOpen, campaignScreens, campaignMedia, form, isAddModalOpen]);

  // Filter campaigns
  const filteredCampaigns = campaigns?.filter(campaign => {
    // Filter by search query
    const matchesSearch = !searchQuery ||
      campaign.name.toLowerCase().includes(searchQuery.toLowerCase());

    // Filter by status
    const matchesStatus = statusFilter === "all" || campaign.status === statusFilter;

    // We don't have tags on campaigns directly, but could filter by associated screens or media tags

    return matchesSearch && matchesStatus;
  });

  const handleAddCampaign = async (data: CampaignFormValues) => {
    try {
      // 1. Create campaign first
      console.log("Step 1: Creating new campaign");
      const newCampaign = await createCampaign({
        ...data,
        teamId,
      });

      if (!newCampaign || !newCampaign.id) {
        throw new Error("Failed to create campaign - no ID returned");
      }

      console.log(`Campaign created successfully with ID: ${newCampaign.id}`);

      // 2. Add media/slides to the campaign
      if (selectedMedia.length > 0) {
        console.log(`Step 2: Adding ${selectedMedia.length} media/slides to campaign`);

        // Add all media items to the campaign in order using Promise.all
        const mediaPromises = selectedMedia.map((mediaId, i) => {
          // Determine if this is a slide or media
          const isSlide = slides?.some(slide => slide.id === mediaId) || false;
          const type = isSlide ? "slide" : "media";
          console.log(`  Adding ${type} with ID ${mediaId} at position ${i}`);

          return addMediaToCampaign({
            campaignId: newCampaign.id,
            mediaId: mediaId,
            order: i, // Use position in array for ordering
            campaignType: isSlide ? 1 : 0 // 1 for slides, 0 for media
          });
        });

        // Wait for all media to be added
        await Promise.all(mediaPromises);
        console.log("  All media/slides added successfully");
      }

      // 3. Add selected screens to the campaign
      if (selectedScreens.length > 0) {
        console.log(`Step 3: Adding ${selectedScreens.length} screens to campaign`);

        // Create an array of promises for adding all screens in parallel
        const screenPromises = selectedScreens.map(screenId => {
          console.log(`  Adding screen with ID ${screenId}`);
          return addScreenToCampaign({
            campaignId: newCampaign.id,
            screenId
          });
        });

        // Wait for all screens to be added
        await Promise.all(screenPromises);
        console.log("  All screens added successfully");
      }

      // 4. Show success and close modal
      console.log("Step 4: Campaign creation completed successfully");
      toast({
        title: "Campaign Created",
        description: "Campaign has been created successfully with all selected media and screens.",
        variant: "default"
      });

      setIsAddModalOpen(false);

    } catch (error) {
      console.error("Error creating campaign:", error);
      toast({
        title: "Error Creating Campaign",
        description: "There was an error creating the campaign. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleEditCampaign = async (data: CampaignFormValues, mediaOrder?: string[]) => {
    if (!selectedCampaignId) return;

    // If we received an updated mediaOrder, use it instead of selectedMedia
    if (mediaOrder && mediaOrder.length > 0) {
      console.log(`Received updated media order with ${mediaOrder.length} items`);
      // Update our state with the ordered media
      setSelectedMedia(mediaOrder);
    }

    // Use mediaOrder if provided, otherwise fallback to selectedMedia
    const orderedMedia = mediaOrder && mediaOrder.length > 0 ? mediaOrder : selectedMedia;

    try {
      // 1. First, delete all existing media/slides from campaign
      const currentMediaIds = campaignMedia?.map(cm => cm.mediaId) || [];
      console.log(`Step 1: Removing ${currentMediaIds.length} existing media/slides from campaign`);

      if (currentMediaIds.length > 0) {
        const mediaRemovalPromises = currentMediaIds.map(mediaId => {
          return removeMediaFromCampaign({
            campaignId: selectedCampaignId,
            mediaId
          });
        });

        await Promise.all(mediaRemovalPromises);
        console.log("Successfully removed all existing media/slides");
      }

      // 2. Delete all existing screens from campaign
      const currentScreenIds = campaignScreens?.map(cs => cs.screenId) || [];
      console.log(`Step 2: Removing ${currentScreenIds.length} existing screens from campaign`);

      if (currentScreenIds.length > 0) {
        const screenRemovalPromises = currentScreenIds.map(screenId => {
          return removeScreenFromCampaign({
            campaignId: selectedCampaignId,
            screenId
          });
        });

        await Promise.all(screenRemovalPromises);
        console.log("Successfully removed all existing screens");
      }

      // 3. Update campaign details
      console.log("Step 3: Updating campaign details");
      await updateCampaign({
        id: selectedCampaignId,
        data: {
          ...data,
          teamId,
        }
      });
      console.log("Successfully updated campaign details");

      // 4. Add all media items with their ordered positions
      console.log(`Step 4: Adding ${orderedMedia.length} media/slides to campaign`);
      if (orderedMedia.length > 0) {
        const mediaAddPromises = orderedMedia.map((mediaId, i) => {
          const isSlide = slides?.some(slide => slide.id === mediaId) || false;
          const type = isSlide ? "slide" : "media";
          console.log(`Adding ${type} with ID ${mediaId} at position ${i} (campaign_type=${isSlide ? 1 : 0})`);

          return addMediaToCampaign({
            campaignId: selectedCampaignId,
            mediaId: mediaId,
            order: i, // Use position in the array for ordering (0-based)
            campaignType: isSlide ? 1 : 0 // 1 for slides, 0 for media
          });
        });

        await Promise.all(mediaAddPromises);
        console.log("Successfully added all media/slides");
      }

      // 5. Add all screen items
      console.log(`Step 5: Adding ${selectedScreens.length} screens to campaign`);
      if (selectedScreens.length > 0) {
        const screenAddPromises = selectedScreens.map(screenId => {
          return addScreenToCampaign({
            campaignId: selectedCampaignId,
            screenId
          });
        });

        await Promise.all(screenAddPromises);
        console.log("Successfully added all screens");
      }

      // 6. Show success and close modal
      console.log("Step 6: All operations completed successfully");
      toast({
        title: "Campaign Updated",
        description: "Campaign has been updated successfully with all media, slides and screens.",
        variant: "default"
      });

      // Close the modal
      setIsEditModalOpen(false);

    } catch (error) {
      console.error("Error updating campaign:", error);
      toast({
        title: "Error Updating Campaign",
        description: "There was an error updating the campaign. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteCampaign = (id: string) => {
    deleteCampaign(id);
  };

  const [_, navigate] = useLocation();

  const handleEditClick = (id: string) => {
    navigate(`/campaigns/${id}`);
  };

  const handleDuplicateCampaign = (id: string) => {
    // Find the campaign to duplicate
    const campaign = campaigns?.find(c => c.id === id);
    if (!campaign) return;

    // Open add modal with campaign data
    form.reset({
      name: `${campaign.name} (Copy)`,
      startDate: new Date(campaign.startDate),
      endDate: new Date(campaign.endDate),
      status: "draft", // Always start as draft
    });

    setIsAddModalOpen(true);
  };

  const handleToggleActive = (id: string, currentStatus: string) => {
    updateCampaign({
      id,
      data: {
        status: currentStatus === "active" ? "paused" : "active"
      }
    });
  };

  const toggleScreenSelection = (screenId: string) => {
    setSelectedScreens(prev =>
      prev.includes(screenId)
        ? prev.filter(id => id !== screenId)
        : [...prev, screenId]
    );
  };

  const toggleMediaSelection = (mediaId: string) => {
    setSelectedMedia(prev =>
      prev.includes(mediaId)
        ? prev.filter(id => id !== mediaId)
        : [...prev, mediaId]
    );
  };

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Campaigns</h2>
          <p className="text-muted-foreground">Schedule and manage your digital signage content</p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button onClick={() => navigate("/campaigns/new")}>
            <PlusCircle className="mr-2 h-4 w-4" /> Create Campaign
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Search campaigns..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            <div className="flex items-center gap-2">

              <div className="flex border border-input rounded-md overflow-hidden">
                <Button
                  variant={statusFilter === "all" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("all")}
                  className="px-3 h-9"
                >
                  All
                </Button>
                <Button
                  variant={statusFilter === "active" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("active")}
                  className="px-3 h-9"
                >
                  <Check className="h-3.5 w-3.5 mr-2 text-green-500" />
                  Active
                </Button>
                <Button
                  variant={statusFilter === "scheduled" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("scheduled")}
                  className="px-3 h-9"
                >
                  <Calendar className="h-3.5 w-3.5 mr-2 text-amber-500" />
                  Scheduled
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns Content */}
      {isLoading ? (
        <div className="py-20 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading campaigns...</p>
        </div>
      ) : isEmpty ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <Presentation className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No campaigns yet</h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Create your first campaign to schedule content on your displays.
          </p>
          <Button onClick={() => navigate("/campaigns/new")}>
            <PlusCircle className="mr-2 h-4 w-4" /> Create Campaign
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredCampaigns?.map(campaign => {
            // Use the counts directly from the campaign object if available,
            // otherwise fall back to the calculated counts from separate queries
            const screenCount = campaign.screenCount !== undefined
              ? campaign.screenCount
              : allCampaignScreens?.filter(cs => cs.campaignId === campaign.id).length || 0;

            const mediaCount = campaign.mediaCount !== undefined
              ? campaign.mediaCount
              : allCampaignMedia?.filter(cm => cm.campaignId === campaign.id && cm.campaignType === 0).length || 0;

            const slideCount = campaign.slideCount !== undefined
              ? campaign.slideCount
              : allCampaignMedia?.filter(cm => cm.campaignId === campaign.id && cm.campaignType === 1).length || 0;

            return (
              <CampaignCard
                key={campaign.id}
                campaign={campaign}
                mediaCount={mediaCount}
                screenCount={screenCount}
                slideCount={slideCount}
                onEdit={() => handleEditClick(campaign.id)}
                onDelete={() => handleDeleteCampaign(campaign.id)}
                onDuplicate={() => handleDuplicateCampaign(campaign.id)}
                onToggleActive={() => handleToggleActive(campaign.id, campaign.status)}
              />
            );
          })}
        </div>
      )}

      {/* Add Campaign Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-5xl w-[95%]">
          <DialogHeader>
            <DialogTitle>Add Campaign</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddCampaign)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Campaign Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarRange className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="flex flex-col">
                          <FormLabel>End Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`w-full pl-3 text-left font-normal ${!field.value && "text-muted-foreground"}`}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarRange className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="scheduled">Scheduled</SelectItem>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="paused">Paused</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-4">
                  <div>
                    <FormLabel>Screens</FormLabel>
                    <FormDescription>Select screens to display this campaign</FormDescription>

                    <div className="mt-2">
                      <Popover open={screenSearchOpen} onOpenChange={(open) => {
                        setScreenSearchOpen(open);
                        if (!open) {
                          setScreenSearchQuery("");
                          setSelectedScreenTag("");
                        }
                      }}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={screenSearchOpen}
                            className="w-full justify-between"
                          >
                            {selectedScreens.length === 0
                              ? "Select screens..."
                              : `${selectedScreens.length} screen${selectedScreens.length > 1 ? 's' : ''} selected`}
                            <Monitor className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" align="start">
                          <Command>
                            <input
                              type="text"
                              placeholder="Search by name or location..."
                              value={screenSearchQuery}
                              onChange={(e) => setScreenSearchQuery(e.target.value)}
                              className="w-full px-3 py-2 border-none focus:outline-none focus:ring-0"
                            />
                            <div className="border-t border-border px-2 py-2">
                              {/* Screen status filter */}
                              <div className="mb-2">
                                <p className="text-xs text-muted-foreground mb-1">Status</p>
                                <div className="flex flex-wrap gap-1 mb-1">
                                  <Button
                                    variant={selectedScreenStatusTag === "" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedScreenStatusTag("")}
                                    className="h-7 text-xs"
                                  >
                                    All
                                  </Button>
                                  <Button
                                    variant={selectedScreenStatusTag === "online" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedScreenStatusTag("online")}
                                    className="h-7 text-xs"
                                  >
                                    Online
                                  </Button>
                                  <Button
                                    variant={selectedScreenStatusTag === "offline" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedScreenStatusTag("offline")}
                                    className="h-7 text-xs"
                                  >
                                    Offline
                                  </Button>
                                </div>
                              </div>

                              {/* Screen tag filter */}
                              {screenTags.length > 0 && (
                                <div className="mb-2">
                                  <p className="text-xs text-muted-foreground mb-1">Tags</p>
                                  <div className="flex flex-wrap gap-1 mb-1">
                                    <Button
                                      variant={selectedScreenTag === "" ? "secondary" : "outline"}
                                      size="sm"
                                      onClick={() => setSelectedScreenTag("")}
                                      className="h-7 text-xs"
                                    >
                                      All Tags
                                    </Button>
                                    {screenTags.map(tag => (
                                      <Button
                                        key={tag.id}
                                        variant={selectedScreenTag === tag.id ? "secondary" : "outline"}
                                        size="sm"
                                        onClick={() => setSelectedScreenTag(tag.id)}
                                        className="h-7 text-xs"
                                      >
                                        {tag.name}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <div className="text-xs text-muted-foreground">
                                {screens?.length || 0} screens total • {selectedScreens.length} selected
                              </div>
                            </div>
                            <CommandList className="max-h-[200px] overflow-auto">
                              <CommandEmpty>No screens found matching your criteria.</CommandEmpty>
                              <CommandGroup>
                                {screens
                                  ?.filter(screen => {
                                    // Filter by search query (name or location)
                                    const matchesSearch = !screenSearchQuery ||
                                      screen.name.toLowerCase().includes(screenSearchQuery.toLowerCase()) ||
                                      (screen.location && screen.location.toLowerCase().includes(screenSearchQuery.toLowerCase()));

                                    // Filter by status tag
                                    const matchesStatusTag = !selectedScreenStatusTag || screen.status === selectedScreenStatusTag;

                                    // Filter by screen tag
                                    const matchesScreenTag = !selectedScreenTag ||
                                      (screenTags.find(tag => tag.id === selectedScreenTag)?.screens || []).includes(screen.id);

                                    return matchesSearch && matchesStatusTag && matchesScreenTag;
                                  })
                                  .map(screen => (
                                    <CommandItem
                                      key={screen.id}
                                      onSelect={() => {
                                        toggleScreenSelection(screen.id);
                                      }}
                                    >
                                      <div className="flex items-center gap-2 w-full">
                                        <Checkbox checked={selectedScreens.includes(screen.id)} />
                                        <div className="mr-2 h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                                          <Monitor className="h-4 w-4 text-foreground" />
                                        </div>
                                        <div className="flex-1">
                                          <p className="text-sm">{screen.name}</p>
                                          {screen.location && (
                                            <p className="text-xs text-muted-foreground">{screen.location}</p>
                                          )}
                                        </div>
                                        <Badge variant="outline" className="ml-auto">
                                          {screen.status}
                                        </Badge>
                                      </div>
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>

                      {selectedScreens.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {selectedScreens.map(screenId => {
                            const screen = screens?.find(s => s.id === screenId);
                            return screen ? (
                              <Badge key={screenId} variant="outline" className="flex items-center gap-1">
                                {screen.name}
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                                  onClick={() => toggleScreenSelection(screenId)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <FormLabel>Media</FormLabel>
                    <FormDescription>Select media to display in this campaign</FormDescription>

                    <div className="mt-2">
                      <Popover open={mediaSearchOpen} onOpenChange={(open) => {
                        setMediaSearchOpen(open);
                        if (!open) {
                          setMediaSearchQuery("");
                          setSelectedMediaTag("");
                        }
                      }}>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            role="combobox"
                            aria-expanded={mediaSearchOpen}
                            className="w-full justify-between"
                          >
                            {selectedMedia.length === 0
                              ? "Select media..."
                              : `${selectedMedia.length} item${selectedMedia.length > 1 ? 's' : ''} selected`}
                            <ImageIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0" align="start">
                          <Command>
                            <input
                              type="text"
                              placeholder="Search media..."
                              value={mediaSearchQuery}
                              onChange={(e) => setMediaSearchQuery(e.target.value)}
                              className="w-full px-3 py-2 border-none focus:outline-none focus:ring-0"
                            />
                            <div className="border-t border-border px-2 py-2">
                              {/* Media type filter */}
                              <div className="mb-2">
                                <p className="text-xs text-muted-foreground mb-1">Type</p>
                                <div className="flex flex-wrap gap-1 mb-1">
                                  <Button
                                    variant={selectedMediaTag === "" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedMediaTag("")}
                                    className="h-7 text-xs"
                                  >
                                    All
                                  </Button>
                                  <Button
                                    variant={selectedMediaTag === "image" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedMediaTag("image")}
                                    className="h-7 text-xs"
                                  >
                                    Images
                                  </Button>
                                  <Button
                                    variant={selectedMediaTag === "video" ? "secondary" : "outline"}
                                    size="sm"
                                    onClick={() => setSelectedMediaTag("video")}
                                    className="h-7 text-xs"
                                  >
                                    Videos
                                  </Button>
                                </div>
                              </div>

                              {/* Media tag filter */}
                              {mediaTags.length > 0 && (
                                <div className="mb-2">
                                  <p className="text-xs text-muted-foreground mb-1">Tags</p>
                                  <div className="flex flex-wrap gap-1 mb-1">
                                    <Button
                                      variant={selectedMediaTag === "" ? "secondary" : "outline"}
                                      size="sm"
                                      onClick={() => setSelectedMediaTag("")}
                                      className="h-7 text-xs"
                                    >
                                      All Tags
                                    </Button>
                                    {mediaTags.map(tag => (
                                      <Button
                                        key={tag.id}
                                        variant={selectedMediaTag === tag.id ? "secondary" : "outline"}
                                        size="sm"
                                        onClick={() => setSelectedMediaTag(tag.id)}
                                        className="h-7 text-xs"
                                      >
                                        {tag.name}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <div className="text-xs text-muted-foreground">
                                {mediaItems?.length || 0} items total • {selectedMedia.length} selected
                              </div>
                            </div>
                            <CommandList className="max-h-[200px] overflow-auto">
                              <CommandEmpty>No media found matching your criteria.</CommandEmpty>
                              <CommandGroup>
                                {mediaItems
                                  ?.filter(media => {
                                    // Filter by search query
                                    const matchesSearch = !mediaSearchQuery ||
                                      media.name.toLowerCase().includes(mediaSearchQuery.toLowerCase());

                                    // Filter by type
                                    const matchesType = !selectedMediaTag ||
                                      selectedMediaTag === "image" && media.fileType.startsWith("image/") ||
                                      selectedMediaTag === "video" && media.fileType.startsWith("video/") ||
                                      (mediaTags.find(tag => tag.id === selectedMediaTag)?.media || []).includes(media.id);

                                    return matchesSearch && matchesType;
                                  })
                                  .map(media => (
                                    <CommandItem
                                      key={media.id}
                                      onSelect={() => {
                                        toggleMediaSelection(media.id);
                                      }}
                                    >
                                      <div className="flex items-center gap-2 w-full">
                                        <Checkbox checked={selectedMedia.includes(media.id)} />
                                        <div className="mr-2 h-8 w-8 overflow-hidden rounded border border-border flex-shrink-0">
                                          <img
                                            src={media.thumbnailUrl || media.fileUrl}
                                            alt={media.name}
                                            className="h-full w-full object-cover"
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <p className="text-sm">{media.name}</p>
                                          <p className="text-xs text-muted-foreground">
                                            {media.fileType.split("/")[0]}
                                          </p>
                                        </div>
                                      </div>
                                    </CommandItem>
                                  ))}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>

                      {selectedMedia.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {selectedMedia.map(mediaId => {
                            const media = mediaItems?.find(m => m.id === mediaId);
                            return media ? (
                              <Badge key={mediaId} variant="outline" className="flex items-center gap-1">
                                {media.name}
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-4 w-4 p-0 text-muted-foreground hover:text-foreground"
                                  onClick={() => toggleMediaSelection(mediaId)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Create Campaign</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Campaign Modal */}
      <CampaignEditModal
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        campaign={selectedCampaign}
        teamId={teamId}
        screens={screens || []}
        mediaItems={mediaItems || []}
        slides={slides || []}
        selectedScreens={selectedScreens}
        setSelectedScreens={setSelectedScreens}
        selectedMedia={selectedMedia}
        setSelectedMedia={setSelectedMedia}
        screenTags={screenTags}
        mediaTags={mediaTags}
        onSave={handleEditCampaign}
      />
    </MainLayout>
  );
}