import React from 'react';
import { useSafeNavigation } from '@/hooks/use-safe-navigation';
import { cn } from '@/lib/utils';

interface SafeRouterLinkProps {
  to: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * A safe Link component that prevents Next.js router invalid href errors in Replit
 * Uses our custom useSafeNavigation hook to handle routing properly
 */
const SafeRouterLink: React.FC<SafeRouterLinkProps> = ({ 
  to, 
  children, 
  className,
  onClick 
}) => {
  const { navigateTo, createSafeUrl } = useSafeNavigation();
  
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    
    if (onClick) {
      onClick();
    }
    
    navigateTo(to);
  };
  
  return (
    <a 
      href={createSafeUrl(to)}
      className={cn(className)}
      onClick={handleClick}
    >
      {children}
    </a>
  );
};

export default SafeRouterLink;