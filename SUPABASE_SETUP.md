# Supabase Setup Guide

## Overview
This application requires Supabase credentials to function properly. Follow this guide to set up your Supabase project and configure the environment variables.

## Step 1: Create a Supabase Project

1. Go to [Supabase](https://app.supabase.com/)
2. Sign up or log in to your account
3. Click "New Project"
4. Choose your organization
5. Fill in your project details:
   - **Name**: Choose a name for your project (e.g., "Digital Signage Manager")
   - **Database Password**: Create a strong password
   - **Region**: Choose the region closest to your users
6. Click "Create new project"
7. Wait for the project to be set up (this may take a few minutes)

## Step 2: Get Your Supabase Credentials

1. Once your project is ready, go to your project dashboard
2. Navigate to **Settings** → **API**
3. You'll find two important values:
   - **Project URL**: This is your `VITE_SUPABASE_URL`
   - **anon public key**: This is your `VITE_SUPABASE_ANON_KEY`

## Step 3: Configure Environment Variables

1. In your project root directory, copy the `.env.example` file to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and replace the placeholder values:
   ```env
   # Your actual Supabase project URL
   VITE_SUPABASE_URL=https://your-actual-project-id.supabase.co
   
   # Your actual Supabase anon/public key
   VITE_SUPABASE_ANON_KEY=your-actual-anon-key-here
   ```

## Step 4: Set Up Database Tables

1. Run the database setup script:
   ```bash
   node setup_database.js
   ```

   Or manually execute the SQL from `supabase_setup.sql` in your Supabase SQL editor.

## Step 5: Test the Setup

1. Start the application:
   ```bash
   npm start
   ```

2. If everything is configured correctly, you should see:
   ```
   serving on port 5000
   ```

3. Open your browser and go to `http://localhost:5000`

## Troubleshooting

### Error: "Missing Supabase credentials"
- Make sure your `.env` file exists in the project root
- Verify that the environment variables are set correctly
- Ensure there are no extra spaces or quotes around the values

### Database Connection Issues
- Check that your Supabase project is active
- Verify your database password is correct
- Make sure the database tables have been created

### API Errors
- Verify your anon key is correct
- Check that Row Level Security (RLS) policies are properly configured
- Ensure your Supabase project is not paused

## Security Notes

- Never commit your `.env` file to version control
- The `.env` file is already included in `.gitignore`
- Use environment variables in production deployments
- Regularly rotate your Supabase keys if needed

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Environment Variables Best Practices](https://supabase.com/docs/guides/getting-started/environment-variables)
