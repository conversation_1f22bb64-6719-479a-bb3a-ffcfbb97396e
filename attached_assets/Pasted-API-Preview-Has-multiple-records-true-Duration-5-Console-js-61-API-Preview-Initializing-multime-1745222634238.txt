API Preview: Has multiple records: true Duration: 5
Console.js:61 API Preview: Initializing multimedia elements with API data
Console.js:61 Element 36f41c4c-ab91-4132-9262-bb2bf85107cc has API mapping: undefined undefined
Console.js:61 API Preview: Found 0 elements with API mappings
Console.js:61 API Preview: Data is an array with 23 items
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=0) for element 36f41c4c-ab91-4132-9262-bb2bf85107cc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 API data preview setup - Has multiple records: true Duration: 5
Console.js:61 Element 36f41c4c-ab91-4132-9262-bb2bf85107cc has API mapping: undefined undefined
Console.js:61 No API elements found on canvas
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=0) for element 36f41c4c-ab91-4132-9262-bb2bf85107cc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=0) for element 36f41c4c-ab91-4132-9262-bb2bf85107cc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=0) for element 36f41c4c-ab91-4132-9262-bb2bf85107cc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii