-- Migration to add enhanced screen functionality
-- Note: screens, screen_activities and screen_registrations tables already exist with the correct structure
-- This migration only adds indexes and helper functions for better performance

-- Create indexes for better performance on existing tables
CREATE INDEX IF NOT EXISTS idx_screen_activities_screen_id ON public.screen_activities(screen_id);
CREATE INDEX IF NOT EXISTS idx_screen_activities_log_datetime ON public.screen_activities(log_datetime);
CREATE INDEX IF NOT EXISTS idx_screen_registrations_screen_id ON public.screen_registrations(screen_id);
CREATE INDEX IF NOT EXISTS idx_screen_registrations_subscription_status ON public.screen_registrations(subscription_status);

-- Create a function to get enhanced screens with tags using LEFT JOINs
CREATE OR REPLACE FUNCTION get_enhanced_screens_with_tags(team_id_param UUID)
RETURNS TABLE(
  id UUID,
  team_id UUID,
  name TEXT,
  location TEXT,
  code TEXT,
  status TEXT,
  last_ping_at TIMESTAMP WITH TIME ZONE,
  start_time TIME,
  end_time TIME,
  update_frequency TEXT,
  site_email TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_registered BOOLEAN,
  updateguid UUID,
  health JSON,
  is_deleted BOOLEAN,
  tags TEXT,
  max_file_download_count INTEGER,
  max_total_file_download INTEGER,
  trial_ends_at TIMESTAMP WITH TIME ZONE,
  subscription_status TEXT,
  lemonsqueezy_subscription_id TEXT,
  billing_cycle TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id,
    s.team_id,
    s.name,
    s.location,
    s.code,
    s.status,
    s.last_ping_at,
    s.start_time,
    s.end_time,
    s.update_frequency,
    s.site_email,
    s.created_at,
    s.updated_at,
    s.is_registered,
    s.updateguid,
    s.health,
    s.is_deleted,
    agg.tags,
    agg.max_file_download_count,
    agg.max_total_file_download,
    agg.trial_ends_at,
    agg.subscription_status,
    agg.lemonsqueezy_subscription_id,
    agg.billing_cycle
  FROM screens s
  LEFT JOIN (
    SELECT
      s2.id as screen_id,
      STRING_AGG(DISTINCT t.name, ',' ORDER BY t.name) as tags,
      COALESCE(MAX(sa.file_download_count), 0)::INTEGER as max_file_download_count,
      COALESCE(MAX(sa.total_file_download), 0)::INTEGER as max_total_file_download,
      sr.trial_ends_at,
      COALESCE(sr.subscription_status, 'trial') as subscription_status,
      sr.lemonsqueezy_subscription_id,
      sr.billing_cycle
    FROM screens s2
    LEFT JOIN screen_tags st ON s2.id = st.screen_id
    LEFT JOIN tags t ON st.tag_id = t.id
    LEFT JOIN screen_activities sa ON s2.id = sa.screen_id
    LEFT JOIN screen_registrations sr ON s2.id = sr.screen_id
    WHERE s2.team_id = team_id_param
    GROUP BY s2.id, sr.trial_ends_at, sr.subscription_status, sr.lemonsqueezy_subscription_id, sr.billing_cycle
  ) agg ON s.id = agg.screen_id
  WHERE s.team_id = team_id_param
  ORDER BY s.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get enhanced single screen using LEFT JOINs
CREATE OR REPLACE FUNCTION get_enhanced_screen_by_id(screen_id_param UUID)
RETURNS TABLE(
  id UUID,
  team_id UUID,
  name TEXT,
  location TEXT,
  code TEXT,
  status TEXT,
  last_ping_at TIMESTAMP WITH TIME ZONE,
  start_time TIME,
  end_time TIME,
  update_frequency TEXT,
  site_email TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  is_registered BOOLEAN,
  updateguid UUID,
  health JSON,
  is_deleted BOOLEAN,
  max_file_download_count INTEGER,
  max_total_file_download INTEGER,
  trial_ends_at TIMESTAMP WITH TIME ZONE,
  subscription_status TEXT,
  lemonsqueezy_subscription_id TEXT,
  billing_cycle TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    s.id,
    s.team_id,
    s.name,
    s.location,
    s.code,
    s.status,
    s.last_ping_at,
    s.start_time,
    s.end_time,
    s.update_frequency,
    s.site_email,
    s.created_at,
    s.updated_at,
    s.is_registered,
    s.updateguid,
    s.health,
    s.is_deleted,
    agg.max_file_download_count,
    agg.max_total_file_download,
    agg.trial_ends_at,
    agg.subscription_status,
    agg.lemonsqueezy_subscription_id,
    agg.billing_cycle
  FROM screens s
  LEFT JOIN (
    SELECT
      s2.id as screen_id,
      COALESCE(MAX(sa.file_download_count), 0)::INTEGER as max_file_download_count,
      COALESCE(MAX(sa.total_file_download), 0)::INTEGER as max_total_file_download,
      sr.trial_ends_at,
      COALESCE(sr.subscription_status, 'trial') as subscription_status,
      sr.lemonsqueezy_subscription_id,
      sr.billing_cycle
    FROM screens s2
    LEFT JOIN screen_activities sa ON s2.id = sa.screen_id
    LEFT JOIN screen_registrations sr ON s2.id = sr.screen_id
    WHERE s2.id = screen_id_param
    GROUP BY s2.id, sr.trial_ends_at, sr.subscription_status, sr.lemonsqueezy_subscription_id, sr.billing_cycle
  ) agg ON s.id = agg.screen_id
  WHERE s.id = screen_id_param;
END;
$$ LANGUAGE plpgsql;

-- Insert sample data for testing (optional)
-- You can uncomment these lines to add test data

-- Add sample activity data
-- INSERT INTO public.screen_activities (screen_id, log_details, log_datetime, file_download_count, total_file_download)
-- SELECT id,
--        'Sample activity log' as log_details,
--        NOW() as log_datetime,
--        FLOOR(RANDOM() * 100)::INTEGER as file_download_count,
--        FLOOR(RANDOM() * 200)::INTEGER as total_file_download
-- FROM public.screens
-- WHERE NOT EXISTS (SELECT 1 FROM public.screen_activities WHERE screen_id = screens.id);

-- Add sample registration data
-- INSERT INTO public.screen_registrations (registered_at, screen_id, trial_ends_at, subscription_status)
-- SELECT NOW() as registered_at,
--        id as screen_id,
--        NOW() + INTERVAL '30 days' as trial_ends_at,
--        'trial' as subscription_status
-- FROM public.screens
-- WHERE NOT EXISTS (SELECT 1 FROM public.screen_registrations WHERE screen_id = screens.id);
