eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: '<PERSON><PERSON>', username: '<PERSON><PERSON>', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: '<PERSON><PERSON>', username: '<PERSON><PERSON>', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Warning: Failed prop type: Invalid prop `labelFontSize` of type `number` supplied to `ReactSpeedometer`, expected `string`.
    at Vu (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/react-d3-speedometer.js?v=926f0d8e:3160:5)
    at Gauge (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745646641179:19:3)
    at div
    at PresenceChild (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7742:24)
    at AnimatePresence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7800:26)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at Canvas (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/canvas.tsx?t=1745646641179:33:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at div
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745646641179:68:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745646641179&v=jYV7p713V_-OGbfo-mbwg:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=926f0d8e:2805:3)
Mt.forEach.n.<computed> @ VM6012 eruda.js:2
printWarning @ VM6034 react_jsx-dev-runtime.js:64
error @ VM6034 react_jsx-dev-runtime.js:48
checkPropTypes @ VM6034 react_jsx-dev-runtime.js:451
validatePropTypes @ VM6034 react_jsx-dev-runtime.js:778
jsxWithValidation @ VM6034 react_jsx-dev-runtime.js:876
Gauge @ VM6161 gauge-speedometer.tsx:78
renderWithHooks @ VM6037 chunk-276SZO74.js:11548
mountIndeterminateComponent @ VM6037 chunk-276SZO74.js:14926
beginWork @ VM6037 chunk-276SZO74.js:15914
beginWork$1 @ VM6037 chunk-276SZO74.js:19753
performUnitOfWork @ VM6037 chunk-276SZO74.js:19198
workLoopSync @ VM6037 chunk-276SZO74.js:19137
renderRootSync @ VM6037 chunk-276SZO74.js:19116
performSyncWorkOnRoot @ VM6037 chunk-276SZO74.js:18874
flushSyncCallbacks @ VM6037 chunk-276SZO74.js:9119
(anonymous) @ VM6037 chunk-276SZO74.js:18627
eruda.js:2 Warning: Failed prop type: Invalid prop `valueTextFontSize` of type `number` supplied to `ReactSpeedometer`, expected `string`.
    at Vu (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/react-d3-speedometer.js?v=926f0d8e:3160:5)
    at Gauge (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745646641179:19:3)
    at div
    at PresenceChild (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7742:24)
    at AnimatePresence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7800:26)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at Canvas (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/canvas.tsx?t=1745646641179:33:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at div
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745646641179:68:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745646641179&v=jYV7p713V_-OGbfo-mbwg:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=926f0d8e:2805:3)
Mt.forEach.n.<computed> @ VM6012 eruda.js:2
printWarning @ VM6034 react_jsx-dev-runtime.js:64
error @ VM6034 react_jsx-dev-runtime.js:48
checkPropTypes @ VM6034 react_jsx-dev-runtime.js:451
validatePropTypes @ VM6034 react_jsx-dev-runtime.js:778
jsxWithValidation @ VM6034 react_jsx-dev-runtime.js:876
Gauge @ VM6161 gauge-speedometer.tsx:78
renderWithHooks @ VM6037 chunk-276SZO74.js:11548
mountIndeterminateComponent @ VM6037 chunk-276SZO74.js:14926
beginWork @ VM6037 chunk-276SZO74.js:15914
beginWork$1 @ VM6037 chunk-276SZO74.js:19753
performUnitOfWork @ VM6037 chunk-276SZO74.js:19198
workLoopSync @ VM6037 chunk-276SZO74.js:19137
renderRootSync @ VM6037 chunk-276SZO74.js:19116
performSyncWorkOnRoot @ VM6037 chunk-276SZO74.js:18874
flushSyncCallbacks @ VM6037 chunk-276SZO74.js:9119
(anonymous) @ VM6037 chunk-276SZO74.js:18627
chunk-276SZO74.js:16670 Uncaught Error: First value should be equivalent to min value given. Current min value - 0
    at Ri (react-d3-speedometer.js:2061:23)
    at react-d3-speedometer.js:3090:10
    at r2 (react-d3-speedometer.js:3058:18)
    at react-d3-speedometer.js:3121:16
    at Ru (react-d3-speedometer.js:3123:4)
    at Vu.renderGauge (react-d3-speedometer.js:3172:193)
    at Vu.componentDidMount (react-d3-speedometer.js:3163:10)
    at commitLayoutEffectOnFiber (chunk-276SZO74.js:17030:36)
    at commitLayoutMountEffects_complete (chunk-276SZO74.js:17980:17)
    at commitLayoutEffects_begin (chunk-276SZO74.js:17969:15)
Ri @ VM6162 react-d3-speedometer.js:2061
(anonymous) @ VM6162 react-d3-speedometer.js:3090
r2 @ VM6162 react-d3-speedometer.js:3058
(anonymous) @ VM6162 react-d3-speedometer.js:3121
Ru @ VM6162 react-d3-speedometer.js:3123
renderGauge @ VM6162 react-d3-speedometer.js:3172
componentDidMount @ VM6162 react-d3-speedometer.js:3163
commitLayoutEffectOnFiber @ VM6037 chunk-276SZO74.js:17030
commitLayoutMountEffects_complete @ VM6037 chunk-276SZO74.js:17980
commitLayoutEffects_begin @ VM6037 chunk-276SZO74.js:17969
commitLayoutEffects @ VM6037 chunk-276SZO74.js:17920
commitRootImpl @ VM6037 chunk-276SZO74.js:19353
commitRoot @ VM6037 chunk-276SZO74.js:19277
performSyncWorkOnRoot @ VM6037 chunk-276SZO74.js:18895
flushSyncCallbacks @ VM6037 chunk-276SZO74.js:9119
(anonymous) @ VM6037 chunk-276SZO74.js:18627
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: address.geo.lat
eruda.js:2 Updating valueLabel with API data: -37.3159
eruda.js:2 The above error occurred in the <ReactSpeedometer> component:

    at Vu (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/react-d3-speedometer.js?v=926f0d8e:3160:5)
    at div
    at Gauge (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745646641179:19:3)
    at div
    at PresenceChild (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7742:24)
    at AnimatePresence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=926f0d8e:7800:26)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at Canvas (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/canvas.tsx?t=1745646641179:33:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at div
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745646641179:68:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745646641179&v=jYV7p713V_-OGbfo-mbwg:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=926f0d8e:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=926f0d8e:2805:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
Mt.forEach.n.<computed> @ VM6012 eruda.js:2
logCapturedError @ VM6037 chunk-276SZO74.js:14032
callback @ VM6037 chunk-276SZO74.js:14078
callCallback @ VM6037 chunk-276SZO74.js:11248
commitUpdateQueue @ VM6037 chunk-276SZO74.js:11265
commitLayoutEffectOnFiber @ VM6037 chunk-276SZO74.js:17075
commitLayoutMountEffects_complete @ VM6037 chunk-276SZO74.js:17980
commitLayoutEffects_begin @ VM6037 chunk-276SZO74.js:17969
commitLayoutEffects @ VM6037 chunk-276SZO74.js:17920
commitRootImpl @ VM6037 chunk-276SZO74.js:19353
commitRoot @ VM6037 chunk-276SZO74.js:19277
performSyncWorkOnRoot @ VM6037 chunk-276SZO74.js:18895
flushSyncCallbacks @ VM6037 chunk-276SZO74.js:9119
(anonymous) @ VM6037 chunk-276SZO74.js:18627
eruda.js:2 Error caught by ErrorBoundary: Error: First value should be equivalent to min value given. Current min value - 0
    at Ri (react-d3-speedometer.js:2061:23)
    at react-d3-speedometer.js:3090:10
    at r2 (react-d3-speedometer.js:3058:18)
    at react-d3-speedometer.js:3121:16
    at Ru (react-d3-speedometer.js:3123:4)
    at Vu.renderGauge (react-d3-speedometer.js:3172:193)
    at Vu.componentDidMount (react-d3-speedometer.js:3163:10)
    at commitLayoutEffectOnFiber (chunk-276SZO74.js:17030:36)
    at commitLayoutMountEffects_complete (chunk-276SZO74.js:17980:17)
    at commitLayoutEffects_begin (chunk-276SZO74.js:17969:15) {componentStack: '\n    at Vu (https://29810b14-7e88-452d-9d46-13ec03…/deps/@tanstack_react-query.js?v=926f0d8e:2805:3)'}
Mt.forEach.n.<computed> @ VM6012 eruda.js:2
componentDidCatch @ VM6142 error-boundary.tsx:35
callback @ VM6037 chunk-276SZO74.js:14084
callCallback @ VM6037 chunk-276SZO74.js:11248
commitUpdateQueue @ VM6037 chunk-276SZO74.js:11265
commitLayoutEffectOnFiber @ VM6037 chunk-276SZO74.js:17075
commitLayoutMountEffects_complete @ VM6037 chunk-276SZO74.js:17980
commitLayoutEffects_begin @ VM6037 chunk-276SZO74.js:17969
commitLayoutEffects @ VM6037 chunk-276SZO74.js:17920
commitRootImpl @ VM6037 chunk-276SZO74.js:19353
commitRoot @ VM6037 chunk-276SZO74.js:19277
performSyncWorkOnRoot @ VM6037 chunk-276SZO74.js:18895
flushSyncCallbacks @ VM6037 chunk-276SZO74.js:9119
(anonymous) @ VM6037 chunk-276SZO74.js:18627
eruda.js:2 [vite] server connection lost. Polling for restart...
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ VM6012 eruda.js:2
ping @ VM6011 client:736
waitForSuccessfulPing @ VM6011 client:749
(anonymous) @ VM6011 client:561
29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ 29810b14-7e88-452d-9…da/3.2.3/eruda.js:2
ping @ 29810b14-7e88-452d-9…ev/@vite/client:736
waitForSuccessfulPing @ 29810b14-7e88-452d-9…ev/@vite/client:749
(anonymous) @ 29810b14-7e88-452d-9…ev/@vite/client:561
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ eruda.js:2
ping @ client:736
waitForSuccessfulPing @ client:749
(anonymous) @ client:561
eruda.js:2  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ eruda.js:2
ping @ client:736
waitForSuccessfulPing @ client:749
(anonymous) @ client:561
eruda.js:2  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ eruda.js:2
ping @ client:736
waitForSuccessfulPing @ client:749
(anonymous) @ client:561
eruda.js:2  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ eruda.js:2
ping @ client:736
waitForSuccessfulPing @ client:749
(anonymous) @ client:561
29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@vite/client:562 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ VM6011 client:562
29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@vite/client:562 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ 29810b14-7e88-452d-9…ev/@vite/client:562
client:562 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ client:562
client:562  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ client:562
client:562  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ client:562
client:562  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ client:562
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=a33d4566:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=a33d4566:6975
createClient @ chunk-K2RJJBRU.js?v=a33d4566:7142
createBrowserClient @ @supabase_ssr.js?v=06edf828:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=a33d4566:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=a33d4566:14926
beginWork @ chunk-276SZO74.js?v=a33d4566:15914
beginWork$1 @ chunk-276SZO74.js?v=a33d4566:19753
performUnitOfWork @ chunk-276SZO74.js?v=a33d4566:19198
workLoopSync @ chunk-276SZO74.js?v=a33d4566:19137
renderRootSync @ chunk-276SZO74.js?v=a33d4566:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=a33d4566:18678
workLoop @ chunk-276SZO74.js?v=a33d4566:197
flushWork @ chunk-276SZO74.js?v=a33d4566:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=a33d4566:384
eruda.js:2 Auth state changed: INITIAL_SESSION
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'ambient-light-sensor'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'battery'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-not-rendered'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'execution-while-out-of-viewport'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'layout-animations'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'legacy-image-formats'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'navigation-override'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'oversized-images'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'publickey-credentials'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'speaker-selection'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unoptimized-images'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
Unrecognized feature: 'unsized-media'.
replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1 Unrecognized feature: 'pointer-lock'.
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
(anonymous) @ main-7194c1fb997646ca.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
navigateTo @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
refresh @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
refresh @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
refresh @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
(anonymous) @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
next @ replEnvironmentDesktop-0f6f17d6ebaf7a4c.js:1
next @ _app-d177f7756b7d8d4c.js:4
next @ _app-d177f7756b7d8d4c.js:34
next @ _app-d177f7756b7d8d4c.js:34
n.observers.next @ _app-d177f7756b7d8d4c.js:34
b @ _app-d177f7756b7d8d4c.js:4
y @ _app-d177f7756b7d8d4c.js:4
t.next @ _app-d177f7756b7d8d4c.js:4
next @ _app-d177f7756b7d8d4c.js:34
next @ _app-d177f7756b7d8d4c.js:34
next @ _app-d177f7756b7d8d4c.js:34
n.observers.next @ _app-d177f7756b7d8d4c.js:34
b @ _app-d177f7756b7d8d4c.js:4
y @ _app-d177f7756b7d8d4c.js:4
t.next @ _app-d177f7756b7d8d4c.js:4
s @ _app-d177f7756b7d8d4c.js:40
(anonymous) @ _app-d177f7756b7d8d4c.js:40
next @ _app-d177f7756b7d8d4c.js:34
next @ _app-d177f7756b7d8d4c.js:34
n.observers.next @ _app-d177f7756b7d8d4c.js:34
b @ _app-d177f7756b7d8d4c.js:4
y @ _app-d177f7756b7d8d4c.js:4
t.next @ _app-d177f7756b7d8d4c.js:4
notify @ _app-d177f7756b7d8d4c.js:40
t.apply @ _app-d177f7756b7d8d4c.js:40
(anonymous) @ _app-d177f7756b7d8d4c.js:40
next @ _app-d177f7756b7d8d4c.js:34
next @ _app-d177f7756b7d8d4c.js:34
b @ _app-d177f7756b7d8d4c.js:4
y @ _app-d177f7756b7d8d4c.js:4
t.next @ _app-d177f7756b7d8d4c.js:4
b @ _app-d177f7756b7d8d4c.js:4
y @ _app-d177f7756b7d8d4c.js:4
t.next @ _app-d177f7756b7d8d4c.js:4
(anonymous) @ _app-d177f7756b7d8d4c.js:40
(anonymous) @ main-7194c1fb997646ca.js:1
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Promise.then
n @ main-7194c1fb997646ca.js:1
u @ main-7194c1fb997646ca.js:1
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowfullscreen'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Allow attribute will take precedence over 'allowpaymentrequest'.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=a33d4566:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=a33d4566:6975
createClient @ chunk-K2RJJBRU.js?v=a33d4566:7142
createBrowserClient @ @supabase_ssr.js?v=06edf828:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=a33d4566:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=a33d4566:14926
beginWork @ chunk-276SZO74.js?v=a33d4566:15914
beginWork$1 @ chunk-276SZO74.js?v=a33d4566:19753
performUnitOfWork @ chunk-276SZO74.js?v=a33d4566:19198
workLoopSync @ chunk-276SZO74.js?v=a33d4566:19137
renderRootSync @ chunk-276SZO74.js?v=a33d4566:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=a33d4566:18678
workLoop @ chunk-276SZO74.js?v=a33d4566:197
flushWork @ chunk-276SZO74.js?v=a33d4566:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=a33d4566:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: address.geo.lat
eruda.js:2 Updating valueLabel with API data: -37.3159
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=a33d4566:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=a33d4566:6975
createClient @ chunk-K2RJJBRU.js?v=a33d4566:7142
createBrowserClient @ @supabase_ssr.js?v=06edf828:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=a33d4566:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=a33d4566:14926
beginWork @ chunk-276SZO74.js?v=a33d4566:15914
beginWork$1 @ chunk-276SZO74.js?v=a33d4566:19753
performUnitOfWork @ chunk-276SZO74.js?v=a33d4566:19198
workLoopSync @ chunk-276SZO74.js?v=a33d4566:19137
renderRootSync @ chunk-276SZO74.js?v=a33d4566:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=a33d4566:18678
workLoop @ chunk-276SZO74.js?v=a33d4566:197
flushWork @ chunk-276SZO74.js?v=a33d4566:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=a33d4566:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: address.geo.lat
eruda.js:2 Updating valueLabel with API data: -37.3159
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=a33d4566:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=a33d4566:6975
createClient @ chunk-K2RJJBRU.js?v=a33d4566:7142
createBrowserClient @ @supabase_ssr.js?v=06edf828:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=a33d4566:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=a33d4566:14926
beginWork @ chunk-276SZO74.js?v=a33d4566:15914
beginWork$1 @ chunk-276SZO74.js?v=a33d4566:19753
performUnitOfWork @ chunk-276SZO74.js?v=a33d4566:19198
workLoopSync @ chunk-276SZO74.js?v=a33d4566:19137
renderRootSync @ chunk-276SZO74.js?v=a33d4566:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=a33d4566:18678
workLoop @ chunk-276SZO74.js?v=a33d4566:197
flushWork @ chunk-276SZO74.js?v=a33d4566:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=a33d4566:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: address.geo.lat
eruda.js:2 Updating valueLabel with API data: -37.3159
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=a33d4566:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=a33d4566:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=a33d4566:6975
createClient @ chunk-K2RJJBRU.js?v=a33d4566:7142
createBrowserClient @ @supabase_ssr.js?v=06edf828:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=a33d4566:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=a33d4566:14926
beginWork @ chunk-276SZO74.js?v=a33d4566:15914
beginWork$1 @ chunk-276SZO74.js?v=a33d4566:19753
performUnitOfWork @ chunk-276SZO74.js?v=a33d4566:19198
workLoopSync @ chunk-276SZO74.js?v=a33d4566:19137
renderRootSync @ chunk-276SZO74.js?v=a33d4566:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=a33d4566:18678
workLoop @ chunk-276SZO74.js?v=a33d4566:197
flushWork @ chunk-276SZO74.js?v=a33d4566:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=a33d4566:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: address.geo.lat
eruda.js:2 Updating valueLabel with API data: -37.3159
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 [vite] server connection lost. Polling for restart...
sidebar.tsx:22 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-mobile.tsx net::ERR_NETWORK_IO_SUSPENDED
sidebar.tsx:24 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/assets/app.png?import net::ERR_NETWORK_IO_SUSPENDED
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ net::ERR_NETWORK_IO_SUSPENDED
window.fetch @ VM7003 eruda.js:2
ping @ VM7001 client:736
waitForSuccessfulPing @ VM7001 client:749
(anonymous) @ VM7001 client:561
sidebar.tsx:25 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/assets/Final-adloopr-V2-light.png?import net::ERR_NETWORK_IO_SUSPENDED
eruda.js:2 
            
            
           Uncaught (in promise) TypeError: Failed to fetch
    at window.fetch (eruda.js:2:218642)
    at ping (client:736:13)
    at waitForSuccessfulPing (client:749:13)
    at WebSocket.<anonymous> (client:561:13)
window.fetch @ VM7003 eruda.js:2
ping @ VM7001 client:736
waitForSuccessfulPing @ VM7001 client:749
(anonymous) @ VM7001 client:561
sidebar.tsx:41 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/sheet.tsx net::ERR_NETWORK_IO_SUSPENDED
3922-587d8dbd125a45e4.js:1 WebSocket connection to 'wss://eval2.worf.platform.replit.com/wsv2/v2.public.Q2dZSTFhMnh3QVlTQmdqMHliSEFCaUlFZDI5eVpqQUFPdElCQ2lReU9UZ3hNR0l4TkMwM1pUZzRMVFExTW1RdE9XUTBOaTB4TTJWak1ETTFNalF3WXprU0EyNXBlQm9XY21Wd2JHbDBMWEpsY0d3dFptbHNaWE10ZDI5eVppSVZSR2xuYVhSaGJGTnBaMjVoWjJWTllXNWhaMlZ5S2dweVlYaHBkR2R2YUdWc1FrNEtGM0psY0d4cGRDMXlaWEJzTFdKc2IyTnJjeTEzYjNKbUVoVnlaWEJzYVhRdGNtVndiQzF0WlhSaExYZHZjbVlhSEhKbGNHeHBkQzF0WVhKbllYSnBibVV0WW14dlkydHpMWGR2Y21aS0J3aVN3T29URUFGUUFGb1BhVzUwWlc1MFgzQmxjbk52Ym1Gc1VpVUlBUkNBZ0lDQUNCa0FBQUFBQUFEd1B5RUFBQUFBQUFEd1B5aUFnSUNBeUFFd0FUZ0FZQUJxRVFpU3dPb1RFZ3B5WVhocGRHZHZhR1ZzY2hsMWMyVXRiR0YxYm1Ob1pHRnlhMng1TFdScGNtVmpkR3g1Z2dFQ0tnQ0tBU01JQVJDQWdJQ0FJQmtBQUFBQUFBQVFRQ0VBQUFBQUFBQVFRQ2lBZ0lDQXlBRXdBSm9CQ2dvSU1DNHdMakV5TXplZ0FRRT2fFoEUUWHwrjdgzfliJoyAZdqg8tspCw5poKo25cWVhnPCxomz8At9TlLytrFuXeB_VJXQsC71Ez5kabKqNgYP.Q2dad2NtOWtPak1pQ25KbGNHeHBkQzVqYjIwPQ' failed: 
connect @ 3922-587d8dbd125a45e4.js:1
handleClose @ 3922-587d8dbd125a45e4.js:1
t @ 3922-587d8dbd125a45e4.js:1
stallwart.build.js:1 stallwart: failed ping 1
stallwart.build.js:1 stallwart: failed ping 2
stallwart.build.js:1 stallwart: failed ping 1
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 503 (Service Unavailable)
window.fetch @ VM7003 eruda.js:2
ping @ VM7001 client:736
waitForSuccessfulPing @ VM7001 client:755
await in waitForSuccessfulPing
(anonymous) @ VM7001 client:561
29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@vite/client:562 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 503 (Service Unavailable)
(anonymous) @ VM7001 client:562
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ VM7161 eruda.js:2
_GoTrueClient @ VM7275 chunk-K2RJJBRU.js:5040
SupabaseAuthClient @ VM7275 chunk-K2RJJBRU.js:6906
_initSupabaseAuthClient @ VM7275 chunk-K2RJJBRU.js:7102
SupabaseClient @ VM7275 chunk-K2RJJBRU.js:6975
createClient @ VM7275 chunk-K2RJJBRU.js:7142
createBrowserClient @ VM7221 @supabase_ssr.js:718
createSupabaseBrowserClient @ VM7204 supabase.ts:5
AuthProvider @ VM7191 use-auth.tsx:43
renderWithHooks @ VM7196 chunk-276SZO74.js:11548
mountIndeterminateComponent @ VM7196 chunk-276SZO74.js:14926
beginWork @ VM7196 chunk-276SZO74.js:15914
beginWork$1 @ VM7196 chunk-276SZO74.js:19753
performUnitOfWork @ VM7196 chunk-276SZO74.js:19198
workLoopSync @ VM7196 chunk-276SZO74.js:19137
renderRootSync @ VM7196 chunk-276SZO74.js:19116
performConcurrentWorkOnRoot @ VM7196 chunk-276SZO74.js:18678
workLoop @ VM7196 chunk-276SZO74.js:197
flushWork @ VM7196 chunk-276SZO74.js:176
performWorkUntilDeadline @ VM7196 chunk-276SZO74.js:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2Fjsonplaceholder.typicode.com%2Fusers%2F1
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: {id: 1, name: 'Leanne Graham', username: 'Bret', email: '<EMAIL>', address: {…}, …}
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2F67e53c0318194932a5852f51.mockapi.io%2Fapi%2Fv1%2Ffireindex
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (31) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (31) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (31) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (31) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: fwi
eruda.js:2 Updating valueLabel with API data: 30
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Gauge value: 30, clampedValue: 30, angle: -36°
eruda.js:2 [vite] server connection lost. Polling for restart...
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ VM7161 eruda.js:2
ping @ VM7183 client:736
waitForSuccessfulPing @ VM7183 client:749
(anonymous) @ VM7183 client:561
29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@vite/client:562 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/designer 502 (Bad Gateway)
(anonymous) @ VM7183 client:562
eruda.js:2 [vite] server connection lost. Polling for restart...
canvas.tsx:22 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745651371648 net::ERR_ABORTED 502 (Bad Gateway)
eruda.js:2 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
window.fetch @ Network.js:219
ping @ client:736
waitForSuccessfulPing @ client:749
(anonymous) @ client:561
workspace_iframe.html:151 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ workspace_iframe.html?initialPath=%2F&id=%3Ar3fj%3A:151
workspace_iframe.html:151 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ workspace_iframe.html:151
eruda.js:2 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=af1a9c3b:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6975
createClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7142
createBrowserClient @ @supabase_ssr.js?v=af1a9c3b:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=af1a9c3b:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=af1a9c3b:14926
beginWork @ chunk-276SZO74.js?v=af1a9c3b:15914
beginWork$1 @ chunk-276SZO74.js?v=af1a9c3b:19753
performUnitOfWork @ chunk-276SZO74.js?v=af1a9c3b:19198
workLoopSync @ chunk-276SZO74.js?v=af1a9c3b:19137
renderRootSync @ chunk-276SZO74.js?v=af1a9c3b:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18678
workLoop @ chunk-276SZO74.js?v=af1a9c3b:197
flushWork @ chunk-276SZO74.js?v=af1a9c3b:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=af1a9c3b:384
eruda.js:2 Auth state changed: INITIAL_SESSION
eruda.js:2 Auth state changed: SIGNED_IN
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: undefined
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 Found teams for user: [{…}]
eruda.js:2 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
eruda.js:2 Slides fetched: (4) [{…}, {…}, {…}, {…}]
eruda.js:2 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2F67e53c0318194932a5852f51.mockapi.io%2Fapi%2Fv1%2Ffireindex
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: undefined
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: undefined
eruda.js:2 localContent dataField: undefined
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.text
eruda.js:2 localContent dataField: 
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Gauge value: 30, percentOfRange: 0.30, angle: -36.0°
eruda.js:2 API data useEffect triggered
eruda.js:2 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
eruda.js:2 Selected element type: api
eruda.js:2 localContent subtype: api.gauge
eruda.js:2 localContent dataField: fwi
eruda.js:2 Updating valueLabel with API data: 30
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 Gauge value: 30, percentOfRange: 0.30, angle: -36.0°
eruda.js:2 Canvas dataIndex value: 0
eruda.js:2 [vite] server connection lost. Polling for restart...
client:602 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/index.css?t=1745651889995&v=R5lxVGfI3ocQ8Gz9e2J1a net::ERR_ABORTED 502 (Bad Gateway)
importUpdatedModule @ client:602
fetchUpdate @ client:211
queueUpdate @ client:186
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
eruda.js:2 [hmr] Failed to reload /src/index.css?v=R5lxVGfI3ocQ8Gz9e2J1a. This could be due to syntax errors or importing non-existent modules. (see errors above)
Mt.forEach.n.<computed> @ Console.js:61
warnFailedUpdate @ client:176
fetchUpdate @ client:213
await in fetchUpdate
queueUpdate @ client:186
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
client:602 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745651889995 net::ERR_ABORTED 502 (Bad Gateway)
importUpdatedModule @ client:602
fetchUpdate @ client:211
queueUpdate @ client:186
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
eruda.js:2 [hmr] Failed to reload /src/components/designer/gauge-speedometer.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above)
Mt.forEach.n.<computed> @ Console.js:61
warnFailedUpdate @ client:176
fetchUpdate @ client:213
await in fetchUpdate
queueUpdate @ client:186
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
workspace_iframe.html?initialPath=%2F&id=%3Ar3hb%3A:151 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ workspace_iframe.html?initialPath=%2F&id=%3Ar3hb%3A:151
 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ 29810b14-7e88-452d-9…F&id=%3Ar3hb%3A:151
 
            
            
           GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ 29810b14-7e88-452d-9…F&id=%3Ar3hb%3A:151
  GET https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/ 502 (Bad Gateway)
(anonymous) @ 29810b14-7e88-452d-9…F&id=%3Ar3hb%3A:151
Console.js:61 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=af1a9c3b:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6975
createClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7142
createBrowserClient @ @supabase_ssr.js?v=af1a9c3b:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=af1a9c3b:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=af1a9c3b:14926
beginWork @ chunk-276SZO74.js?v=af1a9c3b:15914
beginWork$1 @ chunk-276SZO74.js?v=af1a9c3b:19753
performUnitOfWork @ chunk-276SZO74.js?v=af1a9c3b:19198
workLoopSync @ chunk-276SZO74.js?v=af1a9c3b:19137
renderRootSync @ chunk-276SZO74.js?v=af1a9c3b:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18678
workLoop @ chunk-276SZO74.js?v=af1a9c3b:197
flushWork @ chunk-276SZO74.js?v=af1a9c3b:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=af1a9c3b:384
Console.js:61 Auth state changed: INITIAL_SESSION
Console.js:61 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=af1a9c3b:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6975
createClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7142
createBrowserClient @ @supabase_ssr.js?v=af1a9c3b:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=af1a9c3b:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=af1a9c3b:14926
beginWork @ chunk-276SZO74.js?v=af1a9c3b:15914
beginWork$1 @ chunk-276SZO74.js?v=af1a9c3b:19753
performUnitOfWork @ chunk-276SZO74.js?v=af1a9c3b:19198
workLoopSync @ chunk-276SZO74.js?v=af1a9c3b:19137
renderRootSync @ chunk-276SZO74.js?v=af1a9c3b:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18678
workLoop @ chunk-276SZO74.js?v=af1a9c3b:197
flushWork @ chunk-276SZO74.js?v=af1a9c3b:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=af1a9c3b:384
Console.js:61 Auth state changed: INITIAL_SESSION
Console.js:61 Auth state changed: SIGNED_IN
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 API data useEffect triggered
Console.js:61 API data: undefined
Console.js:61 Selected element type: undefined
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 Found teams for user: [{…}]
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Slides fetched: (4) [{…}, {…}, {…}, {…}]
Console.js:61 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2F67e53c0318194932a5852f51.mockapi.io%2Fapi%2Fv1%2Ffireindex
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: undefined
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.text
Console.js:61 localContent dataField: 
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: 36.0°
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.gauge
Console.js:61 localContent dataField: fwi
Console.js:61 Updating valueLabel with API data: 30
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: 36.0°
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=af1a9c3b:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6975
createClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7142
createBrowserClient @ @supabase_ssr.js?v=af1a9c3b:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=af1a9c3b:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=af1a9c3b:14926
beginWork @ chunk-276SZO74.js?v=af1a9c3b:15914
beginWork$1 @ chunk-276SZO74.js?v=af1a9c3b:19753
performUnitOfWork @ chunk-276SZO74.js?v=af1a9c3b:19198
workLoopSync @ chunk-276SZO74.js?v=af1a9c3b:19137
renderRootSync @ chunk-276SZO74.js?v=af1a9c3b:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18678
workLoop @ chunk-276SZO74.js?v=af1a9c3b:197
flushWork @ chunk-276SZO74.js?v=af1a9c3b:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=af1a9c3b:384
Console.js:61 Auth state changed: INITIAL_SESSION
Console.js:61 Auth state changed: SIGNED_IN
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 API data useEffect triggered
Console.js:61 API data: undefined
Console.js:61 Selected element type: undefined
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 Found teams for user: [{…}]
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Slides fetched: (4) [{…}, {…}, {…}, {…}]
Console.js:61 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2F67e53c0318194932a5852f51.mockapi.io%2Fapi%2Fv1%2Ffireindex
Console.js:61 API data useEffect triggered
Console.js:61 API data: undefined
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 API data useEffect triggered
Console.js:61 API data: undefined
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.text
Console.js:61 localContent dataField: 
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.text
Console.js:61 localContent dataField: 
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.gauge
Console.js:61 localContent dataField: fwi
Console.js:61 Updating valueLabel with API data: 30
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge value: 30, ratio: 0.30, angle: -36.0°
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Gauge calculation: min=0, max=100, value=30, normalized=0.30, angle=-36.0°
chunk-276SZO74.js?v=af1a9c3b:16670 Uncaught ReferenceError: valueRatio is not defined
    at renderGauge (gauge-speedometer.tsx?t=1745653337428:146:50)
    at gauge-speedometer.tsx?t=1745653337428:49:7
    at commitHookEffectListMount (chunk-276SZO74.js?v=af1a9c3b:16915:34)
    at commitPassiveMountOnFiber (chunk-276SZO74.js?v=af1a9c3b:18156:19)
    at commitPassiveMountEffects_complete (chunk-276SZO74.js?v=af1a9c3b:18129:17)
    at commitPassiveMountEffects_begin (chunk-276SZO74.js?v=af1a9c3b:18119:15)
    at commitPassiveMountEffects (chunk-276SZO74.js?v=af1a9c3b:18109:11)
    at flushPassiveEffectsImpl (chunk-276SZO74.js?v=af1a9c3b:19490:11)
    at flushPassiveEffects (chunk-276SZO74.js?v=af1a9c3b:19447:22)
    at commitRootImpl (chunk-276SZO74.js?v=af1a9c3b:19416:13)
renderGauge @ gauge-speedometer.tsx?t=1745653337428:146
(anonymous) @ gauge-speedometer.tsx?t=1745653337428:49
commitHookEffectListMount @ chunk-276SZO74.js?v=af1a9c3b:16915
commitPassiveMountOnFiber @ chunk-276SZO74.js?v=af1a9c3b:18156
commitPassiveMountEffects_complete @ chunk-276SZO74.js?v=af1a9c3b:18129
commitPassiveMountEffects_begin @ chunk-276SZO74.js?v=af1a9c3b:18119
commitPassiveMountEffects @ chunk-276SZO74.js?v=af1a9c3b:18109
flushPassiveEffectsImpl @ chunk-276SZO74.js?v=af1a9c3b:19490
flushPassiveEffects @ chunk-276SZO74.js?v=af1a9c3b:19447
commitRootImpl @ chunk-276SZO74.js?v=af1a9c3b:19416
commitRoot @ chunk-276SZO74.js?v=af1a9c3b:19277
performSyncWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18895
flushSyncCallbacks @ chunk-276SZO74.js?v=af1a9c3b:9119
flushSync @ chunk-276SZO74.js?v=af1a9c3b:18959
scheduleRefresh @ chunk-276SZO74.js?v=af1a9c3b:20004
(anonymous) @ @react-refresh:267
performReactRefresh @ @react-refresh:256
(anonymous) @ @react-refresh:677
setTimeout
(anonymous) @ @react-refresh:666
validateRefreshBoundaryAndEnqueueUpdate @ @react-refresh:728
(anonymous) @ gauge-speedometer.tsx?t=1745652776389:191
(anonymous) @ client:34
(anonymous) @ client:218
(anonymous) @ client:193
queueUpdate @ client:193
await in queueUpdate
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
Console.js:61 The above error occurred in the <Gauge> component:

    at Gauge (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/gauge-speedometer.tsx?t=1745653337428:21:3)
    at div
    at PresenceChild (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=af1a9c3b:7742:24)
    at AnimatePresence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/framer-motion.js?v=af1a9c3b:7800:26)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at Canvas (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/designer/canvas.tsx?t=1745652776389:33:3)
    at ErrorBoundary (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/error-boundary.tsx:10:5)
    at div
    at div
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at Designer (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/designer.tsx?t=1745652776389:68:20)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745652776389&v=FRcKm8XneRrQFqAKsuW9R:31:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=af1a9c3b:2805:3)

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
Mt.forEach.n.<computed> @ Console.js:61
logCapturedError @ chunk-276SZO74.js?v=af1a9c3b:14032
callback @ chunk-276SZO74.js?v=af1a9c3b:14078
callCallback @ chunk-276SZO74.js?v=af1a9c3b:11248
commitUpdateQueue @ chunk-276SZO74.js?v=af1a9c3b:11265
commitLayoutEffectOnFiber @ chunk-276SZO74.js?v=af1a9c3b:17075
commitLayoutMountEffects_complete @ chunk-276SZO74.js?v=af1a9c3b:17980
commitLayoutEffects_begin @ chunk-276SZO74.js?v=af1a9c3b:17969
commitLayoutEffects @ chunk-276SZO74.js?v=af1a9c3b:17920
commitRootImpl @ chunk-276SZO74.js?v=af1a9c3b:19353
commitRoot @ chunk-276SZO74.js?v=af1a9c3b:19277
performSyncWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18895
flushSyncCallbacks @ chunk-276SZO74.js?v=af1a9c3b:9119
flushSync @ chunk-276SZO74.js?v=af1a9c3b:18959
scheduleRefresh @ chunk-276SZO74.js?v=af1a9c3b:20004
(anonymous) @ @react-refresh:267
performReactRefresh @ @react-refresh:256
(anonymous) @ @react-refresh:677
setTimeout
(anonymous) @ @react-refresh:666
validateRefreshBoundaryAndEnqueueUpdate @ @react-refresh:728
(anonymous) @ gauge-speedometer.tsx?t=1745652776389:191
(anonymous) @ client:34
(anonymous) @ client:218
(anonymous) @ client:193
queueUpdate @ client:193
await in queueUpdate
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
Console.js:61 Error caught by ErrorBoundary: ReferenceError: valueRatio is not defined
    at renderGauge (gauge-speedometer.tsx?t=1745653337428:146:50)
    at gauge-speedometer.tsx?t=1745653337428:49:7
    at commitHookEffectListMount (chunk-276SZO74.js?v=af1a9c3b:16915:34)
    at commitPassiveMountOnFiber (chunk-276SZO74.js?v=af1a9c3b:18156:19)
    at commitPassiveMountEffects_complete (chunk-276SZO74.js?v=af1a9c3b:18129:17)
    at commitPassiveMountEffects_begin (chunk-276SZO74.js?v=af1a9c3b:18119:15)
    at commitPassiveMountEffects (chunk-276SZO74.js?v=af1a9c3b:18109:11)
    at flushPassiveEffectsImpl (chunk-276SZO74.js?v=af1a9c3b:19490:11)
    at flushPassiveEffects (chunk-276SZO74.js?v=af1a9c3b:19447:22)
    at commitRootImpl (chunk-276SZO74.js?v=af1a9c3b:19416:13) {componentStack: '\n    at Gauge (https://29810b14-7e88-452d-9d46-13e…/deps/@tanstack_react-query.js?v=af1a9c3b:2805:3)'}
Mt.forEach.n.<computed> @ Console.js:61
componentDidCatch @ error-boundary.tsx:40
callback @ chunk-276SZO74.js?v=af1a9c3b:14084
callCallback @ chunk-276SZO74.js?v=af1a9c3b:11248
commitUpdateQueue @ chunk-276SZO74.js?v=af1a9c3b:11265
commitLayoutEffectOnFiber @ chunk-276SZO74.js?v=af1a9c3b:17075
commitLayoutMountEffects_complete @ chunk-276SZO74.js?v=af1a9c3b:17980
commitLayoutEffects_begin @ chunk-276SZO74.js?v=af1a9c3b:17969
commitLayoutEffects @ chunk-276SZO74.js?v=af1a9c3b:17920
commitRootImpl @ chunk-276SZO74.js?v=af1a9c3b:19353
commitRoot @ chunk-276SZO74.js?v=af1a9c3b:19277
performSyncWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18895
flushSyncCallbacks @ chunk-276SZO74.js?v=af1a9c3b:9119
flushSync @ chunk-276SZO74.js?v=af1a9c3b:18959
scheduleRefresh @ chunk-276SZO74.js?v=af1a9c3b:20004
(anonymous) @ @react-refresh:267
performReactRefresh @ @react-refresh:256
(anonymous) @ @react-refresh:677
setTimeout
(anonymous) @ @react-refresh:666
validateRefreshBoundaryAndEnqueueUpdate @ @react-refresh:728
(anonymous) @ gauge-speedometer.tsx?t=1745652776389:191
(anonymous) @ client:34
(anonymous) @ client:218
(anonymous) @ client:193
queueUpdate @ client:193
await in queueUpdate
(anonymous) @ client:642
handleMessage @ client:640
(anonymous) @ client:550
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge calculation: min=0, max=100, value=30, normalized=0.30, angle=-36.0°
Console.js:61 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Mt.forEach.n.<computed> @ Console.js:61
_GoTrueClient @ chunk-K2RJJBRU.js?v=af1a9c3b:5040
SupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6906
_initSupabaseAuthClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7102
SupabaseClient @ chunk-K2RJJBRU.js?v=af1a9c3b:6975
createClient @ chunk-K2RJJBRU.js?v=af1a9c3b:7142
createBrowserClient @ @supabase_ssr.js?v=af1a9c3b:718
createSupabaseBrowserClient @ supabase.ts:10
AuthProvider @ use-auth.tsx:40
renderWithHooks @ chunk-276SZO74.js?v=af1a9c3b:11548
mountIndeterminateComponent @ chunk-276SZO74.js?v=af1a9c3b:14926
beginWork @ chunk-276SZO74.js?v=af1a9c3b:15914
beginWork$1 @ chunk-276SZO74.js?v=af1a9c3b:19753
performUnitOfWork @ chunk-276SZO74.js?v=af1a9c3b:19198
workLoopSync @ chunk-276SZO74.js?v=af1a9c3b:19137
renderRootSync @ chunk-276SZO74.js?v=af1a9c3b:19116
performConcurrentWorkOnRoot @ chunk-276SZO74.js?v=af1a9c3b:18678
workLoop @ chunk-276SZO74.js?v=af1a9c3b:197
flushWork @ chunk-276SZO74.js?v=af1a9c3b:176
performWorkUntilDeadline @ chunk-276SZO74.js?v=af1a9c3b:384
Console.js:61 Auth state changed: INITIAL_SESSION
Console.js:61 Auth state changed: SIGNED_IN
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: (3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 API data useEffect triggered
Console.js:61 API data: undefined
Console.js:61 Selected element type: undefined
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 Found teams for user: [{…}]
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Fetching slides for teamId: c3a76e6d-2f37-4b41-b5bc-acc9f244b439
Console.js:61 Slides fetched: (4) [{…}, {…}, {…}, {…}]
Console.js:61 Fetching API data via proxy: /api/proxy?url=https%3A%2F%2F67e53c0318194932a5852f51.mockapi.io%2Fapi%2Fv1%2Ffireindex
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: undefined
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: undefined
Console.js:61 localContent dataField: undefined
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.text
Console.js:61 localContent dataField: 
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge calculation: min=0, max=100, value=30, normalized=0.30, angle=-36.0°
Console.js:61 API data useEffect triggered
Console.js:61 API data: (36) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Console.js:61 Selected element type: api
Console.js:61 localContent subtype: api.gauge
Console.js:61 localContent dataField: fwi
Console.js:61 Updating valueLabel with API data: 30
Console.js:61 Canvas dataIndex value: 0
Console.js:61 Gauge calculation: min=0, max=100, value=30, normalized=0.30, angle=-36.0°
