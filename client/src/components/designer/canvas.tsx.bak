import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import { 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  Minimize,
  MousePointer,
  Layers,
  Image,
  Video,
  Play,
  File
} from "lucide-react";

interface CanvasProps {
  width: number;
  height: number;
  elements: CanvasElement[];
  onSelectElement: (id: string | null) => void;
  onUpdateElement: (id: string, updates: Partial<CanvasElement>) => void;
  onDeleteElement: (id: string) => void;
  selectedElementId: string | null;
  backgroundColor?: string;
  isPreviewMode?: boolean;
  currentPreviewElements?: any[];
}

export interface CanvasElement {
  id: string;
  type: 'text' | 'image' | 'video' | 'shape' | 'date' | 'multimedia' | 'playlist' | 'api';
  x: number;
  y: number;
  width: number;
  height: number;
  content: any;
  style: any;
  teamId?: string;
  zIndex?: number; // Element layer order
  animation?: {
    type: 'fade-in' | 'slide-in' | 'zoom-in' | 'bounce' | 'rotate' | 'flip-x' | 'flip-y' | 'none';
    direction?: 'left' | 'right' | 'top' | 'bottom';
    duration: number; // in milliseconds
    delay: number; // in milliseconds
    easing?: string; // CSS easing function
    _previewKey?: number; // Used to force animation re-renders
  };
}

export function Canvas({
  width,
  height,
  elements,
  onSelectElement,
  onUpdateElement,
  onDeleteElement,
  selectedElementId,
  backgroundColor = "#ffffff",
  isPreviewMode = false,
  currentPreviewElements = []
}: CanvasProps) {
  const [zoom, setZoom] = useState(0.5); // Default zoom set to 50%
  const [draggingElement, setDraggingElement] = useState<string | null>(null);
  const [resizingElement, setResizingElement] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  // Add a local state to track API data index for re-renders
  const [localApiDataIndex, setLocalApiDataIndex] = useState<number>(0);
  const lastApiDataIndexRef = useRef<number>(0);
  
  // Effect to handle video playback when preview mode changes
  useEffect(() => {
    if (isPreviewMode && canvasRef.current) {
      // Find all videos in the canvas
      const videoElements = canvasRef.current.querySelectorAll('video');
      
      // Play all videos
      videoElements.forEach(video => {
        // Make sure video is ready to play
        video.muted = true;
        video.loop = true;
        
        // Force play the video
        const playPromise = video.play();
        
        if (playPromise !== undefined) {
          playPromise.catch(error => {
            console.error("Error playing video:", error);
          });
        }
      });
    }
  }, [isPreviewMode]);
  
  // Add an interval to check API data index changes and force re-render when it changes
  useEffect(() => {
    if (!isPreviewMode) return;
    
    // Reset local index on preview start
    setLocalApiDataIndex(0);
    lastApiDataIndexRef.current = 0;
    
    const checkApiDataIndexInterval = setInterval(() => {
      const apiDataIndex = window.__API_DATA_INDEX__ || 0;
      
      // Check if the API data index has changed
      if (apiDataIndex !== lastApiDataIndexRef.current) {
        console.log(`Canvas detected API data index change: ${lastApiDataIndexRef.current} -> ${apiDataIndex}`);
        
        // Update the last known index
        lastApiDataIndexRef.current = apiDataIndex;
        
        // Update local state to force a re-render of the whole canvas
        setLocalApiDataIndex(apiDataIndex);
      }
    }, 200); // Check more frequently - 5 times per second
    
    return () => {
      clearInterval(checkApiDataIndexInterval);
    };
  }, [isPreviewMode]);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.1));
  };

  const handleZoomReset = () => {
    setZoom(0.5); // Reset to 50% zoom
  };

  const handleElementMouseDown = (e: React.MouseEvent, elementId: string) => {
    // Stop event propagation to prevent canvas from handling it
    e.stopPropagation();
    
    // Select the element
    onSelectElement(elementId);
    
    // Start dragging the element
    setDraggingElement(elementId);
    
    const element = elements.find(el => el.id === elementId);
    if (element) {
      const rect = (e.target as HTMLDivElement).getBoundingClientRect();
      
      // Calculate drag offset relative to element position
      // This accounts for clicks anywhere in the element, not just the top-left corner
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      
      // Prevent any default browser behavior that might interfere
      e.preventDefault();
    }
  };

  const handleElementResize = (e: React.MouseEvent, elementId: string) => {
    e.stopPropagation();
    e.preventDefault();
    
    setResizingElement(elementId);
  };

  const handleCanvasMouseMove = (e: React.MouseEvent) => {
    if (draggingElement) {
      setIsDragging(true);
      
      const canvas = canvasRef.current;
      if (canvas) {
        const rect = canvas.getBoundingClientRect();
        
        // Adjust coordinates according to zoom factor
        const x = (e.clientX - rect.left) / zoom - dragOffset.x / zoom;
        const y = (e.clientY - rect.top) / zoom - dragOffset.y / zoom;
        
        // No boundary enforcement - elements can be dragged outside canvas
        onUpdateElement(draggingElement, { x, y });
      }
    } else if (resizingElement) {
      const element = elements.find(el => el.id === resizingElement);
      if (element && canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect();
        const canvasX = (e.clientX - rect.left) / zoom;
        const canvasY = (e.clientY - rect.top) / zoom;
        
        // Only enforce minimum size but allow resize beyond canvas bounds
        const newWidth = Math.max(20, canvasX - element.x);
        const newHeight = Math.max(20, canvasY - element.y);
        
        onUpdateElement(resizingElement, { width: newWidth, height: newHeight });
      }
    }
  };

  const handleCanvasMouseUp = (e: React.MouseEvent) => {
    // Don't deselect element on mouse up - we only want to end dragging/resizing
    // The element selection should persist until user explicitly clicks elsewhere
    setDraggingElement(null);
    setResizingElement(null);
    setTimeout(() => setIsDragging(false), 0);
    
    // Prevent event from propagating which could trigger deselection
    e.stopPropagation();
  };

  const renderElement = (element: CanvasElement) => {
    const isSelected = element.id === selectedElementId;
    
    const elementStyle = {
      left: `${element.x}px`,
      top: `${element.y}px`,
      width: `${element.width}px`,
      height: `${element.height}px`,
      zIndex: element.zIndex || 0, // Apply zIndex property, default to 0 if not set
      ...element.style
    };

    let content;
    switch (element.type) {
      case 'text': {
        const style = element.content.style || {};
        const displayStyle = {
          ...style,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: style.verticalAlign === 'top' ? 'flex-start' : 
                      style.verticalAlign === 'bottom' ? 'flex-end' : 
                      'center',
          justifyContent: style.textAlign === 'left' ? 'flex-start' : 
                          style.textAlign === 'right' ? 'flex-end' : 
                          'center'
        };
        content = <div style={displayStyle}>{element.content.text || 'Sample Text'}</div>;
        break;
      }
      case 'image':
        content = <img src={element.content.src} alt={element.content.alt || ""} className="w-full h-full object-cover" />;
        break;
      case 'video':
        content = (
          <video 
            className="w-full h-full" 
            controls={!isPreviewMode}
            autoPlay={isPreviewMode}
            muted={isPreviewMode}
            loop={isPreviewMode}
            playsInline
            preload="auto"
            src={element.content.src}
          ></video>
        );
        break;
      case 'api': {
        // Get subtype from content
        const subtype = element.content?.subtype || 'api.text';
        
        // Get content or provide defaults
        const { dataField, placeholderUrl, style } = element.content || {};
        
        // Get field data from API response if available
        let fieldData = null;
        if (dataField && window.__API_DATA__) {
          try {
            console.log('Accessing API data field:', dataField);
            
            // Dump all the top-level keys for comparison (debug)
            console.log('API Data available keys:', Object.keys(window.__API_DATA__));
            
            // Helper function to find matching key (case insensitive)
            const findKeyIgnoreCase = (obj: Record<string, any> | any[], key: string): string | null => {
              if (!obj || typeof obj !== 'object') return null;
              
              // Check direct match first
              if (obj.hasOwnProperty(key)) return key;
              
              // Try case-insensitive match
              const lowerKey = key.toLowerCase();
              const keys = Object.keys(obj);
              for (const objKey of keys) {
                if (objKey.toLowerCase() === lowerKey) {
                  return objKey;
                }
              }
              
              return null;
            };
            
            // First, check if the API data is an array of objects
            if (Array.isArray(window.__API_DATA__) && window.__API_DATA__.length > 0) {
              console.log("API data is an array of objects");
              
              // Get the appropriate object in the array based on current API data index
              // CRITICAL: Always use the global window.__API_DATA_INDEX__ in preview mode
              // Default to first object (0) if not in preview mode or if global index isn't set
              const globalIndex = typeof window.__API_DATA_INDEX__ === 'number' ? window.__API_DATA_INDEX__ : 0;
              const currentIndex = isPreviewMode 
                ? Math.min(globalIndex, window.__API_DATA__.length - 1) 
                : 0;
              
              // Log detailed info about which API data index we're using
              console.log(`API data cycling: Using index ${currentIndex} (global=${globalIndex}) for element ${element.id}`);
              
              console.log(`Using API data record at index: ${currentIndex}`);
              const currentObject = window.__API_DATA__[currentIndex] as Record<string, any>;
              
              // Make sure it's an object and not a primitive
              if (currentObject && typeof currentObject === 'object') {
                // Try to find the field in the object
                const matchingKey = findKeyIgnoreCase(currentObject, dataField);
                if (matchingKey) {
                  console.log(`Found matching key "${matchingKey}" in data record at index ${currentIndex} for "${dataField}"`);
                  fieldData = currentObject[matchingKey];
                  console.log("Field data found:", fieldData);
                } else {
                  console.log(`No matching field found in API data for "${dataField}"`);
                }
              }
            } else if (window.__API_DATA__ && typeof window.__API_DATA__ === 'object') {
              // Try to find a direct match in the top-level object
              const matchingKey = findKeyIgnoreCase(window.__API_DATA__, dataField);
              if (matchingKey) {
                console.log(`Found matching key "${matchingKey}" for "${dataField}" in API data`);
                fieldData = window.__API_DATA__[matchingKey];
                console.log("Field data found:", fieldData);
              } else {
                // Try to find nested arrays with the field
                for (const key in window.__API_DATA__) {
                  if (Array.isArray(window.__API_DATA__[key]) && window.__API_DATA__[key].length > 0) {
                    // If we found an array, try to get a suitable object from it
                    const array = window.__API_DATA__[key];
                    const firstItem = array[0];
                    if (firstItem && typeof firstItem === 'object') {
                      const nestedMatch = findKeyIgnoreCase(firstItem, dataField);
                      if (nestedMatch) {
                        console.log(`Found matching field "${nestedMatch}" in nested array at key "${key}"`);
                        // Get the appropriate object in the array based on current API data index
                        const globalIndex = typeof window.__API_DATA_INDEX__ === 'number' ? window.__API_DATA_INDEX__ : 0;
                        const currentIndex = isPreviewMode 
                          ? Math.min(globalIndex, array.length - 1) 
                          : 0;
                        fieldData = array[currentIndex][nestedMatch];
                        console.log("Field data found:", fieldData);
                        break;
                      }
                    }
                  }
                }
              }
            }
          } catch (error) {
            console.error('Error accessing API data:', error);
          }
        }
        
        // Render based on subtype
        switch (subtype) {
          case 'api.text': {
            const textStyle = style || {};
            const displayStyle = {
              ...textStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: textStyle.verticalAlign === 'top' ? 'flex-start' : 
                        textStyle.verticalAlign === 'bottom' ? 'flex-end' : 
                        'center',
              justifyContent: textStyle.textAlign === 'left' ? 'flex-start' : 
                            textStyle.textAlign === 'right' ? 'flex-end' : 
                            'center'
            };
            
            content = (
              <div style={displayStyle} className="overflow-hidden">
                {fieldData !== null ? String(fieldData) : (element.content.placeholder || 'API Data')}
              </div>
            );
            break;
          }
          
          case 'api.image': {
            content = (
              <img 
                src={fieldData !== null ? String(fieldData) : (placeholderUrl || '')} 
                alt={element.content.alt || "API Image"} 
                className="w-full h-full object-cover" 
              />
            );
            break;
          }
          
          case 'api.video': {
            content = (
              <video 
                className="w-full h-full" 
                controls={!isPreviewMode}
                autoPlay={isPreviewMode}
                muted={isPreviewMode}
                loop={isPreviewMode}
                playsInline
                preload="auto"
                src={fieldData !== null ? String(fieldData) : (placeholderUrl || '')}
              ></video>
            );
            break;
          }
          
          default: {
            // Default to text display
            content = (
              <div style={{width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
                {fieldData !== null ? String(fieldData) : (element.content.placeholder || 'API Data')}
              </div>
            );
          }
        }
        break;
      }
      case 'shape': {
        const commonStyle = {
          backgroundColor: element.content.fill,
          border: element.content.strokeWidth ? `${element.content.strokeWidth}px solid ${element.content.stroke || '#000000'}` : 'none',
          width: '100%',
          height: '100%'
        };
        
        switch (element.content.shape) {
          case 'rectangle':
            content = <div style={commonStyle}></div>;
            break;
            
          case 'circle':
            content = <div style={{...commonStyle, borderRadius: '50%'}}></div>;
            break;
            
          case 'triangle':
            content = (
              <div style={{position: 'relative', width: '100%', height: '100%'}}>
                <div style={{
                  position: 'absolute',
                  width: '100%',
                  height: '100%',
                  clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)',
                  backgroundColor: element.content.fill,
                  border: element.content.strokeWidth ? `${element.content.strokeWidth}px solid ${element.content.stroke || '#000000'}` : 'none',
                }}></div>
              </div>
            );
            break;
            
          case 'pentagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)',
              }}></div>
            );
            break;
            
          case 'hexagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)',
              }}></div>
            );
            break;
            
          case 'octagon':
            content = (
              <div style={{
                ...commonStyle,
                clipPath: 'polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%)',
              }}></div>
            );
            break;
            
          case 'oval':
            content = <div style={{...commonStyle, borderRadius: '50% / 50%'}}></div>;
            break;
            
          case 'semicircle':
            content = (
              <div style={{
                ...commonStyle,
                height: '50%',
                marginTop: '50%',
                borderRadius: '100% 100% 0 0',
              }}></div>
            );
            break;
            
          case 'star':
            content = (
              <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                <svg
                  viewBox="0 0 24 24"
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'block',
                  }}
                >
                  <path
                    d="M12,2L16.2,8.5L23.4,9.1L17.8,14.3L19.1,21.5L12,18.1L4.9,21.5L6.2,14.3L0.6,9.1L7.8,8.5L12,2z"
                    fill={element.content.fill || '#f3f4f6'}
                    stroke={element.content.strokeWidth ? (element.content.stroke || '#000000') : 'none'}
                    strokeWidth={element.content.strokeWidth || 0}
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            );
            break;
            
          case 'heart':
            content = (
              <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                <svg
                  viewBox="0 0 24 24"
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'block',
                  }}
                >
                  <path
                    d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"
                    fill={element.content.fill || '#f3f4f6'}
                    stroke={element.content.strokeWidth ? (element.content.stroke || '#000000') : 'none'}
                    strokeWidth={element.content.strokeWidth || 0}
                  />
                </svg>
              </div>
            );
            break;
            
          default:
            content = <div style={commonStyle}></div>;
        }
        break;
      }
      case 'date': {
        const style = element.content.style || {};
        const options = element.content.options || {};
        const locale = element.content.locale || 'en-US';
        
        // Extract the timeOnly flag
        const { timeOnly, ...dateOptions } = options;
        
        const displayStyle = {
          ...style,
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: style.verticalAlign === 'top' ? 'flex-start' : 
                      style.verticalAlign === 'bottom' ? 'flex-end' : 
                      'center',
          justifyContent: style.textAlign === 'left' ? 'flex-start' : 
                          style.textAlign === 'right' ? 'flex-end' : 
                          'center'
        };
        
        // Get the current date and time
        const now = new Date();
        
        // Format the date/time string based on options
        let dateTimeString;
        if (timeOnly) {
          // If timeOnly is true, use toLocaleTimeString instead of toLocaleDateString
          dateTimeString = now.toLocaleTimeString(locale, dateOptions);
        } else {
          // Otherwise use toLocaleDateString which includes the date
          dateTimeString = now.toLocaleDateString(locale, dateOptions);
        }
        
        content = (
          <div style={displayStyle}>
            {dateTimeString}
          </div>
        );
        break;
      }
      case 'multimedia': {
        // Get content or provide defaults for multimedia elements
        // These are regular media elements without API connections
        const { type, src, alt, style, text } = element.content || {};
        
        // Render based on the multimedia content type
        switch (type) {
          case 'image': {
            content = (
              <img 
                src={src || ''} 
                alt={alt || "Image"} 
                className="w-full h-full object-cover" 
              />
            );
            break;
          }
          
          case 'video': {
            content = (
              <video 
                className="w-full h-full" 
                controls={!isPreviewMode}
                autoPlay={isPreviewMode}
                muted={isPreviewMode}
                loop={isPreviewMode}
                playsInline
                preload="auto"
                src={src || ''}
              ></video>
            );
            break;
          }
          
          case 'text':
          default: {
            const textStyle = style || {};
            const displayStyle = {
              ...textStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: textStyle.verticalAlign === 'top' ? 'flex-start' : 
                          textStyle.verticalAlign === 'bottom' ? 'flex-end' : 
                          'center',
              justifyContent: textStyle.textAlign === 'left' ? 'flex-start' : 
                              textStyle.textAlign === 'right' ? 'flex-end' : 
                              'center'
            };
            
            content = (
              <div style={displayStyle} className="overflow-hidden">
                {text || 'Multimedia Text'}
              </div>
            );
            break;
          }
        }
        break;
      }
              if (!obj || typeof obj !== 'object') return null;
              
              // Check direct match first
              if (obj.hasOwnProperty(key)) return key;
              
              // Try case-insensitive match
              const lowerKey = key.toLowerCase();
              const keys = Object.keys(obj);
              for (const objKey of keys) {
                if (objKey.toLowerCase() === lowerKey) {
                  return objKey;
                }
              }
              
              return null;
            };
            
            // First, check if the API data is an array of objects
            if (Array.isArray(window.__API_DATA__) && window.__API_DATA__.length > 0) {
              console.log("API data is an array of objects");
              
              // Get the appropriate object in the array based on current API data index
              // CRITICAL: Always use the global window.__API_DATA_INDEX__ in preview mode
              // Default to first object (0) if not in preview mode or if global index isn't set
              const globalIndex = typeof window.__API_DATA_INDEX__ === 'number' ? window.__API_DATA_INDEX__ : 0;
              const currentIndex = isPreviewMode 
                ? Math.min(globalIndex, window.__API_DATA__.length - 1) 
                : 0;
              
              // Log detailed info about which API data index we're using
              console.log(`API data cycling: Using index ${currentIndex} (global=${globalIndex}) for element ${element.id}`);
              
              console.log(`Using API data record at index: ${currentIndex}`);
              const currentObject = window.__API_DATA__[currentIndex] as Record<string, any>;
              
              // Make sure it's an object and not a primitive
              if (typeof currentObject === 'object' && currentObject !== null) {
                // Check if we're looking for a property in the current object
                if (!dataField.includes('.')) {
                  const matchingKey = findKeyIgnoreCase(currentObject, dataField);
                  
                  if (matchingKey) {
                    // Use the data from the current item in the array
                    fieldData = currentObject[matchingKey];
                    console.log(`Found matching key "${matchingKey}" in data record at index ${currentIndex} for "${dataField}"`);
                  }
                } else {
                  // Handle nested paths but start with the current object from the array
                  const parts = dataField.split('.');
                  let current: any = currentObject;
                  
                  // Traverse the path parts
                  for (let i = 0; i < parts.length; i++) {
                    const part = parts[i];
                    
                    if (current === undefined || current === null) {
                      break;
                    }
                    
                    // Check if current is itself an array
                    if (Array.isArray(current)) {
                      // For array data, use the first item if available
                      if (current.length > 0) {
                        current = current[0];
                      } else {
                        break;
                      }
                    }
                    
                    // Find key with case-insensitive match
                    const matchingKey = findKeyIgnoreCase(current, part);
                    if (!matchingKey) {
                      console.log(`No matching key found for part: "${part}"`);
                      current = undefined;
                      break;
                    }
                    
                    // Access the next part
                    current = current[matchingKey];
                  }
                  
                  fieldData = current;
                }
              }
            } 
            // If not an array, check if we're dealing with a direct property (no dots)
            else if (!dataField.includes('.')) {
              // Find key with case-insensitive match
              const matchingKey = findKeyIgnoreCase(window.__API_DATA__ as Record<string, any>, dataField);
              if (matchingKey) {
                fieldData = (window.__API_DATA__ as Record<string, any>)[matchingKey];
                console.log(`Found matching key "${matchingKey}" for "${dataField}"`);
              }
            } else {
              // Handle nested paths with a case-insensitive approach
              const parts = dataField.split('.');
              let current: any = window.__API_DATA__;
              
              // Traverse the path parts
              for (let i = 0; i < parts.length; i++) {
                const part = parts[i];
                
                // If the current part doesn't exist or is null, stop traversal
                if (current === undefined || current === null) {
                  break;
                }
                
                // Check if current is an array
                if (Array.isArray(current)) {
                  // For array data, if there are more path parts remaining
                  // we need to access the first item and continue traversing
                  if (current.length > 0) {
                    // Try to continue with the first array item
                    current = current[0];
                  } else {
                    // Otherwise just return the array
                    break;
                  }
                }
                
                // Find key with case-insensitive match
                const matchingKey = findKeyIgnoreCase(current, part);
                if (!matchingKey) {
                  console.log(`No matching key found for part: "${part}"`);
                  current = undefined;
                  break;
                }
                
                // Move to the next part in the path using the matching key
                current = current[matchingKey];
              }
              
              fieldData = current;
            }
            
            console.log('Field data found:', fieldData);
            
          } catch (error) {
            console.error('Error processing API data field:', error);
          }
        }
        
        // Handle different data types
        if (dataType === 'text') {
          // For text data type, display the API data as text
          const textStyle = style || {};
          const displayStyle = {
            ...textStyle,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: textStyle.verticalAlign === 'top' ? 'flex-start' : 
                      textStyle.verticalAlign === 'bottom' ? 'flex-end' : 
                      'center',
            justifyContent: textStyle.textAlign === 'left' ? 'flex-start' : 
                          textStyle.textAlign === 'right' ? 'flex-end' : 
                          'center'
          };
          
          // Use field data if available, without the fallback text content
          const displayText = fieldData !== undefined && fieldData !== null ? String(fieldData) : 'No data';
          
          content = (
            <div style={displayStyle}>
              {displayText}
            </div>
          );
        } else if (dataType === 'image' || dataType === 'video') {
          // For image and video, we need to determine the actual source URL
          let actualSrc = '';
          
          const isValidUrl = (url: string) => {
            try {
              new URL(url);
              return true;
            } catch {
              return false;
            }
          };
          
          // Check if fieldData is a string URL
          if (fieldData && typeof fieldData === 'string') {
            if (isValidUrl(fieldData)) {
              actualSrc = fieldData;
            } else if (placeholderUrl) {
              // If field data is not a valid URL but we have a placeholder URL,
              // replace the placeholder with the field data
              actualSrc = placeholderUrl.replace(`{${dataField}}`, fieldData);
            }
          } 
          // Check if fieldData is an array of URLs (take the first one)
          else if (Array.isArray(fieldData) && fieldData.length > 0) {
            // If it's an array, use the first item
            const firstItem = fieldData[0];
            if (typeof firstItem === 'string') {
              if (isValidUrl(firstItem)) {
                actualSrc = firstItem;
                console.log('Using array item as URL:', actualSrc);
              } else if (placeholderUrl) {
                actualSrc = placeholderUrl.replace(`{${dataField}}`, firstItem);
              }
            }
          }
          
          if (dataType === 'video') {
            // Add necessary attributes to make the video playable across origins
            content = (
              <video 
                className="w-full h-full" 
                controls={!isPreviewMode}
                autoPlay={isPreviewMode}
                muted={isPreviewMode}
                loop={isPreviewMode}
                crossOrigin="anonymous"
                preload="auto"
                playsInline
                src={actualSrc}
                style={{
                  objectFit: 'fill', // Override the default 'contain' to stretch to fill 
                  width: '100%',
                  height: '100%'
                }}
              >
                Your browser does not support the video tag.
              </video>
            );
          } else { // Image type
            content = (
              <img 
                src={actualSrc} 
                alt="API data" 
                className="w-full h-full object-cover"
                crossOrigin="anonymous"
                onError={(e) => {
                  // Add fallback for image loading errors
                  console.error('Error loading image:', actualSrc);
                  // Show a more user-friendly message in the element
                  (e.target as HTMLImageElement).style.display = 'none';
                  e.currentTarget.insertAdjacentHTML('afterend', 
                    '<div class="w-full h-full flex items-center justify-center bg-muted p-4 text-sm">'+
                    '<p>Unable to load image from this source</p></div>'
                  );
                }}
              />
            );
          }
        }
        break;
      }
      case 'playlist': {
        // Get playlist data
        const playlist = element.content?.playlist || [];
        const currentIndex = element.content?.currentIndex || 0;
        
        // Check if we have any items
        if (playlist.length === 0) {
          content = (
            <div className="w-full h-full flex flex-col items-center justify-center bg-muted/20">
              <Image className="h-10 w-10 mb-2 text-muted-foreground" />
              <div className="text-center text-sm text-muted-foreground">
                <p>Multimedia Playlist</p>
                <p className="text-xs mt-1">No media items added</p>
              </div>
            </div>
          );
        } else {
          // Get the current item
          const currentItem = playlist[currentIndex] || playlist[0];
          const isImage = currentItem.fileType?.startsWith('image/');
          
          if (isImage) {
            content = (
              <div className="w-full h-full">
                <img 
                  src={currentItem.fileUrl} 
                  alt={currentItem.name} 
                  className="w-full h-full object-fill"
                  style={{
                    objectFit: 'fill',
                    width: '100%',
                    height: '100%'
                  }}
                />
                <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {currentIndex + 1}/{playlist.length}
                </div>
              </div>
            );
          } else {
            // Video content
            content = (
              <div className="w-full h-full">
                <video 
                  className="w-full h-full" 
                  controls={!isPreviewMode}
                  autoPlay={isPreviewMode}
                  muted={isPreviewMode}
                  loop={false} // Do not loop individual videos in playlist, we'll handle advancing
                  playsInline
                  style={{
                    objectFit: 'fill',
                    width: '100%',
                    height: '100%'
                  }}
                  src={currentItem.fileUrl} // Direct src to improve compatibility
                ></video>
                <div className="absolute bottom-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                  {currentIndex + 1}/{playlist.length}
                </div>
              </div>
            );
          }
        }
        break;
      }
      default:
        content = <div>Unsupported element type</div>;
    }

    // Define animation variants based on element's animation settings
    const getAnimationVariants = () => {
      if (!element.animation || element.animation.type === 'none') {
        return {
          hidden: { opacity: 1 },
          visible: { opacity: 1 }
        };
      }

      const duration = element.animation.duration || 1000;
      const delay = element.animation.delay || 0;
      const easing = element.animation.easing || "easeOut";
      
      switch (element.animation.type) {
        case 'fade-in':
          return {
            hidden: { opacity: 0 },
            visible: { 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
          
        case 'slide-in': {
          const direction = element.animation.direction || 'left';
          let initialX = 0;
          let initialY = 0;
          
          if (direction === 'left') initialX = -100;
          else if (direction === 'right') initialX = 100;
          else if (direction === 'top') initialY = -100;
          else if (direction === 'bottom') initialY = 100;
          
          return {
            hidden: { x: initialX, y: initialY, opacity: 0 },
            visible: { 
              x: 0, 
              y: 0, 
              opacity: 1,
              transition: { 
                type: 'spring',
                stiffness: 100,
                damping: 15,
                duration: duration / 1000, 
                delay: delay / 1000
              }
            }
          };
        }
        
        case 'zoom-in':
          return {
            hidden: { scale: 0, opacity: 0 },
            visible: { 
              scale: 1, 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
          
        case 'bounce':
          return {
            hidden: { y: -50, opacity: 0 },
            visible: { 
              y: 0, 
              opacity: 1,
              transition: { 
                type: 'spring',
                stiffness: 300,
                damping: 10,
                duration: duration / 1000, 
                delay: delay / 1000
              }
            }
          };
          
        case 'rotate':
          return {
            hidden: { rotate: -180, opacity: 0 },
            visible: { 
              rotate: 0, 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
          
        case 'flip-x':
          return {
            hidden: { rotateY: 90, opacity: 0 },
            visible: { 
              rotateY: 0, 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
          
        case 'flip-y':
          return {
            hidden: { rotateX: 90, opacity: 0 },
            visible: { 
              rotateX: 0, 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
          
        default:
          return {
            hidden: { opacity: 0 },
            visible: { 
              opacity: 1,
              transition: { 
                duration: duration / 1000, 
                delay: delay / 1000,
                ease: easing
              }
            }
          };
      }
    };
    
    const variants = getAnimationVariants();
    // Use the _previewKey if available to ensure animations re-trigger
    const motionKey = element.animation?._previewKey 
      ? `${element.id}-${element.animation._previewKey}` 
      : element.id;

    return (
      <motion.div
        key={motionKey}
        className={cn(
          "absolute cursor-move",
          isSelected && "outline outline-2 outline-secondary shadow-lg",
          isDragging && draggingElement === element.id && "opacity-70"
        )}
        style={elementStyle}
        data-element-id={element.id}
        data-element-type={element.type}
        onMouseDown={(e) => {
          // Prevent the canvas background's onMouseDown from firing
          e.stopPropagation();
          // Select this element
          handleElementMouseDown(e, element.id);
        }}
        onClick={(e) => {
          // Prevent any parent click handlers from firing
          e.stopPropagation();
          // Ensure the element remains selected after clicking
          if (!isSelected) {
            onSelectElement(element.id);
          }
        }}
        initial="hidden"
        animate="visible"
        variants={variants}
      >
        {content}
        {isSelected && (
          <div
            className="absolute bottom-0 right-0 cursor-se-resize"
            style={{
              width: '16px',
              height: '16px',
              borderRight: '3px solid black',
              borderBottom: '3px solid black',
              boxSizing: 'border-box'
            }}
            onMouseDown={(e) => {
              // Prevent element's onMouseDown from firing
              e.stopPropagation();
              // Start resize operation
              handleElementResize(e, element.id);
            }}
          />
        )}
      </motion.div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="bg-background border-b p-2 flex justify-end space-x-2">
        <Button variant="outline" size="sm" onClick={handleZoomOut} disabled={zoom <= 0.1}>
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button variant="outline" size="sm" onClick={handleZoomReset}>
          {Math.round(zoom * 100)}%
        </Button>
        <Button variant="outline" size="sm" onClick={handleZoomIn} disabled={zoom >= 2}>
          <ZoomIn className="h-4 w-4" />
        </Button>
      </div>
      
      <div
        className="flex-1 bg-muted overflow-auto p-10 flex items-center justify-center"
        onMouseUp={handleCanvasMouseUp}
        onMouseLeave={handleCanvasMouseUp}
        onMouseMove={handleCanvasMouseMove}
      >
        <div className="flex items-center justify-center h-full">
          {/* Using a container div that preserves aspect ratio */}
          <div 
            className="relative"
            style={{ 
              display: 'inline-block',
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            {/* This div maintains aspect ratio by padding-bottom technique */}
            <div 
              style={{ 
                width: `${width * zoom}px`,
                paddingBottom: `${(height / width) * 100}%`,
                position: 'relative' 
              }}
            >
              <div
                ref={canvasRef}
                className="absolute shadow-lg origin-top-left"
                style={{
                  top: 0,
                  left: 0,
                  width: `${width}px`,
                  height: `${height}px`,
                  transform: `scale(${zoom})`,
                  backgroundColor: backgroundColor,
                }}
                onMouseDown={(e) => {
                  // Only deselect if clicking directly on canvas background
                  if (e.target === e.currentTarget) {
                    e.stopPropagation();
                    onSelectElement(null);
                  }
                }}
              >
                {elements.map(renderElement)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
