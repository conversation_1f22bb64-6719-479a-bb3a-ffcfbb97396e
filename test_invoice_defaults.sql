-- Test script to verify that invoice table defaults are working

-- First, check the current table structure
\d invoices;

-- Check current default values
SELECT column_name, column_default, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'invoices' 
AND column_name IN ('invoice_date', 'paid_at', 'is_deleted');

-- Test inserting a record without timestamp fields to see if defaults work
INSERT INTO invoices (team_id, billing_cycle, qty, total_amount, external_subscription_id, is_deleted)
VALUES (
    'c3a76e6d-2f37-4b41-b5bc-acc9f244b439',  -- Replace with actual team_id
    'monthly',
    5,
    50.00,
    'TEST123',
    false
) RETURNING *;

-- If the above insert works and shows timestamps, then defaults are working
-- If not, we need to fix the default values

-- Clean up the test record (optional)
-- DELETE FROM invoices WHERE external_subscription_id = 'TEST123';
