import React, { createContext, useContext, useState, ReactNode } from 'react';

interface SidebarContextType {
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
  toggleCollapse: () => void;
  collapseOnDesignerClick: () => void;
  isMobileOpen: boolean;
  setIsMobileOpen: (open: boolean) => void;
  toggleMobileSidebar: () => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

interface SidebarProviderProps {
  children: ReactNode;
}

export function SidebarProvider({ children }: SidebarProviderProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const toggleCollapse = () => {
    setIsCollapsed(prev => !prev);
  };

  const collapseOnDesignerClick = () => {
    setIsCollapsed(true);
  };

  const toggleMobileSidebar = () => {
    setIsMobileOpen(prev => !prev);
  };

  const contextValue = {
    isCollapsed,
    setIsCollapsed,
    toggleCollapse,
    collapseOnDesignerClick,
    isMobileOpen,
    setIsMobileOpen,
    toggleMobileSidebar
  };

  return (
    <SidebarContext.Provider value={contextValue}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);

  if (context === undefined) {
    console.warn('useSidebar called outside of SidebarProvider context, using fallback values');
    // Return fallback values that won't interfere with actual functionality
    return {
      isCollapsed: false,
      setIsCollapsed: () => {},
      toggleCollapse: () => {},
      collapseOnDesignerClick: () => {},
      isMobileOpen: false,
      setIsMobileOpen: () => {},
      toggleMobileSidebar: () => {},
    };
  }

  return context;
}