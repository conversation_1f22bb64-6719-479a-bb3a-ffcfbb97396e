API Preview: Has multiple records: true Duration: 5
Console.js:61 API Preview: Initializing multimedia elements with API data
Console.js:61 API Preview: Setting element 758e2397-fdb1-4c00-a7ad-1de1adf3d8b4 to index 0
Console.js:61 Updating multimedia element 758e2397-fdb1-4c00-a7ad-1de1adf3d8b4 to data index 0
Console.js:61 API Preview: Data is an array with 23 items
Console.js:61 Updated preview elements: 
[{…}]
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: J<PERSON><PERSON><PERSON>iiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 API data preview setup - Has multiple records: true Duration: 5
Console.js:61 No API elements found on canvas
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
chunk-276SZO74.js?v=4236f1a5:1861 The specified value "transparent" does not conform to the required format.  The format is "#rrggbb" where rr, gg, bb are two-digit hexadecimal numbers.
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii