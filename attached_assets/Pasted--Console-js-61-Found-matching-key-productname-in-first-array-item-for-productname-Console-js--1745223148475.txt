﻿
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: <PERSON><PERSON><PERSON><PERSON>iiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Clearing API cycling interval with ID: 2076
Console.js:61 Cleared API data cycling interval
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Preview active: Found 0 video elements to play
Console.js:61 API data preview setup - Has multiple records: true Duration: 3
Console.js:61 Element 093513eb-0b83-42c8-9f66-9d02e5d69fcc has API mapping: true 
{productname: true}
Console.js:61 Element 093513eb-0b83-42c8-9f66-9d02e5d69fcc has data field: true productname
Console.js:61 Found 1 API elements for data cycling
Console.js:61 API data array has 23 records for cycling
Console.js:61 API elements with videos: No
Console.js:61 Setting up API data cycling interval with 3s duration
Console.js:61 Setting up interval to cycle API data every 3 seconds
Console.js:61 API data cycling interval set with ID: 2112
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 Clearing API cycling interval with ID: 2112
Console.js:61 Cleared API data cycling interval
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=0) for element 093513eb-0b83-42c8-9f66-9d02e5d69fcc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear
Console.js:61 Forcing initial API data cycle
Console.js:61 Cycling to next API data record - START
Console.js:61 WARNING: API cycling interval appears to be cleared
Console.js:61 Found 1 API elements to update during cycling
Console.js:61 API data cycling from index 0 to 1 (out of 23 records)
Console.js:61 Forcing deep canvas re-render with updated API data index: 1
Console.js:61 Updating API element 093513eb-0b83-42c8-9f66-9d02e5d69fcc to data index 1
Console.js:61 Updating multimedia element 093513eb-0b83-42c8-9f66-9d02e5d69fcc to data index 1
Console.js:61 Cycling to next API data record - COMPLETE
Console.js:61 Updated preview elements: 
[]
Console.js:61 Accessing API data field: productname
Console.js:61 API Data available keys: 
(23) ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22']
Console.js:61 API data is an array of objects
Console.js:61 API data cycling: Using index 0 (local=0, global=1) for element 093513eb-0b83-42c8-9f66-9d02e5d69fcc
Console.js:61 Using API data record at index: 0
Console.js:61 Found matching key "productname" in first array item for "productname"
Console.js:61 Field data found: Jarritos Tutti Fruttiiiiii
Console.js:61 Cleaning up all preview event listeners and timers
Console.js:61 No API cycling interval found to clear