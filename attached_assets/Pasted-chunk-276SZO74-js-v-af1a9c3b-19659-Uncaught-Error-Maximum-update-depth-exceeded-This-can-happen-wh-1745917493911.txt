chunk-276SZO74.js?v=af1a9c3b:19659 Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
    at checkForNestedUpdates (chunk-276SZO74.js?v=af1a9c3b:19659:19)
    at scheduleUpdateOnFiber (chunk-276SZO74.js?v=af1a9c3b:18533:11)
    at dispatchSetState (chunk-276SZO74.js?v=af1a9c3b:12403:15)
    at @radix-ui_react-chec…js?v=af1a9c3b:58:66
    at setRef (chunk-D3CTYCI6.js?v=af1a9c3b:18:5)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:42
    at Array.forEach (<anonymous>)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:25
    at safelyDetachRef (chunk-276SZO74.js?v=af1a9c3b:16726:30)
    at commitMutationEffectsOnFiber (chunk-276SZO74.js?v=af1a9c3b:17741:19)

Console.js:61 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
Console.js:61 The above error occurred in the <button> component:

    at button
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-ICA7ZJMY.js?v=af1a9c3b:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…kspace/node_modules/.vite/deps/@radix-ui_react-checkbox.js?v=af1a9c3b:46:7
    at _c (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/checkbox.tsx:22:11)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Presence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-IYFCVA3S.js?v=af1a9c3b:24:11)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:175:13
    at _c5 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-ICA7ZJMY.js?v=af1a9c3b:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:53:7
    at form
    at FormProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…nner/workspace/node_modules/.vite/deps/chunk-BW5TH624.js?v=af1a9c3b:102:11)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at CampaignDetailsPage (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/campaign-details.tsx?t=1745917163181:89:18)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745917163181&v=t2a6U54cipYaWffSx4ynM:32:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=af1a9c3b:2805:3)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-276SZO74.js?v=af1a9c3b:19659 Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
    at checkForNestedUpdates (chunk-276SZO74.js?v=af1a9c3b:19659:19)
    at scheduleUpdateOnFiber (chunk-276SZO74.js?v=af1a9c3b:18533:11)
    at dispatchSetState (chunk-276SZO74.js?v=af1a9c3b:12403:15)
    at @radix-ui_react-chec…js?v=af1a9c3b:58:66
    at setRef (chunk-D3CTYCI6.js?v=af1a9c3b:18:5)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:42
    at Array.forEach (<anonymous>)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:25
    at safelyDetachRef (chunk-276SZO74.js?v=af1a9c3b:16726:30)
    at commitMutationEffectsOnFiber (chunk-276SZO74.js?v=af1a9c3b:17741:19)
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'ambient-light-sensor'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'battery'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'execution-while-not-rendered'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'execution-while-out-of-viewport'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'layout-animations'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'legacy-image-formats'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'navigation-override'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'oversized-images'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'publickey-credentials'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'speaker-selection'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'unoptimized-images'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'unsized-media'.
replEnvironmentDeskt…8aaefd67773733.js:1 Unrecognized feature: 'pointer-lock'.
replEnvironmentDeskt…8aaefd67773733.js:1 Allow attribute will take precedence over 'allowfullscreen'.
replEnvironmentDeskt…8aaefd67773733.js:1 Allow attribute will take precedence over 'allowpaymentrequest'.
workspace_iframe.html:32 Error while parsing the 'sandbox' attribute: 'allow-downloads-without-user-activation' is an invalid sandbox flag.
workspace_iframe.html:32 Unrecognized feature: 'ambient-light-sensor'.
workspace_iframe.html:32 Unrecognized feature: 'battery'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-not-rendered'.
workspace_iframe.html:32 Unrecognized feature: 'execution-while-out-of-viewport'.
workspace_iframe.html:32 Unrecognized feature: 'layout-animations'.
workspace_iframe.html:32 Unrecognized feature: 'legacy-image-formats'.
workspace_iframe.html:32 Unrecognized feature: 'navigation-override'.
workspace_iframe.html:32 Unrecognized feature: 'oversized-images'.
workspace_iframe.html:32 Unrecognized feature: 'publickey-credentials'.
workspace_iframe.html:32 Unrecognized feature: 'speaker-selection'.
workspace_iframe.html:32 Unrecognized feature: 'unoptimized-images'.
workspace_iframe.html:32 Unrecognized feature: 'unsized-media'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowfullscreen'.
workspace_iframe.html:32 Allow attribute will take precedence over 'allowpaymentrequest'.
Console.js:61 Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.
Console.js:61 Auth state changed: INITIAL_SESSION
Console.js:61 Auth state changed: SIGNED_IN
Console.js:61 Constructed URL from query key segments: api/teams/c3a76e6d-2f37-4b41-b5bc-acc9f244b439/media original queryKey: 
(3) ['/api/teams', 'c3a76e6d-2f37-4b41-b5bc-acc9f244b439', 'media']
Console.js:61 Warning: validateDOMNesting(...): <div> cannot appear as a descendant of <p>.
    at div
    at Badge (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/badge.tsx:35:18)
    at p
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Presence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-IYFCVA3S.js?v=af1a9c3b:24:11)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:175:13
    at _c5 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-ICA7ZJMY.js?v=af1a9c3b:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:53:7
    at form
    at FormProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…nner/workspace/node_modules/.vite/deps/chunk-BW5TH624.js?v=af1a9c3b:102:11)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at CampaignDetailsPage (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/campaign-details.tsx?t=1745917163181:89:18)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745917163181&v=8JHFdN5bymHsjd9w1-RS_:32:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=af1a9c3b:2805:3)
chunk-276SZO74.js?v=af1a9c3b:19659 Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
    at checkForNestedUpdates (chunk-276SZO74.js?v=af1a9c3b:19659:19)
    at scheduleUpdateOnFiber (chunk-276SZO74.js?v=af1a9c3b:18533:11)
    at dispatchSetState (chunk-276SZO74.js?v=af1a9c3b:12403:15)
    at @radix-ui_react-chec…js?v=af1a9c3b:58:66
    at setRef (chunk-D3CTYCI6.js?v=af1a9c3b:18:5)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:42
    at Array.forEach (<anonymous>)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:25
    at safelyDetachRef (chunk-276SZO74.js?v=af1a9c3b:16726:30)
    at commitMutationEffectsOnFiber (chunk-276SZO74.js?v=af1a9c3b:17741:19)
Console.js:61 Warning: Maximum update depth exceeded. This can happen when a component calls setState inside useEffect, but useEffect either doesn't have a dependency array, or one of the dependencies changes on every render.
Console.js:61 The above error occurred in the <button> component:

    at button
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-ICA7ZJMY.js?v=af1a9c3b:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…kspace/node_modules/.vite/deps/@radix-ui_react-checkbox.js?v=af1a9c3b:46:7
    at _c (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/checkbox.tsx:22:11)
    at div
    at div
    at div
    at div
    at div
    at div
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Presence (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-IYFCVA3S.js?v=af1a9c3b:24:11)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…orkspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:175:13
    at _c5 (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/ui/tabs.tsx:72:12)
    at div
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-QCAFA6Y2.js?v=af1a9c3b:41:13
    at Provider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…unner/workspace/node_modules/.vite/deps/chunk-ICA7ZJMY.js?v=af1a9c3b:38:15)
    at https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/workspace/node_modules/.vite/deps/@radix-ui_react-tabs.js?v=af1a9c3b:53:7
    at form
    at FormProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…nner/workspace/node_modules/.vite/deps/chunk-BW5TH624.js?v=af1a9c3b:102:11)
    at div
    at main
    at div
    at div
    at MainLayout (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/components/layout/main-layout.tsx:19:30)
    at CampaignDetailsPage (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/pages/campaign-details.tsx?t=1745917163181:89:18)
    at AuthenticatedRoute (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/App.tsx?t=1745917163181&v=8JHFdN5bymHsjd9w1-RS_:32:31)
    at Route (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:191:16)
    at Switch (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=af1a9c3b:247:17)
    at App
    at SidebarProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/contexts/sidebar-context.tsx:20:35)
    at AuthProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.dev/src/hooks/use-auth.tsx:37:32)
    at QueryClientProvider (https://29810b14-7e88-452d-9d46-13ec035240c9-00-pnfvpf0v5z02.worf.replit.de…rkspace/node_modules/.vite/deps/@tanstack_react-query.js?v=af1a9c3b:2805:3)

Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://reactjs.org/link/error-boundaries to learn more about error boundaries.
chunk-276SZO74.js?v=af1a9c3b:19659 Uncaught Error: Maximum update depth exceeded. This can happen when a component repeatedly calls setState inside componentWillUpdate or componentDidUpdate. React limits the number of nested updates to prevent infinite loops.
    at checkForNestedUpdates (chunk-276SZO74.js?v=af1a9c3b:19659:19)
    at scheduleUpdateOnFiber (chunk-276SZO74.js?v=af1a9c3b:18533:11)
    at dispatchSetState (chunk-276SZO74.js?v=af1a9c3b:12403:15)
    at @radix-ui_react-chec…js?v=af1a9c3b:58:66
    at setRef (chunk-D3CTYCI6.js?v=af1a9c3b:18:5)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:42
    at Array.forEach (<anonymous>)
    at chunk-D3CTYCI6.js?v=af1a9c3b:24:25
    at safelyDetachRef (chunk-276SZO74.js?v=af1a9c3b:16726:30)
    at commitMutationEffectsOnFiber (chunk-276SZO74.js?v=af1a9c3b:17741:19)