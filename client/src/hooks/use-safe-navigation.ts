import { useCallback } from 'react';
import { useLocation } from 'wouter';
import { createCleanUrl } from '@/lib/path-utils';

/**
 * A custom hook that provides safe navigation functions
 * to avoid Next.js router invalid href errors in Replit
 */
export function useSafeNavigation() {
  const [_, navigate] = useLocation();
  
  /**
   * Navigate to a path safely without double slashes
   * @param to The path to navigate to
   */
  const navigateTo = useCallback((to: string) => {
    // Clean the URL to prevent double slashes
    const cleanPath = to.replace(/^\/+/, '');
    navigate(cleanPath);
  }, [navigate]);
  
  /**
   * Create a safe URL for links without double slashes
   * @param to The path to create a URL for
   */
  const createSafeUrl = useCallback((to: string) => {
    return createCleanUrl('', to);
  }, []);
  
  return {
    navigateTo,
    createSafeUrl
  };
}