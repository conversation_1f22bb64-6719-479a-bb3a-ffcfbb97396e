import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { MainLayout } from "@/components/layout/main-layout";
import { ScreenCard } from "@/components/screens/screen-card";
import { ScreenCampaignsModal } from "@/components/screens/screen-campaigns-modal";
import { ScreenDetailsModal } from "@/components/screens/screen-details-modal";
import { useScreens } from "@/hooks/use-screens";
import { useAuth } from "@/hooks/use-auth";
import { TagInput } from "@/components/media/tag-input";
import { generateRandomCode } from "@/lib/utils";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  MonitorUp,
  Search,
  SlidersHorizontal,
  CheckCircle2,
  XCircle,
  Monitor,
  RefreshCw
} from "lucide-react";

const screenSchema = z.object({
  name: z.string().min(1, "Name is required"),
  location: z.string().optional(),
  updateFrequency: z.string().regex(/^\d{2}:\d{2}:\d{2}$/, "Format must be hh:mm:ss"),
  startTime: z.string().regex(/^\d{2}:\d{2}$/, "Format must be hh:mm"),
  endTime: z.string().regex(/^\d{2}:\d{2}$/, "Format must be hh:mm"),
  siteEmail: z.string().email("Please enter a valid email").optional().or(z.literal("")),
});

type ScreenFormValues = z.infer<typeof screenSchema>;

export default function Screens() {
  const { user } = useAuth();
  const [teamId, setTeamId] = useState<string>("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isConfigureModalOpen, setIsConfigureModalOpen] = useState(false);
  const [isCampaignsModalOpen, setIsCampaignsModalOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedScreenId, setSelectedScreenId] = useState<string | null>(null);
  const [selectedScreenName, setSelectedScreenName] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<"all" | "online" | "offline">("all");
  const [showFilters, setShowFilters] = useState(false);
  const [screenTags, setScreenTags] = useState<string[]>([]);
  const [sortOrder, setSortOrder] = useState<string>("nameAZ");
  const [isSaving, setIsSaving] = useState(false);

  // Get the user's teams
  const { data: teams } = useQuery<any[]>({
    queryKey: [`/api/profiles/${user?.id}/teams`],
    enabled: !!user?.id,
  });

  // Get all available tags for the current team
  const { data: availableTags } = useQuery<any[]>({
    queryKey: ['/api/teams', teamId, 'tags'],
    enabled: !!teamId,
  });

  useEffect(() => {
    if (teams && teams.length > 0) {
      setTeamId(teams[0].id);
    }
  }, [teams]);

  const {
    screens,
    isLoading,
    isEmpty,
    createScreen,
    updateScreen,
    deleteScreen,
    useScreen,
    refetchScreens,
    isCreating,
    isUpdating,
    isDeleting
  } = useScreens(teamId);

  const { data: selectedScreen } = useScreen(selectedScreenId);

  const form = useForm<ScreenFormValues>({
    resolver: zodResolver(screenSchema),
    defaultValues: {
      name: "",
      location: "",
      updateFrequency: "00:05:00",
      startTime: "08:00",
      endTime: "20:00",
      siteEmail: "",
    },
  });

  // Helper function to convert time from "hh:mm:ss" to "hh:mm"
  const formatTimeForDisplay = (time: string | null): string => {
    if (!time) return "";
    // Extract hours and minutes only (hh:mm)
    const match = time.match(/^(\d{2}):(\d{2})/);
    if (match) {
      return `${match[1]}:${match[2]}`;
    }
    return time;
  };

  useEffect(() => {
    if (selectedScreen && isConfigureModalOpen) {
      console.log("Loading screen data:", selectedScreen);

      // Convert time formats from "hh:mm:ss" to "hh:mm" for display
      const formValues = {
        name: selectedScreen.name,
        location: selectedScreen.location || "",
        updateFrequency: selectedScreen.updateFrequency || "00:05:00",
        startTime: formatTimeForDisplay(selectedScreen.startTime) || "08:00",
        endTime: formatTimeForDisplay(selectedScreen.endTime) || "20:00",
        siteEmail: selectedScreen.siteEmail || "",
      };

      console.log("Form values to load:", formValues);
      form.reset(formValues);

      // Set the tags if available
      if (selectedScreen.tags && Array.isArray(selectedScreen.tags)) {
        console.log("Loading tags:", selectedScreen.tags);
        setScreenTags(selectedScreen.tags);
      } else {
        console.log("No tags found for screen");
        setScreenTags([]);
      }
    } else if (!isConfigureModalOpen) {
      form.reset({
        name: "",
        location: "",
        updateFrequency: "00:05:00",
        startTime: "08:00",
        endTime: "20:00",
        siteEmail: "",
      });
      setScreenTags([]);
    }
  }, [selectedScreen, isConfigureModalOpen, form]);

  // Filter screens
  const filteredScreens = screens?.filter(screen => {
    // Filter by search query
    const matchesSearch = !searchQuery ||
      screen.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (screen.location && screen.location.toLowerCase().includes(searchQuery.toLowerCase())) ||
      screen.code.includes(searchQuery);

    // Filter by status
    const matchesStatus = statusFilter === "all" ||
      (statusFilter === "online" && screen.status === "online") ||
      (statusFilter === "offline" && screen.status === "offline");

    // Filter by tags (using OR logic - show if screen has ANY of the selected tags)
    const matchesTags = selectedTags.length === 0 ||
      (screen.tags && selectedTags.some(tag => screen.tags.includes(tag)));

    return matchesSearch && matchesStatus && matchesTags;
  });

  // Sort screens
  const sortedScreens = [...(filteredScreens || [])].sort((a, b) => {
    switch (sortOrder) {
      case "nameAZ":
        return a.name.localeCompare(b.name);
      case "nameZA":
        return b.name.localeCompare(a.name);
      case "lastPingOldest":
        // Parse dates, ensuring they are compared in the same timezone context
        const dateA = a.lastSeen ? new Date(a.lastSeen).getTime() : 0;
        const dateB = b.lastSeen ? new Date(b.lastSeen).getTime() : 0;
        return dateA - dateB;
      case "lastPingNewest":
        // Parse dates, ensuring they are compared in the same timezone context
        const dateA2 = a.lastSeen ? new Date(a.lastSeen).getTime() : 0;
        const dateB2 = b.lastSeen ? new Date(b.lastSeen).getTime() : 0;
        return dateB2 - dateA2;
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Helper function to ensure startTime and endTime have the correct format when saving
  const formatTimeForSaving = (time: string): string => {
    // If time is already in "hh:mm:ss" format, return it as is
    if (time.match(/^\d{2}:\d{2}:\d{2}$/)) {
      return time;
    }
    // If time is in "hh:mm" format, append ":00" for seconds
    if (time.match(/^\d{2}:\d{2}$/)) {
      return `${time}:00`;
    }
    return time;
  };

  const handleAddScreen = (data: ScreenFormValues) => {
    // Prepare the screen data with the correct time format and tags
    const screenData = {
      ...data,
      // Ensure time is correctly formatted when sending to the server
      startTime: formatTimeForSaving(data.startTime),
      endTime: formatTimeForSaving(data.endTime),
      teamId,
      tags: screenTags,
      status: "offline"
    };

    // Create the screen
    createScreen(screenData);
    setIsAddModalOpen(false);
    form.reset();
    setScreenTags([]);
  };

  const handleConfigureScreen = async (data: ScreenFormValues) => {
    if (selectedScreenId) {
      // Show saving state
      setIsSaving(true);

      try {
        // Generate a new GUID for updateguid
        const newGuid = crypto.randomUUID(); // Ensure you have crypto available

        // Prepare the screen data with the correct time format and tags
        const screenData = {
          ...data,
          // Ensure time is correctly formatted when sending to the server
          startTime: formatTimeForSaving(data.startTime),
          endTime: formatTimeForSaving(data.endTime),
          updateguid: newGuid, // Include the new GUID here
          tags: screenTags
        };

        // Update the screen
        await new Promise(resolve => {
          updateScreen({
            id: selectedScreenId,
            data: screenData
          });

          // Wait a short time to ensure the toast has a chance to appear
          setTimeout(resolve, 1500);
        });

        // Close the modal after successful update
        setIsConfigureModalOpen(false);
      } catch (error) {
        console.error("Error updating screen:", error);
      } finally {
        setIsSaving(false);
      }
    }
  };

  // Force refresh screen data when opening the modal
  const refreshAndOpenModal = async (id: string) => {
    // Reset form and tags first to avoid showing stale data
    form.reset({
      name: "",
      location: "",
      updateFrequency: "00:05:00",
      startTime: "08:00",
      endTime: "20:00",
      siteEmail: "",
    });
    setScreenTags([]);

    // Set the ID first
    setSelectedScreenId(id);

    // Force a delay to ensure we get fresh data
    await new Promise(resolve => setTimeout(resolve, 200));

    // Now open the modal - by this time, the useEffect should have re-fetched the data
    setIsConfigureModalOpen(true);
  };

  const handleEditScreen = async (id: string) => {
    // Find the screen to get its name
    const screen = screens?.find(s => s.id === id);

    if (screen) {
      setSelectedScreenId(id);
      setSelectedScreenName(screen.name);
      setIsCampaignsModalOpen(true);
    }
  };

  const handleConfigureScreenOpen = async (id: string) => {
    await refreshAndOpenModal(id);
  };

  const handleDeleteScreen = (id: string) => {
    deleteScreen(id);
  };

  const handleCloseCampaignsModal = () => {
    setIsCampaignsModalOpen(false);
  };

  const handleViewScreenDetails = (id: string) => {
    setSelectedScreenId(id);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedScreenId(null);
  };

  return (
    <MainLayout>
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Screens</h2>
          <p className="text-muted-foreground">Manage your digital signage displays</p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Button
            variant="outline"
            onClick={() => refetchScreens()}
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <MonitorUp className="mr-2 h-4 w-4" /> Add Screen
          </Button>
        </div>
      </div>

      {/* Filters and search */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Input
                  placeholder="Search screens..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={showFilters ? "default" : "outline"}
                size="icon"
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal className="h-4 w-4" />
              </Button>

              <div className="flex border border-input rounded-md overflow-hidden">
                <Button
                  variant={statusFilter === "all" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("all")}
                  className="px-3 h-9"
                >
                  All
                </Button>
                <Button
                  variant={statusFilter === "online" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("online")}
                  className="px-3 h-9"
                >
                  <CheckCircle2 className="h-3.5 w-3.5 mr-2 text-green-500" />
                  Online
                </Button>
                <Button
                  variant={statusFilter === "offline" ? "secondary" : "ghost"}
                  onClick={() => setStatusFilter("offline")}
                  className="px-3 h-9"
                >
                  <XCircle className="h-3.5 w-3.5 mr-2 text-gray-500" />
                  Offline
                </Button>
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium block mb-2">Sort By</label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue placeholder="Name (A-Z)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="nameAZ">Name (A-Z)</SelectItem>
                    <SelectItem value="nameZA">Name (Z-A)</SelectItem>
                    <SelectItem value="lastPingNewest">Last Ping (Newest)</SelectItem>
                    <SelectItem value="lastPingOldest">Last Ping (Oldest)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">
                  Filter by Tags
                  <span className="ml-1 text-xs text-muted-foreground font-normal">(screens with any selected tag will be shown)</span>
                </label>
                <TagInput
                  value={selectedTags}
                  onChange={setSelectedTags}
                  placeholder="Filter by tags..."
                  teamId={teamId}
                />
                {availableTags && availableTags.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {availableTags.map((tag) => (
                      <button
                        key={tag.id}
                        type="button"
                        onClick={() => {
                          // Check if tag is already selected (case-insensitive)
                          const isSelected = selectedTags.some(
                            t => t.toLowerCase() === tag.name.toLowerCase()
                          );

                          if (isSelected) {
                            setSelectedTags(selectedTags.filter(
                              t => t.toLowerCase() !== tag.name.toLowerCase()
                            ));
                          } else {
                            setSelectedTags([...selectedTags, tag.name]);
                          }
                        }}
                        className={`text-xs px-2 py-1 rounded-full ${
                          // Check if tag is already selected (case-insensitive)
                          selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted hover:bg-muted/80'
                        }`}
                        style={{
                          backgroundColor: selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? tag.color
                            : undefined,
                          color: selectedTags.some(t => t.toLowerCase() === tag.name.toLowerCase())
                            ? '#fff'
                            : undefined
                        }}
                      >
                        {tag.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Screens Content */}
      {isLoading ? (
        <div className="py-20 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary/30 border-t-primary rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading screens...</p>
        </div>
      ) : isEmpty ? (
        <div className="bg-white rounded-lg shadow p-12 text-center">
          <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No screens yet</h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            Add screens to your network to start displaying your digital signage content.
          </p>
          <Button onClick={() => setIsAddModalOpen(true)}>
            <MonitorUp className="mr-2 h-4 w-4" /> Add Screen
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedScreens?.map(screen => (
            <ScreenCard
              key={screen.id}
              id={screen.id}
              code={screen.code}
              name={screen.name}
              location={screen.location || undefined}
              lastSeen={screen.lastSeen}
              updateFrequency={screen.updateFrequency || undefined}
              tags={screen.tags}
              subscriptionStatus={screen.subscriptionStatus}
              trialEndsAt={screen.trialEndsAt}
              onEdit={handleEditScreen}
              onDelete={handleDeleteScreen}
              onConfigure={handleConfigureScreenOpen}
              onViewDetails={handleViewScreenDetails}
            />
          ))}
        </div>
      )}

      {/* Add Screen Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add New Screen</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddScreen)} className="space-y-4">
              <div className="p-4 bg-muted/50 rounded-lg flex justify-between items-center">
                <div>
                  <p className="text-sm font-medium">Screen ID Code</p>
                  <p className="text-2xl font-mono mt-1">{generateRandomCode(5)}</p>
                </div>
                <Button type="button" variant="ghost" size="sm">
                  Regenerate
                </Button>
              </div>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Screen Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Lobby Display" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Main Office" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <FormLabel>Tags</FormLabel>
                <TagInput
                  value={screenTags}
                  onChange={setScreenTags}
                  placeholder="Add tag..."
                  className="mt-1"
                  teamId={teamId}
                />
              </div>

              <DialogFooter>
                <Button variant="outline" type="button" onClick={() => setIsAddModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Screen</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Configure Screen Modal */}
      <Dialog open={isConfigureModalOpen} onOpenChange={setIsConfigureModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Configure Screen</DialogTitle>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleConfigureScreen)} className="space-y-4">
              <div className="p-4 bg-muted/50 rounded-lg">
                <p className="text-sm font-medium">Screen ID Code</p>
                <p className="text-2xl font-mono mt-1">{selectedScreen?.code}</p>
              </div>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Screen Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location (Optional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Time</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="endTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Time</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="updateFrequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Update Frequency (hh:mm:ss)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="siteEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Site Contact Email (Optional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div>
                <FormLabel>Tags</FormLabel>
                <TagInput
                  value={screenTags}
                  onChange={setScreenTags}
                  placeholder="Add tag..."
                  teamId={teamId}
                  className="mt-1"
                />
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsConfigureModalOpen(false)}
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-solid border-current border-r-transparent"></span>
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Screen Campaigns Modal */}
      <ScreenCampaignsModal
        isOpen={isCampaignsModalOpen}
        onClose={handleCloseCampaignsModal}
        screenId={selectedScreenId || ""}
        screenName={selectedScreenName}
        teamId={teamId}
      />

      {/* Screen Details Modal */}
      <ScreenDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        screenId={selectedScreenId}
        teamId={teamId}
      />
    </MainLayout>
  );
}
