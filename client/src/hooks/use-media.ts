import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase, getMediaStoragePath } from "@/lib/supabase";
import { MediaItem, Tag } from "@/types/media";

export interface MediaItemWithTags extends MediaItem {
  tags: string[];
}

export function useMedia(teamId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [selectedMedia, setSelectedMedia] = useState<string | null>(null);

  // Single optimized query that fetches media with tags in one request using server-side join
  const mediaWithTagsQuery = useQuery<MediaItemWithTags[]>({
    queryKey: [`/api/teams/${teamId}/media-with-tags`],
    queryFn: async () => {
      if (!teamId) return [];

      // Use the new optimized endpoint that fetches media with tags in a single SQL query
      const response = await apiRequest('GET', `/api/teams/${teamId}/media-with-tags`);
      const mediaWithTags = await response.json();

      if (!Array.isArray(mediaWithTags)) {
        return [];
      }

      return mediaWithTags;
    },
    enabled: !!teamId && teamId.trim() !== '',
    staleTime: 0, // Always fresh for real-time updates
    refetchOnWindowFocus: false,
  });

  // Simplified state management - use query data directly
  const mediaItems = mediaWithTagsQuery.data || [];
  const isLoading = mediaWithTagsQuery.isLoading;
  const error = mediaWithTagsQuery.error;
  const isEmpty = !isLoading && mediaItems.length === 0;

  // Get a single media item with tags
  const useMediaItem = (mediaId: string | null) => {
    // Get media item data
    const mediaItemQuery = useQuery<MediaItem>({
      queryKey: [`/api/media/${mediaId}`],
      enabled: !!mediaId,
      staleTime: 0,
      refetchOnWindowFocus: false
    });

    // Get tags for the media item
    const tagsQuery = useQuery<any[]>({
      queryKey: [`/api/media/${mediaId}/tags`],
      enabled: !!mediaId,
      staleTime: 0, // Always fresh
      refetchOnWindowFocus: false,
    });

    // Combine data
    const mediaItemWithTags = mediaItemQuery.data && mediaId ? {
      ...mediaItemQuery.data,
      tags: Array.isArray(tagsQuery.data)
        ? tagsQuery.data.map((tag: any) => tag.name || '')
        : []
    } : null;

    return {
      data: mediaItemWithTags,
      isLoading: mediaItemQuery.isLoading || tagsQuery.isLoading,
      error: mediaItemQuery.error || tagsQuery.error,
    };
  };

  // Delete a media item
  const deleteMediaMutation = useMutation({
    mutationFn: async (mediaId: string) => {
      const mediaItem = await queryClient.fetchQuery<MediaItem>({
        queryKey: ['/api/media', mediaId]
      });

      if (mediaItem) {
        try {
          // First, delete from storage
          // Extract the storage paths from both fileUrl and thumbnailUrl (if different)
          // URL format example: https://sample.supabase.co/storage/v1/object/public/medialibrary/teamId/filename.ext
          const fileUrl = mediaItem.fileUrl;
          const thumbnailUrl = mediaItem.thumbnailUrl;

          console.log("Deleting file with URL:", fileUrl);
          console.log("Checking for thumbnail URL:", thumbnailUrl);

          // Paths to delete
          const pathsToDelete: string[] = [];

          // Extract the path after the bucket name for the main file
          const filePathMatch = fileUrl.match(/\/medialibrary\/([^?]+)/);
          if (filePathMatch && filePathMatch[1]) {
            // Get the encoded path from the URL
            const encodedPath = filePathMatch[1];
            // Decode the URL-encoded path to get the proper path for deletion
            const mainFilePath = decodeURIComponent(encodedPath);
            console.log("Extracted file path (encoded):", encodedPath);
            console.log("Decoded file path for deletion:", mainFilePath);
            pathsToDelete.push(mainFilePath);
          } else {
            console.error("Could not extract storage path from URL:", fileUrl);
          }

          // If thumbnail URL exists and is different from the main file URL
          if (thumbnailUrl && thumbnailUrl !== fileUrl) {
            const thumbnailPathMatch = thumbnailUrl.match(/\/medialibrary\/([^?]+)/);
            if (thumbnailPathMatch && thumbnailPathMatch[1]) {
              // Get the encoded path from the URL
              const encodedThumbnailPath = thumbnailPathMatch[1];
              // Decode the URL-encoded path to get the proper path for deletion
              const thumbnailPath = decodeURIComponent(encodedThumbnailPath);
              console.log("Extracted thumbnail path (encoded):", encodedThumbnailPath);
              console.log("Decoded thumbnail path for deletion:", thumbnailPath);
              // Only add if it's not already in the array (could be the same in some cases)
              if (!pathsToDelete.includes(thumbnailPath)) {
                pathsToDelete.push(thumbnailPath);
              }
            } else {
              console.error("Could not extract thumbnail path from URL:", thumbnailUrl);
            }
          }

          // Remove files from Supabase storage if we have paths to delete
          if (pathsToDelete.length > 0) {
            console.log("Attempting to delete the following paths:", pathsToDelete);
            const { error: removeError, data: removeData } = await supabase.storage
              .from('medialibrary')
              .remove(pathsToDelete);

            if (removeError) {
              console.error("Error removing files from storage:", removeError);
            } else {
              console.log("Files successfully removed from storage:", removeData);
            }
          } else {
            console.error("No valid storage paths extracted for deletion");
          }
        } catch (error) {
          console.error("Error deleting media from storage:", error);
        }

        // Then delete from database (do this even if storage deletion fails)
        await apiRequest('DELETE', `/api/media/${mediaId}`);
      }
    },
    onSuccess: () => {
      // Invalidate media queries to refresh data
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/media-with-tags`] });
      toast({
        title: "Media deleted",
        description: "The media has been deleted successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting media",
        description: error.message || "An error occurred while deleting the media",
        variant: "destructive"
      });
    },
  });

  // Update a media item
  const updateMediaMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<MediaItem> }) => {
      return apiRequest('PATCH', `/api/media/${id}`, data);
    },
    onSuccess: () => {
      // Invalidate media queries to refresh data
      queryClient.invalidateQueries({ queryKey: [`/api/teams/${teamId}/media-with-tags`] });
      toast({
        title: "Media updated",
        description: "The media has been updated successfully"
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating media",
        description: error.message || "An error occurred while updating the media",
        variant: "destructive"
      });
    },
  });

  return {
    mediaItems,
    isLoading,
    isEmpty,
    error,
    selectedMedia,
    setSelectedMedia,
    useMediaItem,
    deleteMedia: deleteMediaMutation.mutate,
    isDeleting: deleteMediaMutation.isPending,
    updateMedia: updateMediaMutation.mutate,
    isUpdating: updateMediaMutation.isPending,
    refetch: mediaWithTagsQuery.refetch,
  };
}
