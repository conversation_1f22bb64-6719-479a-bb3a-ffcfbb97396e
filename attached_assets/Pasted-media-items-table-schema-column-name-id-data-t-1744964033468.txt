media_items table schema;
--------------------------
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "file_url",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "thumbnail_url",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "file_type",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "file_size",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "width",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "height",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "duration",
    "data_type": "integer",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

tags table schema;
------------------
[
  {
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "team_id",
    "data_type": "uuid",
    "is_nullable": "YES"
  },
  {
    "column_name": "name",
    "data_type": "text",
    "is_nullable": "NO"
  },
  {
    "column_name": "color",
    "data_type": "text",
    "is_nullable": "YES"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  },
  {
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]

- media_tags table schema;
---------------------------
[
  {
    "column_name": "media_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "tag_id",
    "data_type": "uuid",
    "is_nullable": "NO"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES"
  }
]