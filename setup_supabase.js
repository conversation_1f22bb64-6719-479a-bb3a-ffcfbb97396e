// This script sets up the necessary tables and initial data in Supabase
import { createClient } from '@supabase/supabase-js';

// Get environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: Supabase credentials not found in environment variables.');
  console.error('Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupTables() {
  console.log('Setting up database tables...');
  
  try {
    // Create a test user
    console.log('Creating test user...');
    const { data: userData, error: userError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'password123',
      options: {
        data: {
          first_name: 'Demo',
          last_name: 'User'
        }
      }
    });
    
    if (userError) {
      console.error('Error creating user:', userError.message);
    } else {
      console.log('User created or exists:', userData.user.id);
      
      // Create or update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userData.user.id,
          email: userData.user.email,
          first_name: 'Demo',
          last_name: 'User',
          created_at: new Date()
        });
      
      if (profileError) {
        console.error('Error creating profile:', profileError.message);
      } else {
        console.log('Profile created successfully');
        
        // Create team for the test user
        const { data: teamData, error: teamError } = await supabase
          .from('teams')
          .insert([
            { 
              name: 'Demo Company',
              created_at: new Date()
            }
          ])
          .select();
        
        if (teamError) {
          console.error('Error creating team:', teamError.message);
        } else {
          console.log('Team created successfully:', teamData[0].id);
          
          // Add user to team
          const { error: memberError } = await supabase
            .from('team_members')
            .insert([
              {
                profile_id: userData.user.id,
                team_id: teamData[0].id,
                role: 'owner',
                created_at: new Date()
              }
            ]);
          
          if (memberError) {
            console.error('Error adding user to team:', memberError.message);
          } else {
            console.log('User added to team successfully');
          }
        }
      }
    }
    
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error during setup:', error.message);
  }
}

// Run the setup
setupTables().catch(console.error);