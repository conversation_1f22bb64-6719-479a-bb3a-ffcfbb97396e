import React from 'react';
import { format } from 'date-fns';
import { 
  Clock, 
  Maximize2, 
  Edit2, 
  Trash2,
  Tag,
  Calendar
} from 'lucide-react';
import { formatFileSize, formatDuration } from '@/lib/utils';
import { MediaThumbnail } from './MediaThumbnail';

import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

export interface MediaCardProps {
  id: string;
  name: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  thumbnailUrl?: string;
  width?: number;
  height?: number;
  duration?: number;
  createdAt: Date;
  updatedAt: Date;
  tags?: string[];
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onTagClick?: (tag: string) => void;
}

export function MediaCard({
  id,
  name,
  fileUrl,
  fileType,
  fileSize,
  thumbnailUrl,
  width,
  height,
  duration,
  createdAt,
  updatedAt,
  tags = [],
  onView,
  onEdit,
  onDelete,
  onTagClick
}: MediaCardProps) {
  // Handlers
  const handleView = () => onView?.(id);
  const handleEdit = () => onEdit?.(id);
  const handleDelete = () => onDelete?.(id);
  const handleTagClick = (tag: string) => onTagClick?.(tag);
  
  // Determine if it's an image, video, or audio
  const isImage = fileType.startsWith('image/');
  const isVideo = fileType.startsWith('video/');
  const isAudio = fileType.startsWith('audio/');
  
  // Format file dimensions for display
  const dimensionsText = width && height 
    ? `${width} × ${height}px` 
    : '';
  
  // Format creation date
  const createdAtFormatted = format(createdAt, 'MMM d, yyyy');
  
  return (
    <Card className="overflow-hidden flex flex-col h-full">
      <div className="p-2 pt-1">
        <MediaThumbnail
          fileUrl={fileUrl}
          fileType={fileType}
          thumbnailUrl={thumbnailUrl}
          alt={name}
          width={300}
          height={180}
          onClick={handleView}
          className="w-full"
        />
      </div>
      
      <CardHeader className="p-4 pb-2">
        <div className="flex items-start justify-between">
          <CardTitle className="text-base font-medium truncate" title={name}>
            {name}
          </CardTitle>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <circle cx="12" cy="12" r="1" />
                  <circle cx="19" cy="12" r="1" />
                  <circle cx="5" cy="12" r="1" />
                </svg>
                <span className="sr-only">Menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleView}>
                <Maximize2 className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleEdit}>
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-0 pb-2 text-sm text-muted-foreground space-y-2">
        <div className="flex flex-wrap gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center mr-3">
                  <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground/70" />
                  <span>{createdAtFormatted}</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p>Created: {format(createdAt, 'PPpp')}</p>
                {updatedAt && updatedAt > createdAt && (
                  <p>Updated: {format(updatedAt, 'PPpp')}</p>
                )}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <div className="flex items-center">
            <Clock className="h-3.5 w-3.5 mr-1 text-muted-foreground/70" />
            <span>{formatFileSize(fileSize)}</span>
          </div>
          
          {duration && (isVideo || isAudio) && (
            <div className="flex items-center ml-3">
              <span>{formatDuration(duration)}</span>
            </div>
          )}
        </div>
        
        {dimensionsText && isImage && (
          <div className="text-xs text-muted-foreground/70">
            {dimensionsText}
          </div>
        )}
      </CardContent>
      
      {tags.length > 0 && (
        <CardFooter className="p-4 pt-2 flex flex-wrap gap-2 mt-auto">
          {tags.map(tag => (
            <Badge 
              key={tag} 
              variant="secondary" 
              className="cursor-pointer"
              onClick={() => handleTagClick(tag)}
            >
              <Tag className="h-3 w-3 mr-1" />
              {tag}
            </Badge>
          ))}
        </CardFooter>
      )}
    </Card>
  );
}