import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { formatBytes, formatDuration, formatDate } from "@/lib/utils";
import { Eye, MoreVertical, Play, Pencil, Trash2, Download, Video } from "lucide-react";

interface MediaCardProps {
  id: string;
  name: string;
  type: string;  // Accepts either 'fileType' or 'file_type' from the API
  fileUrl: string;
  thumbnailUrl?: string;
  fileSize: number;
  width?: number;
  height?: number;
  duration?: number;
  uploadedDate: Date | string;
  tags: string[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onView: (url: string, type: string) => void;
}

export function MediaCard({
  id,
  name,
  type,
  fileUrl,
  thumbnailUrl,
  fileSize,
  width,
  height,
  duration,
  uploadedDate,
  tags,
  onEdit,
  onDelete,
  onView,
}: MediaCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isTouched, setIsTouched] = useState(false);

  // Add null checks to prevent errors with undefined file_type
  const isImage = type && type.startsWith("image/");
  const isVideo = type && type.startsWith("video/");

  return (
    <Card className="overflow-hidden">
      <div
        className="relative h-48 bg-muted"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onTouchStart={() => setIsTouched(true)}
        onTouchEnd={() => setTimeout(() => setIsTouched(false), 3000)} // Hide after 3 seconds on mobile
      >
        {isImage && thumbnailUrl ? (
          <img
            src={thumbnailUrl}
            alt={name}
            className="w-full h-full object-cover"
          />
        ) : isVideo ? (
          <div className="w-full h-full flex items-center justify-center bg-muted/50">
            <Video className="h-12 w-12 text-muted-foreground" />
            {thumbnailUrl && (
              <img
                src={thumbnailUrl}
                alt={name}
                className="absolute inset-0 w-full h-full object-cover"
              />
            )}
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-muted/50">
            <div className="text-xl font-medium text-muted-foreground">
              {name.split('.').pop()?.toUpperCase()}
            </div>
          </div>
        )}

        {/* Overlay buttons */}
        <div
          className={`absolute top-2 right-2 flex space-x-1 transition-opacity duration-200 ${
            isHovered || isTouched ? 'opacity-100' : 'opacity-100 sm:opacity-0'
          }`}
        >
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8 bg-white/70 backdrop-blur-sm rounded text-gray-700 hover:bg-white"
            onClick={() => onView(fileUrl, type || 'unknown/type')}
          >
            {isVideo ? <Play className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="secondary"
                size="icon"
                className="h-8 w-8 bg-white/70 backdrop-blur-sm rounded text-gray-700 hover:bg-white"
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Media Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onView(fileUrl, type || 'unknown/type')}>
                <Eye className="mr-2 h-4 w-4" /> View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEdit(id)}>
                <Pencil className="mr-2 h-4 w-4" /> Edit
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <a href={fileUrl} download={name}>
                  <Download className="mr-2 h-4 w-4" /> Download
                </a>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => onDelete(id)}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Resolution or duration badge */}
        {(width && height) || duration ? (
          <div className="absolute bottom-2 left-2 flex space-x-1">
            {duration && (
              <Badge variant="outline" className="bg-black/70 backdrop-blur-sm text-white border-0">
                {formatDuration(duration)}
              </Badge>
            )}
            {width && height && (
              <Badge variant="outline" className="bg-black/70 backdrop-blur-sm text-white border-0">
                {width} × {height}
              </Badge>
            )}
          </div>
        ) : null}
      </div>

      <CardContent className="p-4">
        <h4 className="font-medium truncate" title={name}>{name}</h4>
        <div className="flex items-center justify-between mt-2 text-sm text-muted-foreground">
          <span>
            {isImage ? "Image" : isVideo ? "Video" : "File"} • {formatBytes(fileSize)}
          </span>
          <span>{formatDate(uploadedDate)}</span>
        </div>

        {tags && tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
